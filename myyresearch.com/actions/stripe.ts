"use server";
import <PERSON><PERSON> from "stripe";
import { Template, PublicTemplateResponse } from "@/types/template";

export async function createCheckoutSession({
  templates,
  quantity,
}: {
  templates: (Template | PublicTemplateResponse)[];
  quantity: number;
}) {
  if (!templates.length || !quantity) {
    return { error: "Invalid input" };
  }

  try {
    const apiKey = process.env.STRIPE_SECRET_KEY;

    if (!apiKey) {
      throw new Error("Stripe secret key is missing!");
    }

    const stripe = new Stripe(apiKey);

    const lineItems = templates.map((template) => ({
      price_data: {
        currency: "USD",
        product_data: {
          name: template.title,
          images: [
            `${process.env.NEXT_PUBLIC_BASE_URL}/${
              typeof template.thumbnail === "string"
                ? template.thumbnail
                : template.thumbnail.url
            }`,
          ],
        },
        unit_amount: template.discountPrice
          ? Math.round(template.discountPrice * 100)
          : Math.round(template.price * 100),
      },
      quantity,
    }));

    const session = await stripe.checkout.sessions.create({
      mode: "payment",
      success_url: `${process.env.NEXT_PUBLIC_BASE_URL}/payment?status=success`,
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/payment?status=failed`,
      line_items: lineItems,
    });

    console.log("Session created", session.id);
    return { sessionId: session.id };
  } catch (error) {
    console.log("Error creating checkout session", error);
    return { error: "Error creating checkout session" };
  }
}
