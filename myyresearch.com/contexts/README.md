# Cart Context Provider

A comprehensive React Context Provider for cart management that utilizes TanStack Query (React Query) for state management, caching, and data synchronization.

## Features

- ✅ **TanStack Query Integration**: Proper caching, data synchronization, and error handling
- ✅ **Cookie-based Cart ID Storage**: Persistent cart across browser sessions
- ✅ **Authentication Independent**: Works for both authenticated and unauthenticated users
- ✅ **Optimistic Updates**: Immediate UI feedback with automatic rollback on errors
- ✅ **TypeScript Support**: Full type safety throughout
- ✅ **Error Handling**: Comprehensive error handling with retry logic
- ✅ **Query Invalidation**: Automatic cache invalidation after mutations
- ✅ **Multiple Usage Patterns**: Various hooks for different use cases

## Setup

The cart provider is already integrated into the global providers in `providers/GlobalProviders.tsx`. No additional setup is required.

## Usage

### 1. Basic Cart Data Access

```tsx
import { useCartData } from "@/contexts/cart-context";

function CartSummary() {
  const { cart, totalItems, totalAmount, isEmpty, isLoading } = useCartData();

  if (isLoading) return <div>Loading cart...</div>;
  if (isEmpty) return <div>Cart is empty</div>;

  return (
    <div>
      <p>Items: {totalItems}</p>
      <p>Total: ${totalAmount.toFixed(2)}</p>
    </div>
  );
}
```

### 2. Cart Operations

```tsx
import { useCartOperations } from "@/contexts/cart-context";
import { ItemType } from "@/types/cart";

function AddToCartButton({ courseId }: { courseId: string }) {
  const { addToCart, isAddingToCart } = useCartOperations();

  const handleAddToCart = () => {
    addToCart({
      itemType: ItemType.COURSE,
      itemId: courseId,
      quantity: 1,
    });
  };

  return (
    <button 
      onClick={handleAddToCart}
      disabled={isAddingToCart}
    >
      {isAddingToCart ? "Adding..." : "Add to Cart"}
    </button>
  );
}
```

### 3. Helper Functions for Adding Items

```tsx
import { useAddToCartHelper } from "@/contexts/cart-context";

function ProductCard({ courseId, templateId }: { courseId?: string; templateId?: string }) {
  const { addCourseToCart, addTemplateToCart, isAddingToCart } = useAddToCartHelper();

  return (
    <div>
      {courseId && (
        <button 
          onClick={() => addCourseToCart(courseId, 1)}
          disabled={isAddingToCart}
        >
          Add Course to Cart
        </button>
      )}
      {templateId && (
        <button 
          onClick={() => addTemplateToCart(templateId, 1)}
          disabled={isAddingToCart}
        >
          Add Template to Cart
        </button>
      )}
    </div>
  );
}
```

### 4. Full Cart Context Access

```tsx
import { useCartContext } from "@/contexts/cart-context";

function CartManager() {
  const {
    cartId,
    cart,
    addToCart,
    removeFromCart,
    updateCartItem,
    clearCart,
    totalItems,
    totalAmount,
    refreshCart,
  } = useCartContext();

  // Access to all cart functionality
  return (
    <div>
      <p>Cart ID: {cartId}</p>
      <p>Items: {totalItems}</p>
      <button onClick={refreshCart}>Refresh</button>
      <button onClick={() => clearCart.mutate()}>Clear Cart</button>
    </div>
  );
}
```

### 5. Async Operations

```tsx
import { useCartOperations } from "@/contexts/cart-context";

function AsyncCartOperations() {
  const { addToCartAsync, removeFromCartAsync } = useCartOperations();

  const handleAsyncAdd = async () => {
    try {
      await addToCartAsync({
        itemType: ItemType.COURSE,
        itemId: "course-123",
        quantity: 1,
      });
      alert("Item added successfully!");
    } catch (error) {
      alert("Failed to add item");
    }
  };

  return <button onClick={handleAsyncAdd}>Add Item (Async)</button>;
}
```

### 6. Cart Items Management

```tsx
import { useCartData, useCartOperations } from "@/contexts/cart-context";

function CartItemsList() {
  const { cart, isLoading } = useCartData();
  const { updateCartItem, removeFromCart } = useCartOperations();

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      {cart?.items.map((item) => (
        <div key={item._id}>
          <h3>{item.title}</h3>
          <p>Quantity: {item.quantity}</p>
          <button
            onClick={() => updateCartItem({
              cartItemId: item._id,
              quantity: item.quantity + 1
            })}
          >
            Increase
          </button>
          <button
            onClick={() => updateCartItem({
              cartItemId: item._id,
              quantity: Math.max(1, item.quantity - 1)
            })}
          >
            Decrease
          </button>
          <button onClick={() => removeFromCart(item._id)}>
            Remove
          </button>
        </div>
      ))}
    </div>
  );
}
```

### 7. Cart Merging (Login Scenarios)

```tsx
import { useCartOperations } from "@/contexts/cart-context";

function LoginHandler() {
  const { mergeCart, setNewCartId } = useCartOperations();

  const handleLogin = async (userCartId: string) => {
    // Get current guest cart ID before login
    const guestCartId = cartId;
    
    // Merge guest cart with user cart
    try {
      const result = await mergeCartAsync(guestCartId);
      // Update to the merged cart ID
      setNewCartId(result.cart.cartId);
    } catch (error) {
      console.error("Failed to merge carts:", error);
    }
  };

  return <button onClick={() => handleLogin("user-cart-id")}>Login</button>;
}
```

## Available Hooks

### `useCartContext()`
Returns the full cart context with all functionality.

### `useCartData()`
Returns cart data and computed values:
- `cart`: Cart object with items
- `cartId`: Current cart ID
- `totalItems`: Total number of items
- `totalAmount`: Total cart amount
- `isEmpty`: Boolean indicating if cart is empty
- `isLoading`: Loading state
- `isError`: Error state
- `refetch`: Function to refetch cart data

### `useCartOperations()`
Returns cart operation functions and their states:
- `addToCart`, `addToCartAsync`: Add items to cart
- `removeFromCart`, `removeFromCartAsync`: Remove items from cart
- `updateCartItem`, `updateCartItemAsync`: Update item quantities
- `clearCart`, `clearCartAsync`: Clear entire cart
- `mergeCart`, `mergeCartAsync`: Merge carts
- `refreshCart`: Refresh cart data
- `setNewCartId`: Set new cart ID
- `clearCartId`: Clear current cart ID and create new one
- Loading states: `isAddingToCart`, `isRemovingFromCart`, etc.
- Error states: `addToCartError`, `removeFromCartError`, etc.

### `useAddToCartHelper()`
Returns helper functions for adding specific item types:
- `addCourseToCart`: Add course to cart
- `addTemplateToCart`: Add template to cart
- `addCourseToCartAsync`: Add course to cart (async)
- `addTemplateToCartAsync`: Add template to cart (async)
- `isAddingToCart`: Loading state
- `addToCartError`: Error state

## Error Handling

The cart context includes comprehensive error handling:

1. **Automatic Retry**: Failed requests are automatically retried with exponential backoff
2. **Optimistic Updates**: UI updates immediately with automatic rollback on errors
3. **Error States**: Each operation exposes error states for UI feedback
4. **Network Resilience**: Handles network errors and server issues gracefully

## Caching Strategy

- **Cart Data**: Cached for 2 minutes with automatic refetching
- **Query Invalidation**: Cache is invalidated after successful mutations
- **Optimistic Updates**: Immediate UI feedback with server reconciliation
- **Background Refetching**: Data is refetched when window regains focus

## TypeScript Support

All hooks and functions are fully typed with TypeScript, providing:
- Type-safe cart operations
- Proper error typing
- IntelliSense support
- Compile-time error checking

## Example Components

See `components/cart/cart-example.tsx` for comprehensive usage examples including:
- Cart information display
- Adding items to cart
- Managing cart items
- Cart operations
- Error handling
- Loading states
