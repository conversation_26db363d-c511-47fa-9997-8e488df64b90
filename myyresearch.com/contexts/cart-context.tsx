"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { UseMutationResult, UseQueryResult } from "@tanstack/react-query";
import {
  useCart,
  useAddToCart,
  useRemoveFromCart,
  useUpdateCartItem,
  useClearCart,
  useMergeCart,
} from "@/lib/hooks/useCart";
import {
  getOrCreateCartId,
  getCartIdFromCookies,
  setCartIdInCookies,
  removeCartIdFromCookies,
} from "@/lib/cart";
import {
  AddToCartRequest,
  AddToCartResponse,
  RemoveFromCartResponse,
  UpdateCartItemQuantityResponse,
  ClearCartResponse,
  BackendCartResponse,
  ItemType,
} from "@/types/cart";

// Cart context interface
interface CartContextType {
  // Cart state
  cartId: string;
  cart: UseQueryResult<BackendCartResponse, Error>;

  // Cart operations
  addToCart: UseMutationResult<
    AddToCartResponse,
    Error,
    Omit<AddToCartRequest, "cartId">,
    { previousCart?: BackendCartResponse }
  >;
  removeFromCart: UseMutationResult<
    RemoveFromCartResponse,
    Error,
    string,
    { previousCart?: BackendCartResponse }
  >;
  updateCartItem: UseMutationResult<
    UpdateCartItemQuantityResponse,
    Error,
    { cartItemId: string; quantity: number },
    { previousCart?: BackendCartResponse }
  >;
  clearCart: UseMutationResult<
    ClearCartResponse,
    Error,
    void,
    { previousCart?: BackendCartResponse }
  >;
  mergeCart: UseMutationResult<BackendCartResponse, Error, string>;

  // Utility functions
  refreshCart: () => void;
  setNewCartId: (newCartId: string) => void;
  clearCartId: () => void;

  // Computed values
  totalItems: number;
  totalAmount: number;
  isEmpty: boolean;
  isLoading: boolean;
  isError: boolean;
}

// Create the context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Cart provider props
interface CartProviderProps {
  children: React.ReactNode;
  initialCartId?: string;
}

/**
 * Cart Provider component that manages cart state using TanStack Query
 * Supports both authenticated and unauthenticated users with cookie-based cart ID storage
 */
export function CartProvider({ children, initialCartId }: CartProviderProps) {
  const [cartId, setCartId] = useState<string>("");

  // Initialize cart ID on mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      const id = initialCartId || getOrCreateCartId();
      setCartId(id);
    }
  }, [initialCartId]);

  // Cart query and mutations
  const cart = useCart(cartId);
  const addToCartMutation = useAddToCart(cartId);
  const removeFromCartMutation = useRemoveFromCart(cartId);
  const updateCartItemMutation = useUpdateCartItem(cartId);
  const clearCartMutation = useClearCart(cartId);
  const mergeCartMutation = useMergeCart();

  // Utility functions
  const refreshCart = () => {
    cart.refetch();
  };

  const setNewCartId = (newCartId: string) => {
    setCartId(newCartId);
    setCartIdInCookies(newCartId);
  };

  const clearCartId = () => {
    removeCartIdFromCookies();
    const newCartId = getOrCreateCartId();
    setCartId(newCartId);
  };

  // Computed values
  const totalItems = cart.data?.cart?.totalItems || 0;
  const totalAmount = cart.data?.cart?.totalAmount || 0;
  const isEmpty = totalItems === 0;
  const isLoading = cart.isLoading;
  const isError = cart.isError;

  const contextValue: CartContextType = {
    // Cart state
    cartId,
    cart,

    // Cart operations
    addToCart: addToCartMutation,
    removeFromCart: removeFromCartMutation,
    updateCartItem: updateCartItemMutation,
    clearCart: clearCartMutation,
    mergeCart: mergeCartMutation,

    // Utility functions
    refreshCart,
    setNewCartId,
    clearCartId,

    // Computed values
    totalItems,
    totalAmount,
    isEmpty,
    isLoading,
    isError,
  };

  return (
    <CartContext.Provider value={contextValue}>{children}</CartContext.Provider>
  );
}

/**
 * Hook to use the cart context
 * Must be used within a CartProvider
 */
export function useCartContext(): CartContextType {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error("useCartContext must be used within a CartProvider");
  }
  return context;
}

/**
 * Higher-order component to provide cart context
 */
export function withCartProvider<P extends object>(
  Component: React.ComponentType<P>
) {
  return function CartProviderWrapper(props: P) {
    return (
      <CartProvider>
        <Component {...props} />
      </CartProvider>
    );
  };
}

// Export types for external use
export type { CartContextType };

// Convenience hooks that use the cart context
export function useCartData() {
  const { cart, cartId, totalItems, totalAmount, isEmpty, isLoading, isError } =
    useCartContext();

  console.log("Cart data in useCartData:", cart.data);

  return {
    cart: cart.data?.cart,
    cartId,
    totalItems,
    totalAmount: totalAmount,
    isEmpty,
    isLoading,
    isError,
    refetch: cart.refetch,
  };
}

export function useCartOperations() {
  const {
    addToCart,
    removeFromCart,
    updateCartItem,
    clearCart,
    mergeCart,
    refreshCart,
    setNewCartId,
    clearCartId,
  } = useCartContext();

  return {
    addToCart: addToCart.mutate,
    addToCartAsync: addToCart.mutateAsync,
    removeFromCart: removeFromCart.mutate,
    removeFromCartAsync: removeFromCart.mutateAsync,
    updateCartItem: updateCartItem.mutate,
    updateCartItemAsync: updateCartItem.mutateAsync,
    clearCart: clearCart.mutate,
    clearCartAsync: clearCart.mutateAsync,
    mergeCart: mergeCart.mutate,
    mergeCartAsync: mergeCart.mutateAsync,
    refreshCart,
    setNewCartId,
    clearCartId,

    // Loading states
    isAddingToCart: addToCart.isPending,
    isRemovingFromCart: removeFromCart.isPending,
    isUpdatingCartItem: updateCartItem.isPending,
    isClearingCart: clearCart.isPending,
    isMergingCart: mergeCart.isPending,

    // Error states
    addToCartError: addToCart.error,
    removeFromCartError: removeFromCart.error,
    updateCartItemError: updateCartItem.error,
    clearCartError: clearCart.error,
    mergeCartError: mergeCart.error,
  };
}

/**
 * Convenience hook for adding items to cart with proper typing
 */
export function useAddToCartHelper() {
  const { addToCart, addToCartAsync, isAddingToCart, addToCartError } =
    useCartOperations();

  const addCourseToCart = (
    courseId: string,
    publicId: string,
    quantity: number = 1
  ) => {
    addToCart({
      itemType: ItemType.COURSE,
      publicId,
      itemId: courseId,
      quantity,
    });
  };

  const addTemplateToCart = (
    templateId: string,
    publicId: string,
    quantity: number = 1
  ) => {
    addToCart({
      itemType: ItemType.TEMPLATE,
      publicId,
      itemId: templateId,
      quantity,
    });
  };

  const addCourseToCartAsync = async (
    courseId: string,
    publicId: string,
    quantity: number = 1
  ) => {
    return addToCartAsync({
      itemType: ItemType.COURSE,
      publicId,
      itemId: courseId,
      quantity,
    });
  };

  const addTemplateToCartAsync = async (
    templateId: string,
    publicId: string,
    quantity: number = 1
  ) => {
    return addToCartAsync({
      itemType: ItemType.TEMPLATE,
      publicId,
      itemId: templateId,
      quantity,
    });
  };

  return {
    addCourseToCart,
    addTemplateToCart,
    addCourseToCartAsync,
    addTemplateToCartAsync,
    isAddingToCart,
    addToCartError,
  };
}
