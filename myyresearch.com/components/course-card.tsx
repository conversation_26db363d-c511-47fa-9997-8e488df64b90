"use client";

import Image from "next/image";
import { PublicMinimalCourse, CourseTag } from "@/types/course";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useAddToCartHelper, useCartData } from "@/contexts/cart-context";
import { Loader2, ShoppingCart, Check } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useState } from "react";

interface CourseCardProps {
  course: PublicMinimalCourse;
}

export function CourseCard({ course }: CourseCardProps) {
  const router = useRouter();
  const { toast } = useToast();

  // Local loading state for this specific component
  const [isLocallyAddingToCart, setIsLocallyAddingToCart] = useState(false);

  // Cart context hooks
  const { cart } = useCartData();
  const { addCourseToCartAsync, addToCartError } = useAddToCartHelper();

  // Get course ID safely
  const courseId = course._id || "";

  // Check if course is free
  const isFree =
    course.price === 0 ||
    (course.discountPrice !== undefined && course.discountPrice === 0);

  // Check if course is in cart by comparing the course ID with cart item's itemId
  const isInCart =
    cart?.items.some((item) => {
      return item.itemId === courseId;
    }) || false;

  const handleAddToCart = async () => {
    if (!courseId) {
      toast({
        title: "Error",
        description: "Course ID is missing.",
        variant: "destructive",
      });
      return;
    }

    setIsLocallyAddingToCart(true);
    try {
      await addCourseToCartAsync(courseId, course.publicId, 1);
      toast({
        title: "Added to cart",
        description: `${course.title} has been added to your cart.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLocallyAddingToCart(false);
    }
  };

  const handleBuyNow = async () => {
    if (!courseId) {
      toast({
        title: "Error",
        description: "Course ID is missing.",
        variant: "destructive",
      });
      return;
    }

    try {
      // If course is NOT in cart, redirect directly to checkout without adding to cart
      if (!isInCart) {
        router.push("/checkout");
      } else {
        // If course IS in cart, redirect to checkout
        router.push("/checkout");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to navigate to checkout. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAccessNow = () => {
    router.push(`/courses/${course.publicId}`);
  };

  return (
    <div className="group relative bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 cursor-pointer">
      <Link href={`/courses/${course.publicId}`}>
        {/* Thumbnail */}
        <div className="relative w-full overflow-hidden rounded-t-lg aspect-[16/9]">
          <Image
            src={`${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${course.thumbnail}`}
            alt={course.title}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>

        {/* Tag Badge */}
        {"tag" in course && course.tag === CourseTag.BEST_SELLER && (
          <div className="absolute top-2 right-2">
            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-[#eceb98] text-[#593d00]">
              Bestseller
            </span>
          </div>
        )}

        {/* Content */}
        <div className="p-4">
          <div className="flex items-start justify-between gap-2">
            <h3 className="font-semibold text-gray-900 line-clamp-1">
              {course.title}
            </h3>
          </div>

          <p className="mt-1 text-sm text-gray-500 line-clamp-2">
            {course.shortDescription}
          </p>

          {/* Price Section */}
          <div className="mt-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isFree ? (
                <span className="text-lg font-bold text-green-600">Free</span>
              ) : course.discountPrice ? (
                <>
                  <span className="text-lg font-bold text-primary-600">
                    ${course.discountPrice}
                  </span>
                  <span className="text-sm text-gray-400 line-through">
                    ${course.price}
                  </span>
                </>
              ) : (
                <span className="text-lg font-bold text-primary-600">
                  ${course.price}
                </span>
              )}
            </div>
          </div>
        </div>
      </Link>

      {/* Buttons remain outside the Link */}
      <div className="mt-4 flex gap-2 p-4 pt-0">
        {isFree ? (
          <Button
            className="w-full"
            variant="default"
            onClick={handleAccessNow}
          >
            Access Now
          </Button>
        ) : (
          <>
            <Button
              className="flex-1"
              variant="default"
              onClick={handleAddToCart}
              disabled={isInCart || isLocallyAddingToCart}
            >
              {isLocallyAddingToCart ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Adding...</span>
                </div>
              ) : isInCart ? (
                <div className="flex items-center space-x-2">
                  <Check className="h-4 w-4" />
                  <span>In Cart</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <ShoppingCart className="h-4 w-4" />
                  <span>Add to Cart</span>
                </div>
              )}
            </Button>
            <Button
              className="flex-1"
              variant="outline"
              onClick={handleBuyNow}
              disabled={isLocallyAddingToCart}
            >
              {isLocallyAddingToCart ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Adding...</span>
                </div>
              ) : (
                "Buy now"
              )}
            </Button>
          </>
        )}
      </div>

      {/* Error display */}
      {addToCartError && (
        <div className="px-4 pb-4">
          <p className="text-sm text-red-600">{addToCartError.message}</p>
        </div>
      )}
    </div>
  );
}
