import React from "react";
import Link from "next/link";
import { templateCategories } from "@/data";

interface TemplateCategoryProps {
  className?: string;
}

const TemplateCategories: React.FC<TemplateCategoryProps> = ({
  className = "",
}) => {
  return (
    <div className={`container mx-auto px-4 py-12 ${className}`}>
      <h2 className="text-3xl font-bold text-center mb-12">
        Template Categories
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {templateCategories.map((category) => (
          <div
            key={category.id}
            className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300"
          >
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-4">{category.name}</h3>

              <ul className="space-y-2">
                {category.subcategories.slice(0, 4).map((subcategory) => (
                  <li key={subcategory.id}>
                    <Link
                      href={`/templates?categories=${category.id}&subcategories=${subcategory.id}`}
                      className="text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      {subcategory.name}
                    </Link>
                  </li>
                ))}
              </ul>

              {category.subcategories.length > 4 && (
                <div className="mt-4">
                  <Link
                    href={`/templates?categories=${category.id}`}
                    className="text-sm text-gray-600 hover:text-blue-600"
                  >
                    + {category.subcategories.length - 4} more subcategories
                  </Link>
                </div>
              )}

              <div className="mt-6">
                <Link
                  href={`/templates?categories=${category.id}`}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Browse All {category.name}
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TemplateCategories;
