"use client";

import {
  BookOpenIcon,
  BriefcaseIcon,
  BarChartIcon,
  FileTextIcon,
  GraduationCapIcon,
  LampDeskIcon,
  LineChartIcon,
  LayoutDashboardIcon,
  PencilRulerIcon,
  DatabaseIcon,
  FileSpreadsheetIcon,
  PresentationIcon,
  SearchIcon,
  ClipboardCheckIcon,
  TableIcon,
  PieChartIcon,
  BarChart4Icon,
  UsersIcon,
  FileIcon,
  MailIcon,
  NewspaperIcon,
  PenToolIcon,
  MegaphoneIcon,
  SmartphoneIcon,
  BlocksIcon,
  BoxIcon,
  BoxesIcon,
  CloudIcon,
  Code2Icon,
  CodeIcon,
  GlobeIcon,
  LayersIcon,
  PencilIcon,
  ServerIcon,
  UserIcon,
  AtSignIcon,
  SendIcon,
  BuildingIcon,
  PhoneIcon,
  CheckCircleIcon,
  ArrowRightIcon,
} from "lucide-react";
import { useRef, useState } from "react";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import MultiStepForm, { type FormStep } from "@/components/ui/multi-step-form";
import { But<PERSON> } from "@/components/ui/button";
import { useSubmitContactForm } from "@/lib/hooks/useContactUs";
import { ApiStatus } from "@/types/common";
import { Loader2 } from "lucide-react";

const formSteps: FormStep[] = [
  {
    level: 1,
    id: "service-type",
    title: "Select Your Service Type",
    description: "What type of service are you looking for?",
    items: [
      {
        id: "academic-services",
        title: "Academic Services",
        description: "Professional academic support",
        icon: GraduationCapIcon,
        image:
          "https://images.unsplash.com/photo-1517842645767-c639042777db?q=80&w=2370&auto=format&fit=crop",
        validNextSteps: [
          "assignment-templates",
          "presentation-templates",
          "assignments-help",
          "research-help",
        ],
      },
      {
        id: "business-services",
        title: "Business Services",
        description: "Smart insights for growth",
        icon: BriefcaseIcon,
        image:
          "https://images.unsplash.com/photo-1664575602276-acd073f104c1?q=80&w=2370&auto=format&fit=crop",
        validNextSteps: [
          "business-research",
          "tech-data-services",
          "document-templates",
          "content-writing",
        ],
      },
    ],
  },
  {
    level: 2,
    id: "service-category",
    title: "Choose Service Category",
    description: "Select the specific service that fits your needs",
    items: [
      // Academic Services Categories
      {
        id: "assignment-templates",
        title: "Assignment Templates",
        description: "Professional Templates for Polished Assignments",
        icon: FileTextIcon,
        image:
          "https://images.unsplash.com/photo-1606326608606-aa0b62935f2b?q=80&w=2370&auto=format&fit=crop",
        validNextSteps: [
          "nvq-template",
          "bachelors-template",
          "masters-template",
          "postgrad-template",
          "other-template",
        ],
      },
      {
        id: "presentation-templates",
        title: "Presentation Templates",
        description: "Eye-Catching Templates for Standout Presentations",
        icon: PresentationIcon,
        image:
          "https://images.unsplash.com/photo-1557426272-fc759fdf7a8d?q=80&w=2370&auto=format&fit=crop",
        validNextSteps: ["academic-presentation", "business-presentation"],
      },
      {
        id: "assignments-help",
        title: "Assignments Help",
        description: "Professional Assistance for Top Assignments",
        icon: LampDeskIcon,
        image:
          "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?q=80&w=2370&auto=format&fit=crop",
        validNextSteps: [
          "nvq-help",
          "bachelors-help",
          "masters-help",
          "postgrad-help",
          "other-help",
        ],
      },
      {
        id: "research-help",
        title: "Research Help",
        description: "Expert Guidance for Your Research Projects",
        icon: SearchIcon,
        image:
          "https://images.unsplash.com/photo-1532619675605-1ede6c2ed2b0?q=80&w=2370&auto=format&fit=crop",
        validNextSteps: [
          "academic-research",
          "business-research-help",
          "scientific-research",
        ],
      },

      // Business Services Categories
      {
        id: "business-research",
        title: "Business Research & Analysis",
        description: "Comprehensive research and analysis for your business",
        icon: LineChartIcon,
        image:
          "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2370&auto=format&fit=crop",
        validNextSteps: [
          "market-research",
          "competitor-analysis",
          "feasibility-studies",
          "trend-analysis",
          "swot-pestel",
          "survey-design",
          "other-research",
        ],
      },
      {
        id: "tech-data-services",
        title: "Tech & Data Services",
        description: "Technical and data solutions for your business",
        icon: DatabaseIcon,
        image:
          "https://images.unsplash.com/photo-1599658880436-c61792e70672?q=80&w=2370&auto=format&fit=crop",
        validNextSteps: [
          "data-visualization",
          "data-entry",
          "kpi-tracking",
          "report-automation",
          "crm-setup",
          "other-tech",
        ],
      },
      {
        id: "document-templates",
        title: "Business Document Templates",
        description: "Professional templates for business documents",
        icon: FileSpreadsheetIcon,
        image:
          "https://images.unsplash.com/photo-1586282391129-76a6df230234?q=80&w=2370&auto=format&fit=crop",
        validNextSteps: [
          "proposal-templates",
          "plan-templates",
          "pitch-deck",
          "financial-report",
          "company-profile",
          "meeting-agenda",
          "other-document",
        ],
      },
      {
        id: "content-writing",
        title: "Content Writing & Marketing",
        description: "Professional content creation and marketing services",
        icon: PenToolIcon,
        image:
          "https://images.unsplash.com/photo-1499750310107-5fef28a66643?q=80&w=2370&auto=format&fit=crop",
        validNextSteps: [
          "website-content",
          "blog-writing",
          "product-descriptions",
          "social-media",
          "newsletter-email",
          "case-studies",
          "press-release",
          "brand-storytelling",
          "other-content",
        ],
      },
    ],
  },
  {
    level: 3,
    id: "specific-service",
    title: "Select Specific Service",
    description: "Choose the specific service that meets your requirements",
    items: [
      // Assignment Templates options
      {
        id: "nvq-template",
        title: "NVQ Templates",
        description: "Professional templates for NVQ assignments",
        icon: ClipboardCheckIcon,
        image:
          "https://images.unsplash.com/photo-1522881451255-f59ad836fdfb?q=80&w=2372&auto=format&fit=crop",
      },
      {
        id: "bachelors-template",
        title: "Bachelor's Templates",
        description: "Professional templates for Bachelor's assignments",
        icon: FileTextIcon,
        image:
          "https://images.unsplash.com/photo-1523289217630-0dd16184af8e?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "masters-template",
        title: "Master's Templates",
        description: "Professional templates for Master's assignments",
        icon: FileTextIcon,
        image:
          "https://images.unsplash.com/photo-1456513080867-f24f06edba7b?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "postgrad-template",
        title: "Postgraduate Templates",
        description: "Professional templates for Postgraduate assignments",
        icon: FileTextIcon,
        image:
          "https://images.unsplash.com/photo-1532094349884-543bc11b234d?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "other-template",
        title: "Other Templates",
        description: "Custom templates for other academic levels",
        icon: FileIcon,
        image:
          "https://images.unsplash.com/photo-1553484771-689277e6fa16?q=80&w=2370&auto=format&fit=crop",
      },

      // Presentation Templates options
      {
        id: "academic-presentation",
        title: "Academic Presentation Templates",
        description: "Professional templates for academic presentations",
        icon: PresentationIcon,
        image:
          "https://images.unsplash.com/photo-1588345921523-c2dcdb7f1dcd?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "business-presentation",
        title: "Business Presentation Templates",
        description: "Professional templates for business presentations",
        icon: PresentationIcon,
        image:
          "https://images.unsplash.com/photo-1616531770192-6eaea74c2456?q=80&w=2370&auto=format&fit=crop",
      },

      // Assignments Help options
      {
        id: "nvq-help",
        title: "NVQ Assignments Help",
        description: "Professional assistance for NVQ assignments",
        icon: LampDeskIcon,
        image:
          "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "bachelors-help",
        title: "Bachelor's Assignments Help",
        description: "Professional assistance for Bachelor's assignments",
        icon: LampDeskIcon,
        image:
          "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "masters-help",
        title: "Master's Assignments Help",
        description: "Professional assistance for Master's assignments",
        icon: LampDeskIcon,
        image:
          "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "postgrad-help",
        title: "Postgraduate Assignments Help",
        description: "Professional assistance for Postgraduate assignments",
        icon: LampDeskIcon,
        image:
          "https://images.unsplash.com/photo-1606761568499-6d2451b23c66?q=80&w=2274&auto=format&fit=crop",
      },
      {
        id: "other-help",
        title: "Other Assignments Help",
        description: "Professional assistance for other academic levels",
        icon: LampDeskIcon,
        image:
          "https://images.unsplash.com/photo-1471107340929-a87cd0f5b5f3?q=80&w=2373&auto=format&fit=crop",
      },

      // Research Help options
      {
        id: "academic-research",
        title: "Academic Research",
        description: "Expert guidance for academic research projects",
        icon: BookOpenIcon,
        image:
          "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "business-research-help",
        title: "Market and Business Research",
        description: "Expert guidance for business research projects",
        icon: LineChartIcon,
        image:
          "https://images.unsplash.com/photo-1590402494610-2c378a9114c6?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "scientific-research",
        title: "Scientific Research",
        description: "Expert guidance for scientific research projects",
        icon: SearchIcon,
        image:
          "https://images.unsplash.com/photo-1576086213369-97a306d36557?q=80&w=2380&auto=format&fit=crop",
      },

      // Business Research & Analysis options
      {
        id: "market-research",
        title: "Market Research Reports",
        description: "Comprehensive market research reports",
        icon: BarChartIcon,
        image:
          "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=2426&auto=format&fit=crop",
      },
      {
        id: "competitor-analysis",
        title: "Competitor Analysis",
        description: "Detailed analysis of your competitors",
        icon: UsersIcon,
        image:
          "https://images.unsplash.com/photo-1552664730-d307ca884978?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "feasibility-studies",
        title: "Feasibility Studies",
        description: "Thorough feasibility studies for your projects",
        icon: ClipboardCheckIcon,
        image:
          "https://images.unsplash.com/photo-1572177812156-58036aae439c?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "trend-analysis",
        title: "Industry Trend Analysis",
        description: "Analysis of trends in your industry",
        icon: LineChartIcon,
        image:
          "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "swot-pestel",
        title: "SWOT & PESTEL Analysis",
        description: "SWOT and PESTEL analysis for your business",
        icon: LayoutDashboardIcon,
        image:
          "https://images.unsplash.com/photo-1542744173-05336fcc7ad4?q=80&w=2369&auto=format&fit=crop",
      },
      {
        id: "survey-design",
        title: "Survey Design & Data Analysis",
        description: "Professional survey design and data analysis",
        icon: TableIcon,
        image:
          "https://images.unsplash.com/photo-1591522810850-58128c5fb089?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "other-research",
        title: "Other Research Services",
        description: "Custom research services for your needs",
        icon: SearchIcon,
        image:
          "https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?q=80&w=2370&auto=format&fit=crop",
      },

      // Tech & Data Services options
      {
        id: "data-visualization",
        title: "Data Visualization Dashboards",
        description: "Interactive data visualization dashboards",
        icon: PieChartIcon,
        image:
          "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "data-entry",
        title: "Data Entry & Data Cleaning",
        description: "Professional data entry and cleaning services",
        icon: TableIcon,
        image:
          "https://images.unsplash.com/photo-1515879218367-8466d910aaa4?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "kpi-tracking",
        title: "KPI Tracking Tools",
        description: "Tools for tracking your key performance indicators",
        icon: BarChart4Icon,
        image:
          "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=2426&auto=format&fit=crop",
      },
      {
        id: "report-automation",
        title: "Custom Report Automation",
        description: "Automated reports in Excel, Power BI, Google Sheets",
        icon: LayoutDashboardIcon,
        image:
          "https://images.unsplash.com/photo-1565106430482-8f6e74349ca1?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "crm-setup",
        title: "CRM Setup & Optimization",
        description: "Setup and optimization of your CRM system",
        icon: DatabaseIcon,
        image:
          "https://images.unsplash.com/photo-1552664730-d307ca884978?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "other-tech",
        title: "Other Tech Services",
        description: "Custom tech and data services for your needs",
        icon: ServerIcon,
        image:
          "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?q=80&w=2372&auto=format&fit=crop",
      },

      // Business Document Templates options
      {
        id: "proposal-templates",
        title: "Business Proposal Templates",
        description: "Professional templates for business proposals",
        icon: FileTextIcon,
        image:
          "https://images.unsplash.com/photo-1542435503-956c469947f6?q=80&w=2374&auto=format&fit=crop",
      },
      {
        id: "plan-templates",
        title: "Business Plan Templates",
        description: "Professional templates for business plans",
        icon: FileTextIcon,
        image:
          "https://images.unsplash.com/photo-1494607239400-ff147086c950?q=80&w=2369&auto=format&fit=crop",
      },
      {
        id: "pitch-deck",
        title: "Pitch Deck Templates",
        description: "Professional templates for pitch decks",
        icon: PresentationIcon,
        image:
          "https://images.unsplash.com/photo-1612550761236-e813928f7271?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "financial-report",
        title: "Financial Report Templates",
        description: "Professional templates for financial reports",
        icon: FileSpreadsheetIcon,
        image:
          "https://images.unsplash.com/photo-1554224155-6726b3ff858f?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "company-profile",
        title: "Company Profile Templates",
        description: "Professional templates for company profiles",
        icon: BriefcaseIcon,
        image:
          "https://images.unsplash.com/photo-1570126618953-d437176e8c79?q=80&w=2394&auto=format&fit=crop",
      },
      {
        id: "meeting-agenda",
        title: "Meeting & Agenda Templates",
        description: "Professional templates for meetings and agendas",
        icon: ClipboardCheckIcon,
        image:
          "https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "other-document",
        title: "Other Document Templates",
        description: "Custom templates for other business documents",
        icon: FileIcon,
        image:
          "https://images.unsplash.com/photo-1586281380349-632531db7ed4?q=80&w=2370&auto=format&fit=crop",
      },

      // Content Writing & Marketing options
      {
        id: "website-content",
        title: "Website Content Writing",
        description: "Professional content for your website",
        icon: GlobeIcon,
        image:
          "https://images.unsplash.com/photo-1507238691740-187a5b1d37b8?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "blog-writing",
        title: "Blog Writing",
        description: "SEO-friendly, industry-specific blog writing",
        icon: PencilRulerIcon,
        image:
          "https://images.unsplash.com/photo-1499750310107-5fef28a66643?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "product-descriptions",
        title: "Product & Service Descriptions",
        description: "Professional descriptions for your products and services",
        icon: BoxIcon,
        image:
          "https://images.unsplash.com/photo-1542744094-3a31f272c490?q=80&w=2369&auto=format&fit=crop",
      },
      {
        id: "social-media",
        title: "Social Media Post Creation",
        description: "Engaging content for your social media channels",
        icon: SmartphoneIcon,
        image:
          "https://images.unsplash.com/photo-1563986768609-322da13575f3?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "newsletter-email",
        title: "Newsletter & Email Copy",
        description: "Professional copy for newsletters and emails",
        icon: MailIcon,
        image:
          "https://images.unsplash.com/photo-1596526131083-e8c633c948d2?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "case-studies",
        title: "Case Studies & Whitepapers",
        description: "In-depth case studies and whitepapers",
        icon: FileTextIcon,
        image:
          "https://images.unsplash.com/photo-1512314889357-e157c22f938d?q=80&w=2371&auto=format&fit=crop",
      },
      {
        id: "press-release",
        title: "Press Release Writing",
        description: "Professional press releases for your business",
        icon: NewspaperIcon,
        image:
          "https://images.unsplash.com/photo-1504711434969-e33886168f5c?q=80&w=2370&auto=format&fit=crop",
      },
      {
        id: "brand-storytelling",
        title: "Brand Storytelling",
        description: "Compelling storytelling for your brand",
        icon: MegaphoneIcon,
        image:
          "https://images.unsplash.com/photo-1494438639946-1ebd1d20bf85?q=80&w=2367&auto=format&fit=crop",
      },
      {
        id: "other-content",
        title: "Other Content Services",
        description: "Custom content writing services for your needs",
        icon: PenToolIcon,
        image:
          "https://images.unsplash.com/photo-1603796846097-bee99e4a601f?q=80&w=2574&auto=format&fit=crop",
      },
    ],
  },
];

// Keep the original form for backward compatibility or as a combined option
export default function ExtendedForm() {
  const [isCompact, setIsCompact] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<HTMLFormElement>(null);
  const submitContactForm = useSubmitContactForm();

  const handleComplete = (selections: Record<string | number, string>) => {
    if (formRef.current) {
      const isValid = formRef.current.checkValidity();
      if (!isValid) {
        formRef.current.reportValidity();
        return false;
      }
    }

    // Extract form data
    const formData = new FormData(formRef.current!);
    const firstName = formData.get("name") as string;
    const email = formData.get("email") as string;
    const phone = formData.get("phone") as string;
    const company = formData.get("company") as string;

    // Validate required fields
    if (!firstName || !email || !phone) {
      toast.error("Please fill in all required fields");
      return false;
    }

    // Build selected services message
    const selectedServices = formSteps
      .map((step, index) => {
        const selectedItem = step.items.find(
          (item) => item.id === selections[index]
        );
        if (selectedItem) {
          return `${step.title}: ${selectedItem.title}`;
        }
        return null;
      })
      .filter(Boolean)
      .join("\n");

    const message = `Service Request Details:

${selectedServices}

${company ? `Company: ${company}\n` : ""}
Additional Information: This request was submitted through the extended service selection form.`;

    // Split firstName into first and last name
    const nameParts = firstName.trim().split(" ");
    const firstNamePart = nameParts[0] || "";
    const lastNamePart = nameParts.slice(1).join(" ") || "N/A";

    setIsSubmitting(true);

    // Submit the form asynchronously
    submitContactForm.mutate(
      {
        firstName: firstNamePart,
        lastName: lastNamePart,
        email,
        phone,
        message,
      },
      {
        onSuccess: (response) => {
          setIsSubmitting(false);
          if (response.status === ApiStatus.SUCCESS) {
            toast.success("Request submitted successfully!", {
              description: "We'll get back to you within 24 hours.",
            });
          } else {
            toast.error("Failed to submit request", {
              description: response.message || "Please try again later.",
            });
          }
        },
        onError: (error: any) => {
          setIsSubmitting(false);
          toast.error("Failed to submit request", {
            description: error?.message || "Please try again later.",
          });
        },
      }
    );

    // Return true to proceed to success step
    return true;
  };

  return (
    <MultiStepForm
      title={
        <div className="flex items-center justify-between w-full flex-col space-y-4 pt-6 sm:pt-10 md:pt-16 lg:pt-20">
          <div className="flex items-center gap-2">
            <span className="font-semibold">My Research</span>
          </div>
        </div>
      }
      formSteps={formSteps}
      onComplete={handleComplete}
      variant={isCompact ? "compact" : "default"}
      imageClassName="grayscale hover:grayscale-0"
      cardClassName="pb-2"
      finalStep={
        <div className="flex flex-col items-center gap-6 p-8 text-center">
          <div className="relative">
            <div className="absolute -inset-4 rounded-full bg-primary-100 dark:bg-primary-900/20 animate-pulse"></div>
            <div className="relative bg-primary-500 rounded-full p-4">
              <CheckCircleIcon
                className="h-12 w-12 text-white"
                strokeWidth={1.5}
              />
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-2xl font-bold tracking-tight">
              Thank You for Your Submission!
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-md">
              We've received your information and will be in touch shortly. Our
              team is excited to help with your project.
            </p>
          </div>

          <div className="w-full max-w-md p-4 mt-2 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-100 dark:border-gray-800">
            <h4 className="font-medium mb-2 text-sm">What happens next?</h4>
            <ol className="space-y-2 text-sm text-left">
              <li className="flex items-start gap-2">
                <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300 rounded-full h-5 w-5 flex items-center justify-center shrink-0 mt-0.5">
                  1
                </div>
                <span>Our team will review your request within 24 hours</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300 rounded-full h-5 w-5 flex items-center justify-center shrink-0 mt-0.5">
                  2
                </div>
                <span>You'll receive a confirmation email with details</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300 rounded-full h-5 w-5 flex items-center justify-center shrink-0 mt-0.5">
                  3
                </div>
                <span>A specialist will contact you to discuss your needs</span>
              </li>
            </ol>
          </div>

          <Button
            className="mt-2 group flex items-center gap-1.5 text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium transition-colors"
            variant="link"
            onClick={() => window.location.reload()}
          >
            Start a new request
            <ArrowRightIcon className="h-3.5 w-3.5 transition-transform group-hover:translate-x-0.5" />
          </Button>
        </div>
      }
    >
      <form
        ref={formRef}
        className="space-y-6 p-6 bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-100 dark:border-gray-800 transition-all hover:shadow-md"
      >
        <div className="space-y-2">
          <Label htmlFor="name" className="text-sm font-medium">
            Your Name
          </Label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
              <UserIcon className="h-5 w-5" />
            </div>
            <Input
              id="name"
              name="name"
              required={true}
              placeholder="Enter your name"
              className="pl-10 py-3 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 rounded-lg focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email" className="text-sm font-medium">
            Email Address
          </Label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
              <AtSignIcon className="h-5 w-5" />
            </div>
            <Input
              id="email"
              name="email"
              type="email"
              required={true}
              placeholder="Enter your email"
              className="pl-10 py-3 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 rounded-lg focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone" className="text-sm font-medium">
            Telephone Number
          </Label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
              <PhoneIcon className="h-5 w-5" />
            </div>
            <Input
              id="phone"
              name="phone"
              type="tel"
              required={true}
              placeholder="Enter your phone number"
              className="pl-10 py-3 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 rounded-lg focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="company" className="text-sm font-medium">
            Company (Optional)
          </Label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
              <BuildingIcon className="h-5 w-5" />
            </div>
            <Input
              id="company"
              name="company"
              type="text"
              placeholder="Enter your company name"
              className="pl-10 py-3 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 rounded-lg focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-600"
            />
          </div>
        </div>

        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full group relative py-3.5 px-6 overflow-hidden rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 text-white font-medium shadow-sm hover:shadow-md transition-all duration-300 outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span className="relative z-10 flex items-center justify-center gap-2">
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Submitting...
              </>
            ) : (
              <>
                Submit Request
                <SendIcon className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              </>
            )}
          </span>
          {!isSubmitting && (
            <div className="absolute inset-0 w-1/3 bg-white bg-opacity-20 skew-x-12 -translate-x-full group-hover:animate-shine"></div>
          )}
        </button>
      </form>

      <style jsx global>{`
        @keyframes shine {
          100% {
            transform: translateX(300%);
          }
        }
        .group:hover .group-hover\\:animate-shine {
          animation: shine 1s ease-in-out;
        }
      `}</style>
    </MultiStepForm>
  );
}
