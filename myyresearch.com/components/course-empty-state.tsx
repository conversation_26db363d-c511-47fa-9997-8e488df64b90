import { FolderSearch } from "lucide-react"; // Import the icon from lucide-react

interface CourseEmptyStateProps {
  category: string;
}

export function CourseEmptyState({ category }: CourseEmptyStateProps) {
  return (
    <div className="h-[400px] flex items-center justify-center">
      <div className="flex flex-col items-center justify-center space-y-4 p-6 text-center">
        <FolderSearch className="h-12 w-12 text-muted-foreground" />
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">No Courses Found</h3>
          <p className="text-sm text-muted-foreground">
            We couldn't find any courses in the {category} category.
          </p>
        </div>
      </div>
    </div>
  );
}
