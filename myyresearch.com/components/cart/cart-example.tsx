"use client";

import React from "react";
import {
  useCartContext,
  useCartData,
  useCartOperations,
  useAddToCartHelper,
} from "@/contexts/cart-context";
import { ItemType } from "@/types/cart";

/**
 * Example component demonstrating how to use the cart context
 * This shows all the different ways to interact with the cart
 */
export function CartExample() {
  // Method 1: Use the full cart context
  const cartContext = useCartContext();

  // Method 2: Use convenience hooks for specific functionality
  const cartData = useCartData();
  const cartOperations = useCartOperations();
  const addToCartHelper = useAddToCartHelper();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Cart Management Example</h1>

      {/* Cart Information */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-3">Cart Information</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p><strong>Cart ID:</strong> {cartData.cartId}</p>
            <p><strong>Total Items:</strong> {cartData.totalItems}</p>
            <p><strong>Total Amount:</strong> ${cartData.totalAmount.toFixed(2)}</p>
            <p><strong>Is Empty:</strong> {cartData.isEmpty ? "Yes" : "No"}</p>
          </div>
          <div>
            <p><strong>Loading:</strong> {cartData.isLoading ? "Yes" : "No"}</p>
            <p><strong>Error:</strong> {cartData.isError ? "Yes" : "No"}</p>
          </div>
        </div>
      </div>

      {/* Add to Cart Examples */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-3">Add to Cart</h2>
        <div className="space-y-3">
          {/* Using helper functions */}
          <div className="flex gap-2">
            <button
              onClick={() => addToCartHelper.addCourseToCart("course-123", 1)}
              disabled={addToCartHelper.isAddingToCart}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {addToCartHelper.isAddingToCart ? "Adding..." : "Add Course"}
            </button>
            <button
              onClick={() => addToCartHelper.addTemplateToCart("template-456", 1)}
              disabled={addToCartHelper.isAddingToCart}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
            >
              {addToCartHelper.isAddingToCart ? "Adding..." : "Add Template"}
            </button>
          </div>

          {/* Using direct cart operations */}
          <div className="flex gap-2">
            <button
              onClick={() => cartOperations.addToCart({
                itemType: ItemType.COURSE,
                itemId: "course-789",
                quantity: 2
              })}
              disabled={cartOperations.isAddingToCart}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
            >
              Add Course (Qty: 2)
            </button>
          </div>

          {/* Using async operations */}
          <div className="flex gap-2">
            <button
              onClick={async () => {
                try {
                  await addToCartHelper.addCourseToCartAsync("course-async", 1);
                  alert("Course added successfully!");
                } catch (error) {
                  alert("Failed to add course");
                }
              }}
              className="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600"
            >
              Add Course (Async)
            </button>
          </div>

          {addToCartHelper.addToCartError && (
            <p className="text-red-500">
              Error: {addToCartHelper.addToCartError.message}
            </p>
          )}
        </div>
      </div>

      {/* Cart Items Display */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-3">Cart Items</h2>
        {cartData.isLoading ? (
          <p>Loading cart...</p>
        ) : cartData.cart?.items.length ? (
          <div className="space-y-3">
            {cartData.cart.items.map((item) => (
              <div key={item._id} className="flex items-center justify-between bg-white p-3 rounded border">
                <div className="flex-1">
                  <h3 className="font-medium">{item.title}</h3>
                  <p className="text-sm text-gray-600">
                    {item.itemType} • Quantity: {item.quantity} • 
                    Price: ${(item.discountPrice || item.price).toFixed(2)}
                  </p>
                </div>
                <div className="flex gap-2">
                  {/* Update quantity */}
                  <button
                    onClick={() => cartOperations.updateCartItem({
                      cartItemId: item._id,
                      quantity: item.quantity + 1
                    })}
                    disabled={cartOperations.isUpdatingCartItem}
                    className="px-2 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 disabled:opacity-50"
                  >
                    +
                  </button>
                  <button
                    onClick={() => cartOperations.updateCartItem({
                      cartItemId: item._id,
                      quantity: Math.max(1, item.quantity - 1)
                    })}
                    disabled={cartOperations.isUpdatingCartItem}
                    className="px-2 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 disabled:opacity-50"
                  >
                    -
                  </button>
                  {/* Remove item */}
                  <button
                    onClick={() => cartOperations.removeFromCart(item._id)}
                    disabled={cartOperations.isRemovingFromCart}
                    className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 disabled:opacity-50"
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p>Your cart is empty</p>
        )}
      </div>

      {/* Cart Operations */}
      <div className="bg-red-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-3">Cart Operations</h2>
        <div className="flex gap-3">
          <button
            onClick={() => cartOperations.clearCart()}
            disabled={cartOperations.isClearingCart || cartData.isEmpty}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            {cartOperations.isClearingCart ? "Clearing..." : "Clear Cart"}
          </button>
          <button
            onClick={() => cartData.refetch()}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Refresh Cart
          </button>
          <button
            onClick={() => cartOperations.clearCartId()}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            New Cart ID
          </button>
        </div>
      </div>

      {/* Merge Cart Example (for login scenarios) */}
      <div className="bg-green-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Merge Cart (Login Scenario)</h2>
        <div className="flex gap-3">
          <button
            onClick={() => {
              // Simulate merging a guest cart when user logs in
              const guestCartId = "guest-cart-123";
              cartOperations.mergeCart(guestCartId);
            }}
            disabled={cartOperations.isMergingCart}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {cartOperations.isMergingCart ? "Merging..." : "Merge Guest Cart"}
          </button>
        </div>
        {cartOperations.mergeCartError && (
          <p className="text-red-500 mt-2">
            Merge Error: {cartOperations.mergeCartError.message}
          </p>
        )}
      </div>

      {/* Loading States */}
      {(cartOperations.isAddingToCart || 
        cartOperations.isRemovingFromCart || 
        cartOperations.isUpdatingCartItem || 
        cartOperations.isClearingCart || 
        cartOperations.isMergingCart) && (
        <div className="fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded shadow-lg">
          Processing cart operation...
        </div>
      )}
    </div>
  );
}

/**
 * Simple cart summary component
 */
export function CartSummary() {
  const { totalItems, totalAmount, isEmpty } = useCartData();

  if (isEmpty) {
    return (
      <div className="text-sm text-gray-500">
        Cart is empty
      </div>
    );
  }

  return (
    <div className="text-sm">
      <span className="font-medium">{totalItems} items</span>
      <span className="mx-2">•</span>
      <span className="font-medium">${totalAmount.toFixed(2)}</span>
    </div>
  );
}

/**
 * Cart badge component for navigation
 */
export function CartBadge() {
  const { totalItems, isLoading } = useCartData();

  return (
    <div className="relative">
      <span className="text-lg">🛒</span>
      {!isLoading && totalItems > 0 && (
        <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
          {totalItems > 99 ? "99+" : totalItems}
        </span>
      )}
    </div>
  );
}
