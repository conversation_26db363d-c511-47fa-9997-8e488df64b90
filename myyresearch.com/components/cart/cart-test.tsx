"use client";

import React from "react";
import { useCartData, useCartOperations, useAddToCartHelper } from "@/contexts/cart-context";
import { ItemType } from "@/types/cart";

/**
 * Simple test component to verify cart functionality
 */
export function CartTest() {
  const { cart, cartId, totalItems, totalAmount, isEmpty, isLoading, isError } = useCartData();
  const { 
    addToCart, 
    removeFromCart, 
    updateCartItem, 
    clearCart,
    isAddingToCart,
    isRemovingFromCart,
    isUpdatingCartItem,
    isClearingCart,
    addToCartError,
    removeFromCartError,
    updateCartItemError,
    clearCartError
  } = useCartOperations();
  
  const { 
    addCourseToCart, 
    addTemplateToCart, 
    isAddingToCart: isHelperAdding,
    addToCartError: helperError 
  } = useAddToCartHelper();

  if (isLoading) {
    return <div className="p-4">Loading cart...</div>;
  }

  if (isError) {
    return <div className="p-4 text-red-500">Error loading cart</div>;
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Cart Test Component</h1>
      
      {/* Cart Status */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">Cart Status</h2>
        <p><strong>Cart ID:</strong> {cartId}</p>
        <p><strong>Total Items:</strong> {totalItems}</p>
        <p><strong>Total Amount:</strong> ${totalAmount.toFixed(2)}</p>
        <p><strong>Is Empty:</strong> {isEmpty ? "Yes" : "No"}</p>
      </div>

      {/* Add Items */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-3">Add Items</h2>
        <div className="space-y-2">
          <div className="flex gap-2">
            <button
              onClick={() => addCourseToCart("test-course-1", 1)}
              disabled={isHelperAdding}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {isHelperAdding ? "Adding..." : "Add Test Course"}
            </button>
            <button
              onClick={() => addTemplateToCart("test-template-1", 1)}
              disabled={isHelperAdding}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
            >
              {isHelperAdding ? "Adding..." : "Add Test Template"}
            </button>
          </div>
          
          <button
            onClick={() => addToCart({
              itemType: ItemType.COURSE,
              itemId: "test-course-2",
              quantity: 2
            })}
            disabled={isAddingToCart}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
          >
            {isAddingToCart ? "Adding..." : "Add Course (Qty: 2)"}
          </button>
          
          {(addToCartError || helperError) && (
            <p className="text-red-500 text-sm">
              Error: {(addToCartError || helperError)?.message}
            </p>
          )}
        </div>
      </div>

      {/* Cart Items */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-3">Cart Items</h2>
        {cart?.items && cart.items.length > 0 ? (
          <div className="space-y-2">
            {cart.items.map((item) => (
              <div key={item._id} className="flex items-center justify-between bg-white p-3 rounded border">
                <div>
                  <h3 className="font-medium">{item.title}</h3>
                  <p className="text-sm text-gray-600">
                    {item.itemType} • Qty: {item.quantity} • ${(item.discountPrice || item.price).toFixed(2)}
                  </p>
                </div>
                <div className="flex gap-1">
                  <button
                    onClick={() => updateCartItem({
                      cartItemId: item._id,
                      quantity: item.quantity + 1
                    })}
                    disabled={isUpdatingCartItem}
                    className="px-2 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 disabled:opacity-50"
                  >
                    +
                  </button>
                  <button
                    onClick={() => updateCartItem({
                      cartItemId: item._id,
                      quantity: Math.max(1, item.quantity - 1)
                    })}
                    disabled={isUpdatingCartItem}
                    className="px-2 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 disabled:opacity-50"
                  >
                    -
                  </button>
                  <button
                    onClick={() => removeFromCart(item._id)}
                    disabled={isRemovingFromCart}
                    className="px-2 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 disabled:opacity-50"
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No items in cart</p>
        )}
        
        {(removeFromCartError || updateCartItemError) && (
          <p className="text-red-500 text-sm mt-2">
            Error: {(removeFromCartError || updateCartItemError)?.message}
          </p>
        )}
      </div>

      {/* Cart Actions */}
      <div className="bg-red-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-3">Cart Actions</h2>
        <button
          onClick={() => clearCart()}
          disabled={isClearingCart || isEmpty}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
        >
          {isClearingCart ? "Clearing..." : "Clear Cart"}
        </button>
        
        {clearCartError && (
          <p className="text-red-500 text-sm mt-2">
            Error: {clearCartError.message}
          </p>
        )}
      </div>
    </div>
  );
}

/**
 * Simple cart badge for testing
 */
export function SimpleCartBadge() {
  const { totalItems, isLoading } = useCartData();

  return (
    <div className="flex items-center gap-2 p-2 bg-gray-100 rounded">
      <span>🛒</span>
      <span className="text-sm">
        {isLoading ? "Loading..." : `${totalItems} items`}
      </span>
    </div>
  );
}
