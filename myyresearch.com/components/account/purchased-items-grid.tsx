"use client";

import React, { useState } from "react";
import { OrderItemDetails, OrderItemType, CreatorType } from "@/types/orders";
import { PurchasedItemCard } from "./purchased-item-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Search, Filter, SortAsc, SortDesc } from "lucide-react";

interface PurchasedItemsGridProps {
  items: OrderItemDetails[];
  itemType?: OrderItemType;
  title?: string;
  emptyMessage?: string;
  onItemAccess?: (item: OrderItemDetails) => void;
}

type SortOption = 'date-desc' | 'date-asc' | 'title-asc' | 'title-desc' | 'price-desc' | 'price-asc';

export function PurchasedItemsGrid({
  items,
  itemType,
  title,
  emptyMessage,
  onItemAccess,
}: PurchasedItemsGridProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [creatorFilter, setCreatorFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<SortOption>("date-desc");

  // Get unique categories from items
  const categories = React.useMemo(() => {
    const uniqueCategories = Array.from(new Set(items.map(item => item.category)));
    return uniqueCategories.sort();
  }, [items]);

  // Filter and sort items
  const filteredAndSortedItems = React.useMemo(() => {
    let filtered = items;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.itemTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.shortDescription.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.subCategory.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply category filter
    if (categoryFilter !== "all") {
      filtered = filtered.filter(item => item.category === categoryFilter);
    }

    // Apply creator filter
    if (creatorFilter !== "all") {
      filtered = filtered.filter(item => item.creatorType === creatorFilter);
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'date-desc':
          return new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime();
        case 'date-asc':
          return new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime();
        case 'title-asc':
          return a.itemTitle.localeCompare(b.itemTitle);
        case 'title-desc':
          return b.itemTitle.localeCompare(a.itemTitle);
        case 'price-desc':
          return b.totalPrice - a.totalPrice;
        case 'price-asc':
          return a.totalPrice - b.totalPrice;
        default:
          return 0;
      }
    });

    return sorted;
  }, [items, searchTerm, categoryFilter, creatorFilter, sortBy]);

  const clearFilters = () => {
    setSearchTerm("");
    setCategoryFilter("all");
    setCreatorFilter("all");
    setSortBy("date-desc");
  };

  const hasActiveFilters = searchTerm || categoryFilter !== "all" || creatorFilter !== "all" || sortBy !== "date-desc";

  return (
    <div className="space-y-6">
      {/* Header */}
      {title && (
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">{title}</h2>
          <Badge variant="outline" className="text-sm">
            {filteredAndSortedItems.length} {filteredAndSortedItems.length === 1 ? 'item' : 'items'}
          </Badge>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search your purchases..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Category Filter */}
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Creator Filter */}
        {!itemType && (
          <Select value={creatorFilter} onValueChange={setCreatorFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="All Creators" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Creators</SelectItem>
              <SelectItem value={CreatorType.MYRESEARCH}>MyResearch</SelectItem>
              <SelectItem value={CreatorType.INSTRUCTOR}>Instructor</SelectItem>
            </SelectContent>
          </Select>
        )}

        {/* Sort */}
        <Select value={sortBy} onValueChange={(value) => setSortBy(value as SortOption)}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="date-desc">
              <div className="flex items-center gap-2">
                <SortDesc className="w-4 h-4" />
                Newest First
              </div>
            </SelectItem>
            <SelectItem value="date-asc">
              <div className="flex items-center gap-2">
                <SortAsc className="w-4 h-4" />
                Oldest First
              </div>
            </SelectItem>
            <SelectItem value="title-asc">A-Z</SelectItem>
            <SelectItem value="title-desc">Z-A</SelectItem>
            <SelectItem value="price-desc">Price: High to Low</SelectItem>
            <SelectItem value="price-asc">Price: Low to High</SelectItem>
          </SelectContent>
        </Select>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <Button variant="outline" onClick={clearFilters} className="whitespace-nowrap">
            <Filter className="w-4 h-4 mr-2" />
            Clear
          </Button>
        )}
      </div>

      {/* Items Grid */}
      {filteredAndSortedItems.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedItems.map((item) => (
            <PurchasedItemCard
              key={item._id}
              item={item}
              onAccess={onItemAccess}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-muted-foreground mb-4">
            {hasActiveFilters ? (
              <>
                <Filter className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">No items match your filters</p>
                <p className="text-sm">Try adjusting your search or filter criteria</p>
              </>
            ) : (
              <>
                <div className="w-12 h-12 mx-auto mb-4 opacity-50">
                  {itemType === OrderItemType.COURSE ? "📚" : itemType === OrderItemType.TEMPLATE ? "📄" : "🛒"}
                </div>
                <p className="text-lg font-medium">
                  {emptyMessage || `No ${itemType ? (itemType === OrderItemType.COURSE ? 'courses' : 'templates') : 'purchases'} yet`}
                </p>
                <p className="text-sm">
                  {itemType === OrderItemType.COURSE 
                    ? "Start learning by purchasing your first course"
                    : itemType === OrderItemType.TEMPLATE
                    ? "Download templates to boost your productivity"
                    : "Your purchase history will appear here"
                  }
                </p>
              </>
            )}
          </div>
          {hasActiveFilters && (
            <Button variant="outline" onClick={clearFilters}>
              Clear Filters
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
