"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { OrderItemDetails, OrderItemType, CreatorType } from "@/types/orders";
import { formatDate } from "@/lib/utils";
import { Calendar, DollarSign, User, ExternalLink, Play, Download } from "lucide-react";

interface PurchasedItemCardProps {
  item: OrderItemDetails;
  onAccess?: (item: OrderItemDetails) => void;
}

export function PurchasedItemCard({ item, onAccess }: PurchasedItemCardProps) {
  const isCourse = item.itemType === OrderItemType.COURSE;
  const isTemplate = item.itemType === OrderItemType.TEMPLATE;

  const handleAccess = () => {
    if (onAccess) {
      onAccess(item);
    } else {
      // Default behavior - navigate to item page
      const url = isCourse ? `/courses/${item.publicId}` : `/templates/${item.publicId}`;
      window.open(url, '_blank');
    }
  };

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 border-border/50 hover:border-border">
      <CardHeader className="pb-3">
        <div className="flex items-start gap-3">
          {/* Thumbnail */}
          <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-muted flex-shrink-0">
            {item.thumbnail?.url ? (
              <Image
                src={item.thumbnail.url}
                alt={item.itemTitle}
                fill
                className="object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                {isCourse ? (
                  <Play className="w-6 h-6 text-muted-foreground" />
                ) : (
                  <Download className="w-6 h-6 text-muted-foreground" />
                )}
              </div>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <h3 className="font-semibold text-sm leading-tight line-clamp-2 group-hover:text-primary transition-colors">
                {item.itemTitle}
              </h3>
              <Badge 
                variant={isCourse ? "default" : "secondary"}
                className="text-xs flex-shrink-0"
              >
                {isCourse ? "Course" : "Template"}
              </Badge>
            </div>
            
            <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
              {item.shortDescription}
            </p>

            {/* Category */}
            <div className="flex items-center gap-1 mt-2">
              <span className="text-xs text-muted-foreground">{item.category}</span>
              {item.subCategory && (
                <>
                  <span className="text-xs text-muted-foreground">•</span>
                  <span className="text-xs text-muted-foreground">{item.subCategory}</span>
                </>
              )}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0 pb-3">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            {/* Purchase Date */}
            <div className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              <span>{formatDate(new Date(item.orderDate))}</span>
            </div>

            {/* Price */}
            <div className="flex items-center gap-1">
              <DollarSign className="w-3 h-3" />
              <span>${item.totalPrice.toFixed(2)}</span>
            </div>
          </div>

          {/* Creator Type */}
          <div className="flex items-center gap-1">
            <User className="w-3 h-3" />
            <span>
              {item.creatorType === CreatorType.MYRESEARCH ? "MyResearch" : "Instructor"}
            </span>
          </div>
        </div>

        {/* Order ID */}
        <div className="mt-2">
          <span className="text-xs text-muted-foreground">
            Order: {item.orderId}
          </span>
        </div>
      </CardContent>

      <CardFooter className="pt-0">
        <div className="flex gap-2 w-full">
          <Button
            onClick={handleAccess}
            className="flex-1"
            size="sm"
          >
            {isCourse ? (
              <>
                <Play className="w-4 h-4 mr-2" />
                Access Course
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Download
              </>
            )}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            asChild
          >
            <Link href={isCourse ? `/courses/${item.publicId}` : `/templates/${item.publicId}`}>
              <ExternalLink className="w-4 h-4" />
            </Link>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
