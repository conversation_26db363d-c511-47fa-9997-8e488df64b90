import { FolderSearch } from "lucide-react"; // Import the icon from lucide-react

interface TemplateEmptyStateProps {
  category: string;
}

export function TemplateEmptyState({ category }: TemplateEmptyStateProps) {
  return (
    <div className="h-[400px] flex items-center justify-center">
      <div className="flex flex-col items-center justify-center space-y-4 p-6 text-center">
        <FolderSearch className="h-12 w-12 text-muted-foreground" />
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">No Templates Found</h3>
          <p className="text-sm text-muted-foreground">
            We couldn't find any templates in the {category} category.
          </p>
        </div>
      </div>
    </div>
  );
}
