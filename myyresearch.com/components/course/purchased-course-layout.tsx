"use client";

import { CourseVideoSection } from "./course-video-section";
import { CourseContentTabs } from "./course-content-tabs";
import { CourseCurriculumSidebar } from "./course-curriculum-sidebar";
import { useState } from "react";
import { PublicCourseResponse } from "@/types/course";

interface PurchasedCourseLayoutProps {
  course: PublicCourseResponse;
}

export function PurchasedCourseLayout({ course }: PurchasedCourseLayoutProps) {
  const [currentLesson, setCurrentLesson] = useState<{
    sectionId: string;
    lessonId: string;
  } | null>(null);

  // Find current lesson details
  const getCurrentLessonDetails = () => {
    if (!currentLesson) return null;

    const section = course.sections.find(
      (s) => s._id === currentLesson.sectionId
    );
    if (!section) return null;

    const lesson = section.lessons.find(
      (l) => l._id === currentLesson.lessonId
    );
    if (!lesson) return null;

    return { section, lesson };
  };

  const currentLessonDetails = getCurrentLessonDetails();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Course Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {course.title}
              </h1>
              {currentLessonDetails && (
                <p className="text-sm text-gray-600 mt-1">
                  {currentLessonDetails.section.title} •{" "}
                  {currentLessonDetails.lesson.title}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              {/* <span className="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">
                Purchased
              </span> */}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Column - Video and Content */}
          <div className="lg:col-span-3 space-y-6 order-2 lg:order-1">
            {/* Video Player Section */}
            <CourseVideoSection
              course={course}
              currentLesson={currentLessonDetails}
              onLessonChange={setCurrentLesson}
            />

            {/* Tabbed Content - Hidden on mobile when sidebar is visible */}
            <div className="hidden lg:block">
              <CourseContentTabs
                course={course}
                currentLesson={currentLessonDetails}
              />
            </div>
          </div>

          {/* Right Column - Course Curriculum */}
          <div className="lg:col-span-1 order-1 lg:order-2">
            <div className="lg:sticky lg:top-6">
              <CourseCurriculumSidebar
                course={course}
                currentLesson={currentLesson}
                onLessonSelect={setCurrentLesson}
              />
            </div>
          </div>
        </div>

        {/* Mobile Tabbed Content */}
        <div className="lg:hidden mt-6">
          <CourseContentTabs
            course={course}
            currentLesson={currentLessonDetails}
          />
        </div>
      </div>
    </div>
  );
}
