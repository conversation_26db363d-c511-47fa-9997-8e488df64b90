"use client";

import { PublicSection, PublicLesson } from "@/types/course";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DownloadableFilesList } from "./downloadable-files-list";
import Viewer from "@/components/editor/viewer";
import { FileText, Download } from "lucide-react";

interface CourseContentTabsProps {
  course: any;
  currentLesson: {
    section: PublicSection;
    lesson: PublicLesson;
  } | null;
}

export function CourseContentTabs({
  course,
  currentLesson,
}: CourseContentTabsProps) {
  // Collect all downloadable files from course and current lesson
  const getAllDownloadableFiles = () => {
    const files = [];

    // Course-level files
    if (course.downloadableFiles) {
      files.push(...course.downloadableFiles);
    }

    // Current lesson files
    if (currentLesson?.lesson.downloadableFiles) {
      files.push(...currentLesson.lesson.downloadableFiles);
    }

    // Section-level files (if any sections have downloadable files)
    course.sections.forEach((section) => {
      section.lessons.forEach((lesson) => {
        if (lesson.downloadableFiles) {
          files.push(...lesson.downloadableFiles);
        }
      });
    });

    // Remove duplicates based on file ID
    const uniqueFiles = files.filter(
      (file, index, self) => index === self.findIndex((f) => f._id === file._id)
    );

    return uniqueFiles;
  };

  const downloadableFiles = getAllDownloadableFiles();

  return (
    <Card>
      <CardContent className="p-0">
        <Tabs defaultValue="description" className="w-full">
          <div className="border-b">
            <TabsList className="w-full justify-start rounded-none bg-transparent p-0 h-auto">
              <TabsTrigger
                value="description"
                className="rounded-none border-b-2 border-transparent data-[state=active]:border-blue-600 data-[state=active]:bg-transparent px-6 py-4"
              >
                <FileText className="w-4 h-4 mr-2" />
                Description
              </TabsTrigger>
              <TabsTrigger
                value="files"
                className="rounded-none border-b-2 border-transparent data-[state=active]:border-blue-600 data-[state=active]:bg-transparent px-6 py-4"
              >
                <Download className="w-4 h-4 mr-2" />
                Files ({downloadableFiles.length})
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="description" className="p-6 mt-0">
            <div className="space-y-6">
              {/* Current Lesson Content */}
              {currentLesson?.lesson.content && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">
                    Lesson Content
                  </h3>
                  <div className="prose max-w-none">
                    <Viewer content={currentLesson.lesson.content} />
                  </div>
                </div>
              )}

              {/* Course Description */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  About This Course
                </h3>
                <div className="prose max-w-none">
                  <Viewer content={course.description} />
                </div>
              </div>

              {/* What You'll Learn */}
              {course.whatYouWillLearn &&
                course.whatYouWillLearn.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">
                      What You'll Learn
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {course.whatYouWillLearn.map((objective, index) => (
                        <div key={index} className="flex items-start gap-2">
                          <svg
                            className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                          <span className="text-gray-700">{objective}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

              {/* Course Stats */}
              {/* <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-6 border-t">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {course.sections.length}
                  </div>
                  <div className="text-sm text-gray-600">Sections</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {course.sections.reduce(
                      (acc, section) => acc + section.lessons.length,
                      0
                    )}
                  </div>
                  <div className="text-sm text-gray-600">Lessons</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {course.duration || "N/A"}
                  </div>
                  <div className="text-sm text-gray-600">Duration</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {downloadableFiles.length}
                  </div>
                  <div className="text-sm text-gray-600">Downloads</div>
                </div>
              </div> */}
            </div>
          </TabsContent>

          <TabsContent value="files" className="p-6 mt-0">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Downloadable Files
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  Download course materials, resources, and supplementary files.
                </p>
              </div>

              {downloadableFiles.length > 0 ? (
                <DownloadableFilesList files={downloadableFiles} />
              ) : (
                <div className="text-center py-8">
                  <Download className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    No downloadable files
                  </h4>
                  <p className="text-gray-600">
                    This course doesn't have any downloadable files yet.
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
