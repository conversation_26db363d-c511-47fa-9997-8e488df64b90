"use client";

import {
  PublicCourseDetails,
  PublicSection,
  PublicLesson,
} from "@/types/course";
import { VideoPlayer } from "@/components/video-player";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  ChevronLeft,
  ChevronRight,
  Play,
  Loader2,
  FileText,
} from "lucide-react";
import { useState, useEffect } from "react";

interface CourseVideoSectionProps {
  course: PublicCourseDetails;
  currentLesson: {
    section: PublicSection;
    lesson: PublicLesson;
  } | null;
  onLessonChange: (lesson: { sectionId: string; lessonId: string }) => void;
}

export function CourseVideoSection({
  course,
  currentLesson,
  onLessonChange,
}: CourseVideoSectionProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [videoLoading, setVideoLoading] = useState(false);
  const [videoKey, setVideoKey] = useState(0); // Force video player re-mount

  // Get all lessons in order for navigation
  const getAllLessons = () => {
    const lessons: Array<{
      sectionId: string;
      lessonId: string;
      section: PublicSection;
      lesson: PublicLesson;
    }> = [];

    course.sections.forEach((section) => {
      section.lessons.forEach((lesson) => {
        lessons.push({
          sectionId: section._id,
          lessonId: lesson._id,
          section,
          lesson,
        });
      });
    });

    return lessons.sort((a, b) => {
      if (a.section.order !== b.section.order) {
        return a.section.order - b.section.order;
      }
      return a.lesson.order - b.lesson.order;
    });
  };

  const allLessons = getAllLessons();
  const currentIndex = currentLesson
    ? allLessons.findIndex(
        (l) =>
          l.sectionId === currentLesson.section._id &&
          l.lessonId === currentLesson.lesson._id
      )
    : -1;

  const canGoPrevious = currentIndex > 0;
  const canGoNext = currentIndex < allLessons.length - 1;

  const handlePrevious = () => {
    if (canGoPrevious && !isLoading) {
      setIsLoading(true);
      setVideoLoading(true);
      const prevLesson = allLessons[currentIndex - 1];
      onLessonChange({
        sectionId: prevLesson.sectionId,
        lessonId: prevLesson.lessonId,
      });
    }
  };

  const handleNext = () => {
    if (canGoNext && !isLoading) {
      setIsLoading(true);
      setVideoLoading(true);
      const nextLesson = allLessons[currentIndex + 1];
      onLessonChange({
        sectionId: nextLesson.sectionId,
        lessonId: nextLesson.lessonId,
      });
    }
  };

  // Auto-play first lesson if none selected
  useEffect(() => {
    if (!currentLesson && allLessons.length > 0) {
      const firstLesson = allLessons[0];
      onLessonChange({
        sectionId: firstLesson.sectionId,
        lessonId: firstLesson.lessonId,
      });
    }
  }, [currentLesson, allLessons, onLessonChange]);

  // Handle lesson changes and video loading
  useEffect(() => {
    if (currentLesson) {
      setVideoLoading(true);
      // Force video player re-mount by changing key
      setVideoKey((prev) => prev + 1);

      // Reset loading states after video has time to load
      const timer = setTimeout(() => {
        setIsLoading(false);
        setVideoLoading(false);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [currentLesson?.lesson._id]); // Only trigger when lesson ID changes

  if (!currentLesson) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="flex flex-col items-center gap-4">
            <Play className="w-16 h-16 text-gray-400" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Select a lesson to start learning
              </h3>
              <p className="text-gray-600">
                Choose a lesson from the curriculum to begin watching
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-0">
        {/* Video Player */}
        <div className="relative aspect-video bg-black">
          {videoLoading && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10">
              <div className="text-center text-white">
                <Loader2 className="w-8 h-8 mx-auto mb-2 animate-spin" />
                <p className="text-sm">Loading video...</p>
              </div>
            </div>
          )}

          {currentLesson.lesson.video ? (
            <VideoPlayer
              key={videoKey} // Force re-mount when lesson changes
              autoPlay={true} // Auto-play when lesson changes
              onVideoLoad={() => {
                // Video has loaded, hide loading state
                setVideoLoading(false);
              }}
              sources={[
                {
                  label: "HD",
                  src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${currentLesson.lesson.video.key}`,
                },
                {
                  label: "720p",
                  src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${currentLesson.lesson.video.key}?quality=720`,
                },
                {
                  label: "480p",
                  src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${currentLesson.lesson.video.key}?quality=480`,
                },
                {
                  label: "360p",
                  src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${currentLesson.lesson.video.key}?quality=360`,
                },
              ]}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-white">
                <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg">No video available for this lesson</p>
                <p className="text-sm opacity-75 mt-2">
                  This lesson contains text content only. Check the description
                  tab below for lesson materials.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Video Controls */}
        <div className="p-4 bg-white border-t">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-medium text-gray-900">
                {currentLesson.lesson.title}
              </h3>
              <p className="text-sm text-gray-600">
                {currentLesson.section.title} • Lesson{" "}
                {currentLesson.lesson.order}
                {currentLesson.lesson.duration && (
                  <span>
                    {" "}
                    • {Math.ceil(currentLesson.lesson.duration / 60)} min
                  </span>
                )}
              </p>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevious}
                disabled={!canGoPrevious || isLoading}
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleNext}
                disabled={!canGoNext || isLoading}
              >
                Next
                <ChevronRight className="w-4 h-4 ml-1" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
