"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useAddToCartHelper, useCartData } from "@/contexts/cart-context";
import { Loader2, ShoppingCart, Check } from "lucide-react";

interface EnrollButtonProps {
  courseId: string;
  publicId: string;
  courseTitle: string;
  price: number;
  discountPrice?: number | null;
  isFree?: boolean;
  thumbnail?: string;
}

export function EnrollButton({
  courseId,
  publicId,
  courseTitle,
  price,
  discountPrice,
  isFree = false,
  thumbnail,
}: EnrollButtonProps) {
  const { toast } = useToast();
  const router = useRouter();

  // Local loading state for this specific component
  const [isLocallyAddingToCart, setIsLocallyAddingToCart] = useState(false);

  // Cart context hooks
  const { cart } = useCartData();
  const { addCourseToCartAsync, addToCartError } = useAddToCartHelper();

  // Check if course is in cart by comparing the course ID with cart item's itemId
  const isInCart =
    cart?.items.some((item) => {
      return item.itemId === courseId;
    }) || false;

  const handleAddToCart = async () => {
    if (!courseId) {
      toast({
        title: "Error",
        description: "Course ID is missing.",
        variant: "destructive",
      });
      return;
    }

    setIsLocallyAddingToCart(true);
    try {
      await addCourseToCartAsync(courseId, publicId, 1);
      toast({
        title: "Added to cart",
        description: `${courseTitle} has been added to your cart.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLocallyAddingToCart(false);
    }
  };

  const handleBuyNow = async () => {
    if (!courseId) {
      toast({
        title: "Error",
        description: "Course ID is missing.",
        variant: "destructive",
      });
      return;
    }

    try {
      // If course is NOT in cart, redirect directly to checkout without adding to cart
      if (!isInCart) {
        router.push("/checkout");
      } else {
        // If course IS in cart, redirect to checkout
        router.push("/checkout");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to navigate to checkout. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAccessNow = () => {
    router.push(`/courses/${publicId}`);
  };

  return (
    <div className="w-full">
      {isFree ? (
        <Button
          className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          variant="default"
          onClick={handleAccessNow}
        >
          Access Now
        </Button>
      ) : (
        <div className="flex gap-2">
          <Button
            className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            variant="default"
            onClick={handleAddToCart}
            disabled={isInCart || isLocallyAddingToCart}
          >
            {isLocallyAddingToCart ? (
              <div className="flex items-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Adding...</span>
              </div>
            ) : isInCart ? (
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4" />
                <span>In Cart</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <ShoppingCart className="h-4 w-4" />
                <span>Add to Cart</span>
              </div>
            )}
          </Button>
          <Button
            className="flex-1 py-3 px-6 rounded-lg font-medium"
            variant="outline"
            onClick={handleBuyNow}
            disabled={isLocallyAddingToCart}
          >
            {isLocallyAddingToCart ? (
              <div className="flex items-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Adding...</span>
              </div>
            ) : (
              "Buy now"
            )}
          </Button>
        </div>
      )}

      {/* Error display */}
      {addToCartError && (
        <div className="mt-2">
          <p className="text-sm text-red-600">{addToCartError.message}</p>
        </div>
      )}
    </div>
  );
}
