"use client";

import { DownloadableFile } from "@/types/course";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Download, FileText, File, Image, Video, Archive } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface DownloadableFilesListProps {
  files: DownloadableFile[];
}

export function DownloadableFilesList({ files }: DownloadableFilesListProps) {
  const [downloadingFiles, setDownloadingFiles] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  // Get file icon based on file type
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'pdf':
      case 'doc':
      case 'docx':
      case 'txt':
        return <FileText className="w-5 h-5" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
        return <Image className="w-5 h-5" />;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return <Video className="w-5 h-5" />;
      case 'zip':
      case 'rar':
      case '7z':
        return <Archive className="w-5 h-5" />;
      default:
        return <File className="w-5 h-5" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  // Handle file download
  const handleDownload = async (file: DownloadableFile) => {
    if (downloadingFiles.has(file._id)) return;

    setDownloadingFiles(prev => new Set(prev).add(file._id));

    try {
      // Create download URL
      const downloadUrl = `${process.env.NEXT_PUBLIC_AWS_FILES_CLOUDFRONT_URL}/${file.file.key}`;
      
      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = file.name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Download started",
        description: `${file.name} is being downloaded.`,
      });
    } catch (error) {
      console.error('Download error:', error);
      toast({
        title: "Download failed",
        description: "There was an error downloading the file. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDownloadingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(file._id);
        return newSet;
      });
    }
  };

  if (files.length === 0) {
    return null;
  }

  return (
    <div className="space-y-3">
      {files.map((file) => (
        <Card key={file._id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 flex-1 min-w-0">
                <div className="text-gray-500 flex-shrink-0">
                  {getFileIcon(file.name)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 truncate">
                    {file.name}
                  </h4>
                  
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm text-gray-500">
                      {formatFileSize(file.size)}
                    </span>
                    
                    {file.downloadCount !== undefined && (
                      <>
                        <span className="text-gray-300">•</span>
                        <span className="text-sm text-gray-500">
                          {file.downloadCount} downloads
                        </span>
                      </>
                    )}
                  </div>
                  
                  {file.description && (
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {file.description}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2 flex-shrink-0">
                {/* File type badge */}
                <Badge variant="secondary" className="text-xs">
                  {file.name.split('.').pop()?.toUpperCase() || 'FILE'}
                </Badge>
                
                {/* Download button */}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDownload(file)}
                  disabled={downloadingFiles.has(file._id)}
                  className="gap-2"
                >
                  <Download className="w-4 h-4" />
                  {downloadingFiles.has(file._id) ? 'Downloading...' : 'Download'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
