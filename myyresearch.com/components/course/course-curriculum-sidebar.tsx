"use client";

import { PublicLesson, PublicCourseResponse } from "@/types/course";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Play,
  PlayCircle,
  CheckCircle,
  Clock,
  FileText,
  ChevronDown,
  ChevronRight,
  Loader2,
} from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface CourseCurriculumSidebarProps {
  course: PublicCourseResponse;
  currentLesson: {
    sectionId: string;
    lessonId: string;
  } | null;
  onLessonSelect: (lesson: { sectionId: string; lessonId: string }) => void;
}

export function CourseCurriculumSidebar({
  course,
  currentLesson,
  onLessonSelect,
}: CourseCurriculumSidebarProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(course.sections.map((s) => s._id))
  );
  const [loadingLesson, setLoadingLesson] = useState<string | null>(null);

  // Calculate course progress (mock data - in real app this would come from API)
  const totalLessons = course.sections.reduce(
    (acc, section) => acc + section.lessons.length,
    0
  );
  const completedLessons = 0; // This would be tracked in real app
  const progressPercentage =
    totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;

  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  };

  const isLessonCurrent = (sectionId: string, lessonId: string) => {
    return (
      currentLesson?.sectionId === sectionId &&
      currentLesson?.lessonId === lessonId
    );
  };

  const handleLessonSelect = (sectionId: string, lessonId: string) => {
    setLoadingLesson(lessonId);
    onLessonSelect({ sectionId, lessonId });

    // Clear loading state after a delay
    setTimeout(() => {
      setLoadingLesson(null);
    }, 1500);
  };

  const getLessonIcon = (lesson: PublicLesson) => {
    if (lesson.video) {
      return <PlayCircle className="w-4 h-4" />;
    }
    return <FileText className="w-4 h-4" />;
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return "";
    const minutes = Math.ceil(seconds / 60);
    return `${minutes} min`;
  };

  return (
    <div className="space-y-4">
      {/* Course Curriculum */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Course Content</CardTitle>
          <p className="text-sm text-gray-600">
            {course.sections.length} sections • {totalLessons} lessons
          </p>
        </CardHeader>
        <CardContent className="p-2">
          <div className="space-y-1">
            {course.sections.map((section, sectionIndex) => (
              <div key={section._id}>
                {/* Section Header */}
                <Button
                  variant="ghost"
                  className="w-full justify-between p-3 h-auto font-medium text-left"
                  onClick={() => toggleSection(section._id)}
                >
                  <div className="flex items-center gap-2">
                    {expandedSections.has(section._id) ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                    <span className="truncate">
                      {sectionIndex + 1}: {section.title}
                    </span>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {section.lessons.length}
                  </Badge>
                </Button>

                {/* Section Lessons */}
                {expandedSections.has(section._id) && (
                  <div className="ml-4 border-l border-gray-200">
                    {section.lessons.map((lesson, lessonIndex) => (
                      <Button
                        key={lesson._id}
                        variant="ghost"
                        className={cn(
                          "w-full justify-start p-3 h-auto text-left border-l-2 border-transparent ",
                          isLessonCurrent(section._id, lesson._id) &&
                            "bg-blue-50 border-l-blue-500 text-blue-700"
                        )}
                        onClick={() =>
                          handleLessonSelect(section._id, lesson._id)
                        }
                        disabled={loadingLesson === lesson._id}
                      >
                        <div className="flex items-start gap-3 w-full">
                          <div className="flex-shrink-0 mt-0.5">
                            {loadingLesson === lesson._id ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              getLessonIcon(lesson)
                            )}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-sm font-medium truncate">
                                {lessonIndex + 1}. {lesson.title}
                              </span>
                              {lesson.isFree && (
                                <Badge variant="outline" className="text-xs">
                                  Free
                                </Badge>
                              )}
                            </div>

                            <div className="flex items-center gap-2 text-xs text-gray-500">
                              {lesson.duration && (
                                <div className="flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  {formatDuration(lesson.duration)}
                                </div>
                              )}

                              {lesson.video && (
                                <div className="flex items-center gap-1">
                                  <Play className="w-3 h-3" />
                                  Video
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Completion status (mock) */}
                          <div className="flex-shrink-0">
                            {/* In real app, this would show actual completion status */}
                            {false && (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            )}
                          </div>
                        </div>
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
