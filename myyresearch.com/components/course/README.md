# Course Components

This directory contains components for the enhanced course viewing experience, specifically designed for purchased courses.

## Components Overview

### PurchasedCourseLayout
Main layout component that orchestrates the two-column layout for purchased courses.

**Features:**
- Two-column responsive layout
- Mobile-first design with proper ordering
- State management for current lesson
- Sticky sidebar on desktop

**Props:**
- `course: PublicCourseDetails` - The course data

### CourseVideoSection
Video player component with lesson navigation controls and automatic video switching.

**Features:**
- Video streaming with multiple quality options
- Previous/Next lesson navigation
- Auto-play first lesson and when switching lessons
- Automatic video source updating when lessons change
- Loading states and error handling during video switches
- Responsive video player
- Proper video player state reset between lessons
- Graceful handling of lessons without videos

**Props:**
- `course: PublicCourseDetails` - The course data
- `currentLesson` - Current lesson details
- `onLessonChange` - Callback for lesson changes

**Video Switching Logic:**
- Uses a `videoKey` state to force VideoPlayer re-mounting when lessons change
- Implements loading states during video transitions
- Auto-plays new videos when lessons are selected
- Resets video player state (time, duration, etc.) for each new lesson

### CourseContentTabs
Tabbed interface for course description and downloadable files.

**Features:**
- Two tabs: Description and Files
- Dynamic file count display
- Rich content rendering with Viewer component
- Course statistics display
- What you'll learn section

**Props:**
- `course: PublicCourseDetails` - The course data
- `currentLesson` - Current lesson details

### CourseCurriculumSidebar
Course content navigation sidebar with progress tracking and interactive lesson selection.

**Features:**
- Expandable/collapsible sections
- Progress tracking (mock implementation)
- Lesson type indicators (video/text)
- Current lesson highlighting
- Interactive lesson selection with loading states
- Visual feedback during lesson switches
- Responsive design

**Props:**
- `course: PublicCourseDetails` - The course data
- `currentLesson` - Current lesson selection
- `onLessonSelect` - Callback for lesson selection

**Interaction Features:**
- Click any lesson to switch video player content
- Loading spinner shows during lesson transitions
- Disabled state prevents multiple rapid clicks
- Visual highlighting of currently playing lesson

### DownloadableFilesList
Component for displaying and downloading course files.

**Features:**
- File type detection and icons
- File size formatting
- Download functionality
- Loading states for downloads
- File type badges

**Props:**
- `files: DownloadableFile[]` - Array of downloadable files

## Usage

The main course page (`/courses/[course]/page.tsx`) conditionally renders either:
1. `PurchasedCourseLayout` - For purchased courses
2. Regular course preview - For non-purchased courses

```tsx
if (course.isPurchased) {
  return (
    <>
      <PurchasedCourseLayout course={course} />
      <Toaster />
    </>
  );
}
```

## Data Flow

1. Course page fetches course details using `useCourseDetails` hook
2. Purchase status is determined by `course.isPurchased` field
3. If purchased, the enhanced layout is rendered
4. User interactions (lesson selection) flow through callback props
5. State is managed at the `PurchasedCourseLayout` level

## Video Switching Functionality

The video switching system ensures seamless transitions between lessons:

### How It Works:
1. **Lesson Selection**: User clicks a lesson in the curriculum sidebar
2. **Loading State**: Visual feedback shows the lesson is loading
3. **Video Player Reset**: VideoPlayer component is re-mounted with new `key`
4. **Source Update**: New video sources are passed to the VideoPlayer
5. **Auto-play**: New video automatically starts playing
6. **State Reset**: Video player state (time, duration) is reset for the new lesson

### Key Components:
- **VideoKey**: Forces VideoPlayer re-mounting when lessons change
- **Loading States**: Provides visual feedback during transitions
- **Auto-play**: Automatically starts new videos when selected
- **Error Handling**: Gracefully handles lessons without videos

### Technical Implementation:
```tsx
// Force video player re-mount
const [videoKey, setVideoKey] = useState(0);

// Update key when lesson changes
useEffect(() => {
  if (currentLesson) {
    setVideoKey(prev => prev + 1);
  }
}, [currentLesson?.lesson._id]);

// VideoPlayer with key prop
<VideoPlayer
  key={videoKey}
  autoPlay={true}
  sources={videoSources}
/>
```

## Responsive Design

- **Desktop (lg+)**: Two-column layout with sticky sidebar
- **Mobile**: Single column with curriculum first, then video, then tabs
- **Tablet**: Responsive breakpoints ensure optimal viewing

## File Downloads

Downloads are handled through CloudFront URLs:
- Files are served from `NEXT_PUBLIC_AWS_FILES_CLOUDFRONT_URL`
- Downloads trigger browser's native download functionality
- Progress feedback through toast notifications

## Future Enhancements

1. **Progress Tracking**: Implement real progress tracking with API
2. **Bookmarks**: Allow users to bookmark specific lessons
3. **Notes**: Add note-taking functionality
4. **Offline Support**: Cache videos for offline viewing
5. **Subtitles**: Add subtitle support to video player
6. **Speed Control**: Add playback speed controls
7. **Auto-advance**: Automatically advance to next lesson
8. **Completion Certificates**: Generate certificates on course completion
