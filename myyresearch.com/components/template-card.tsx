"use client";

import Image from "next/image";
import Link from "next/link";
import { Template, TemplateTag, MinimalisticTemplate } from "@/types/template";
import { Button } from "@/components/ui/button";
import { useAddToCartHelper, useCartData } from "@/contexts/cart-context";
import { useRouter } from "next/navigation";
import { Loader2, ShoppingCart, Check } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useState } from "react";

interface TemplateCardProps {
  template: Template | MinimalisticTemplate;
  onClick?: (template: Template | MinimalisticTemplate) => void;
  showButtons?: boolean;
}

export function TemplateCard({
  template,
  showButtons = true,
}: TemplateCardProps) {
  const router = useRouter();
  const { toast } = useToast();

  // Local loading state for this specific component
  const [isLocallyAddingToCart, setIsLocallyAddingToCart] = useState(false);

  // Cart context hooks
  const { cart } = useCartData();
  const { addTemplateToCartAsync, addToCartError } = useAddToCartHelper();

  // Get template ID safely
  const templateId = template._id || "";

  // Check if template is free
  const isFree =
    template.price === 0 ||
    (template.discountPrice !== undefined && template.discountPrice === 0);

  // Check if template is in cart by comparing the template ID with cart item's itemId
  const isInCart =
    cart?.items.some((item) => {
      return item.itemId === templateId;
    }) || false;

  const handleAddToCart = async () => {
    if (!templateId) {
      toast({
        title: "Error",
        description: "Template ID is missing.",
        variant: "destructive",
      });
      return;
    }

    setIsLocallyAddingToCart(true);
    try {
      await addTemplateToCartAsync(templateId, template.publicId, 1);
      toast({
        title: "Added to cart",
        description: `${template.title} has been added to your cart.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLocallyAddingToCart(false);
    }
  };

  const handleBuyNow = async () => {
    if (!templateId) {
      toast({
        title: "Error",
        description: "Template ID is missing.",
        variant: "destructive",
      });
      return;
    }

    try {
      // If template is NOT in cart, redirect directly to checkout without adding to cart
      if (!isInCart) {
        router.push("/checkout");
      } else {
        // If template IS in cart, redirect to checkout
        router.push("/checkout");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to navigate to checkout. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDownloadNow = () => {
    router.push(getTemplateUrl());
  };

  const {
    title,
    shortDescription,
    thumbnail,
    price,
    discountPrice,
    tag,
    mediaType,
    downloadCount,
  } = template;

  // Handle the different thumbnail structure between Template and MinimalisticTemplate

  // Handle the URL generation based on template type
  const getTemplateUrl = () => {
    if ("publicId" in template) {
      return `/templates/${template.publicId}`;
    }
    // if ("slug" in template) {
    //   return `/templates/${template.slug}`;
    // }
    return "/templates"; // Fallback to templates listing
  };

  return (
    <div className="group relative bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 cursor-pointer">
      <Link href={getTemplateUrl()}>
        {/* Thumbnail */}
        <div className="relative w-full overflow-hidden rounded-t-lg aspect-[16/9]">
          <Image
            src={`${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${thumbnail}`}
            alt={title}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>

        {/* Tag Badge */}
        {tag !== TemplateTag.NONE && (
          <div className="absolute top-2 right-2">
            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-primary-500 text-white">
              {tag.replace("-", " ")}
            </span>
          </div>
        )}

        {/* Content */}
        <div className="p-4">
          <div className="flex items-start justify-between gap-2">
            <h3 className="font-semibold text-gray-900 line-clamp-1">
              {title}
            </h3>
            <span className="text-xs text-gray-500">{mediaType}</span>
          </div>

          <p className="mt-1 text-sm text-gray-500 line-clamp-2">
            {shortDescription}
          </p>

          {/* Price and Downloads */}
          <div className="mt-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isFree ? (
                <span className="text-lg font-bold text-green-600">Free</span>
              ) : discountPrice ? (
                <>
                  <span className="text-lg font-bold text-primary-600">
                    ${discountPrice}
                  </span>
                  <span className="text-sm text-gray-400 line-through">
                    ${price}
                  </span>
                </>
              ) : (
                <span className="text-lg font-bold text-primary-600">
                  ${price}
                </span>
              )}
            </div>

            <div className="flex items-center gap-1 text-sm text-gray-500">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3v-13"
                />
              </svg>
              <span>{downloadCount}</span>
            </div>
          </div>
        </div>
      </Link>

      {/* Buttons remain outside the Link */}
      {showButtons && (
        <div className="mt-4 flex gap-2 p-4 pt-0">
          {isFree ? (
            <Button
              className="w-full"
              variant="default"
              onClick={handleDownloadNow}
            >
              Download Now
            </Button>
          ) : (
            <>
              <Button
                className="flex-1"
                variant="default"
                onClick={handleAddToCart}
                disabled={isInCart || isLocallyAddingToCart}
              >
                {isLocallyAddingToCart ? (
                  <div className="flex items-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Adding...</span>
                  </div>
                ) : isInCart ? (
                  <div className="flex items-center space-x-2">
                    <Check className="h-4 w-4" />
                    <span>In Cart</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <ShoppingCart className="h-4 w-4" />
                    <span>Add to Cart</span>
                  </div>
                )}
              </Button>
              <Button
                className="flex-1"
                variant="outline"
                onClick={handleBuyNow}
                disabled={isLocallyAddingToCart}
              >
                {isLocallyAddingToCart ? (
                  <div className="flex items-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Adding...</span>
                  </div>
                ) : (
                  "Buy now"
                )}
              </Button>
            </>
          )}
        </div>
      )}

      {/* Error display */}
      {addToCartError && (
        <div className="px-4 pb-4">
          <p className="text-sm text-red-600">{addToCartError.message}</p>
        </div>
      )}
    </div>
  );
}

export function TemplateCardSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Thumbnail Skeleton */}
      <div className="relative w-full aspect-video rounded-t-lg overflow-hidden">
        <div className="w-full h-full bg-gray-200 animate-pulse" />
      </div>

      {/* Content Skeleton */}
      <div className="p-4">
        {/* Title and Media Type */}
        <div className="flex items-start justify-between gap-2">
          <div className="h-6 w-2/3 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
        </div>

        {/* Description */}
        <div className="mt-1 space-y-2">
          <div className="h-4 w-full bg-gray-200 rounded animate-pulse" />
          <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />
        </div>

        {/* Price and Downloads */}
        <div className="mt-4 flex items-center justify-between">
          <div className="h-6 w-20 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Buttons Skeleton */}
      <div className="mt-4 flex gap-2 p-4 pt-0">
        <div className="h-10 flex-1 bg-gray-200 rounded animate-pulse" />
        <div className="h-10 flex-1 bg-gray-200 rounded animate-pulse" />
      </div>
    </div>
  );
}
