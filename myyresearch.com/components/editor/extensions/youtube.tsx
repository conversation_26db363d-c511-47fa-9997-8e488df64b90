import { mergeAttributes, Node, nodeInputRule } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";
import { YouTubeComponent } from "../components/youtube-component";
import Youtube from "@tiptap/extension-youtube";

export interface YouTubeOptions {
  addPasteHandler: boolean;
  allowFullscreen: boolean;
  controls: boolean;
  height: number;
  HTMLAttributes: Record<string, any>;
  inline: boolean;
  nocookie: boolean;
  width: number;
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    //@ts-ignore
    youtube: {
      /**
       * Add a YouTube video
       */
      setYoutubeVideo: (options: {
        src: string;
        width?: number;
        height?: number;
      }) => ReturnType;
    };
  }
}

export const YouTube = Youtube.configure({
  controls: false,
  nocookie: true,
  progressBarColor: "white",
});
