"use client";

import React, { useState } from "react";
import { ImageExtension } from "./extensions/image";
import { ImagePlaceholder } from "./extensions/image-placeholder";
import SearchAndReplace from "./extensions/search-and-replace";
import Heading from "@tiptap/extension-heading";
import Link from "@tiptap/extension-link";
import Subscript from "@tiptap/extension-subscript";
import Superscript from "@tiptap/extension-superscript";
import TextAlign from "@tiptap/extension-text-align";
import TextStyle from "@tiptap/extension-text-style";
import Underline from "@tiptap/extension-underline";
import StarterKit from "@tiptap/starter-kit";
import { EditorContent, useEditor } from "@tiptap/react";
import { ToolbarProvider } from "./providers/toolbar-provider";
import { AlignmentTooolbar } from "./toolbar-components/alignment";
import { BulletListToolbar } from "./toolbar-components/bullet-list";
import { ImageToolbar } from "./toolbar-components/image";
import {
  BoldToolbar,
  Italic<PERSON>oolbar,
  LinkToolbar,
  UnderlineToolbar,
} from "./toolbar-components/marks";
import { OrderedListToolbar } from "./toolbar-components/ordered-list";
import { RedoToolbar } from "./toolbar-components/redo";
import { SearchAndReplaceToolbar } from "./toolbar-components/search-and-replace-toolbar";
import { UndoToolbar } from "./toolbar-components/undo";
import { Separator } from "@/components/ui/separator";
import { useEffect } from "react";
import { HeadingToolbar } from "./toolbar-components/heading";
import { YouTube } from "./extensions/youtube";
import { YouTubeToolbar } from "./toolbar-components/youtube";
import HorizontalRule from "@tiptap/extension-horizontal-rule";
import { HorizontalRuleToolbar } from "./toolbar-components/horizontal-rule";
import Blockquote from "@tiptap/extension-blockquote";
import { BlockquoteToolbar } from "./toolbar-components/blockquote";
import TaskList from "@tiptap/extension-task-list";
import TaskItem from "@tiptap/extension-task-item";
import { TaskListToolbar } from "./toolbar-components/task-list";

const extensions = [
  StarterKit.configure({
    orderedList: {
      HTMLAttributes: {
        class: "list-decimal",
      },
    },
    bulletList: {
      HTMLAttributes: {
        class: "list-disc",
      },
    },
  }),
  Heading.configure({
    levels: [1, 2, 3, 4],
    HTMLAttributes: {
      class: "tiptap-heading",
    },
  }),
  TextAlign.configure({
    types: ["heading", "paragraph"],
  }),
  TextStyle,
  Subscript,
  Superscript,
  Underline,
  Link,
  ImageExtension,
  ImagePlaceholder,
  SearchAndReplace,
  YouTube,
  HorizontalRule,
  Blockquote,
  TaskList,
  TaskItem.configure({
    nested: true,
    HTMLAttributes: {
      class: "task-item",
    },
  }),
];

interface TiptapEditorProps {
  content: string;
}

const TiptapViewer: React.FC<TiptapEditorProps> = ({ content }) => {
  const editor = useEditor({
    extensions,
    content,
    editable: false,
  });
  // const [appliedContent, setAppliedContent] = useState(content);

  useEffect(() => {
    // const timer = setTimeout(() => {
    // }, 100);

    // return () => clearTimeout(timer);
    editor?.commands.setContent(content);
  }, [content, editor]);

  if (!editor) {
    return null;
  }

  return (
    <EditorContent
      className="outline-none focus:outline-none border-none "
      editor={editor}
    />
  );
};

export default TiptapViewer;
