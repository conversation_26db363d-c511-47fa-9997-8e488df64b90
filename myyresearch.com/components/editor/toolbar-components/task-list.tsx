"use client";

import { CheckSquare } from "lucide-react";
import React from "react";

import { cn } from "@/lib/utils";
import { useToolbar } from "../providers/toolbar-provider";
import { Button, type ButtonProps } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const TaskListToolbar = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, onClick, children, ...props }, ref) => {
    const { editor } = useToolbar();

    const handleToggleTaskList = () => {
      editor?.chain().focus().toggleTaskList().run();
    };

    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-8 w-8",
              editor?.isActive("taskList") && "bg-accent",
              className
            )}
            onClick={(e) => {
              e.preventDefault();
              handleToggleTaskList();
            }}
            ref={ref}
            {...props}
          >
            {children || <CheckSquare className="h-4 w-4" />}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <span>Task List</span>
        </TooltipContent>
      </Tooltip>
    );
  }
);

TaskListToolbar.displayName = "TaskListToolbar";

export { TaskListToolbar };
