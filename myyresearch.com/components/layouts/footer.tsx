import Image from "next/image";
import * as React from "react";
import { courseCategories, templateCategories } from "@/data";

const navigation = {
  courses: courseCategories.map((category) => ({
    name: category.name,
    href: `/courses?categories=${category.id}`,
  })),
  templates: templateCategories.map((category) => ({
    name: category.name,
    href: `/templates?categories=${category.id}`,
  })),
  company: [
    { name: "About Us", href: "/about-us" },
    { name: "Blog", href: "/blog" },
    { name: "Testimonials", href: "/testimonials" },
    { name: "Privacy", href: "/privacy-policy" },
    { name: "Terms", href: "/terms-conditions" },
  ],
  services: [
    { name: "Online Courses", href: "/online-courses" },
    { name: "Research Help", href: "/research-help" },
    { name: "Assignments Help", href: "/assignments-help" },
    { name: "Assignment Templates", href: "/assignment-templates" },
    { name: "Presentation Templates", href: "/presentation-templates" },
  ],
  social: [
    {
      name: "Facebook",
      href: "#",
      icon: (props: any) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path
            fillRule="evenodd"
            d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
            clipRule="evenodd"
          />
        </svg>
      ),
    },
    {
      name: "Instagram",
      href: "#",
      icon: (props: any) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path
            fillRule="evenodd"
            d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
            clipRule="evenodd"
          />
        </svg>
      ),
    },
    {
      name: "X",
      href: "#",
      icon: (props: any) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path d="M13.6823 10.6218L20.2391 3H18.6854L12.9921 9.61788L8.44486 3H3.2002L10.0765 13.0074L3.2002 21H4.75404L10.7663 14.0113L15.5685 21H20.8131L13.6819 10.6218H13.6823ZM11.5541 13.0956L10.8574 12.0991L5.31391 4.16971H7.70053L12.1742 10.5689L12.8709 11.5655L18.6861 19.8835H16.2995L11.5541 13.096V13.0956Z" />
        </svg>
      ),
    },

    {
      name: "YouTube",
      href: "#",
      icon: (props: any) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path
            fillRule="evenodd"
            d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z"
            clipRule="evenodd"
          />
        </svg>
      ),
    },
  ],
};

export default function Footer() {
  return (
    <div>
      <div className={"mt-[100px]"}></div>
      <footer
        className="bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900"
        aria-labelledby="footer-heading"
      >
        <h2 id="footer-heading" className="sr-only">
          Footer
        </h2>
        <div className="mx-auto container px-6 pb-12 pt-20 sm:pt-28 lg:px-8 lg:pt-32">
          <div className="xl:grid xl:grid-cols-3 xl:gap-12">
            <div className="space-y-8">
              <div className="transform transition hover:scale-105">
                <Image
                  src={"/logo/White_Logo.png"}
                  alt=""
                  width={170}
                  height={100}
                  className="brightness-110"
                />
              </div>
              <p className="text-base leading-6 text-gray-300 font-light max-w-md">
                Empowering Lifelong Learning through innovative research
                methodologies and comprehensive educational resources.
              </p>
              <div className="flex space-x-8">
                {navigation.social.map((item) => (
                  <a
                    key={item.name}
                    href={item.href}
                    className="text-gray-300 hover:text-white transform transition-all duration-200 hover:scale-110"
                  >
                    <span className="sr-only">{item.name}</span>
                    <item.icon className="h-7 w-7" aria-hidden="true" />
                  </a>
                ))}
              </div>
            </div>
            <div className="mt-16 grid grid-cols-2 gap-12 xl:col-span-2 xl:mt-0">
              <div className="md:grid md:grid-cols-2 md:gap-12">
                <div>
                  <h3 className="text-sm font-semibold leading-6 text-white uppercase tracking-wider">
                    Courses
                  </h3>
                  <ul role="list" className="mt-8 space-y-5">
                    {navigation.courses.map((item) => (
                      <li key={item.name}>
                        <a
                          href={item.href}
                          className="text-[15px] leading-6 text-gray-300 hover:text-white transition-colors duration-200 inline-block relative after:absolute after:bottom-0 after:left-0 after:h-[1px] after:w-0 after:bg-white after:transition-all hover:after:w-full"
                        >
                          {item.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className="mt-10 md:mt-0">
                  <h3 className="text-sm font-semibold leading-6 text-white uppercase tracking-wider">
                    Templates
                  </h3>
                  <ul role="list" className="mt-8 space-y-5">
                    {navigation.templates.map((item) => (
                      <li key={item.name}>
                        <a
                          href={item.href}
                          className="text-[15px] leading-6 text-gray-300 hover:text-white transition-colors duration-200 inline-block relative after:absolute after:bottom-0 after:left-0 after:h-[1px] after:w-0 after:bg-white after:transition-all hover:after:w-full"
                        >
                          {item.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              <div className="md:grid md:grid-cols-2 md:gap-12">
                <div className="mt-10 md:mt-0">
                  <h3 className="text-sm font-semibold leading-6 text-white uppercase tracking-wider">
                    Services
                  </h3>
                  <ul role="list" className="mt-8 space-y-5">
                    {navigation.services.map((item) => (
                      <li key={item.name}>
                        <a
                          href={item.href}
                          className="text-[15px] leading-6 text-gray-300 hover:text-white transition-colors duration-200 inline-block relative after:absolute after:bottom-0 after:left-0 after:h-[1px] after:w-0 after:bg-white after:transition-all hover:after:w-full"
                        >
                          {item.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h3 className="text-sm font-semibold leading-6 text-white uppercase tracking-wider">
                    Company
                  </h3>
                  <ul role="list" className="mt-8 space-y-5">
                    {navigation.company.map((item) => (
                      <li key={item.name}>
                        <a
                          href={item.href}
                          className="text-[15px] leading-6 text-gray-300 hover:text-white transition-colors duration-200 inline-block relative after:absolute after:bottom-0 after:left-0 after:h-[1px] after:w-0 after:bg-white after:transition-all hover:after:w-full"
                        >
                          {item.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-16 pt-8 sm:mt-20 lg:mt-24 border-t border-white/10">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <p className="text-sm leading-5 text-gray-400 hover:text-gray-300 transition-colors">
                &copy; 2025 Research Tec Consultancy LLC. All rights reserved.
              </p>
              <p className="text-sm leading-5 text-gray-400 hover:text-gray-300 transition-colors">
                Design and developed by{" "}
                <span className="text-gray-300">elenine</span>
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
