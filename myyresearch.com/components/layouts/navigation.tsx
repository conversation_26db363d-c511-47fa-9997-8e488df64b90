"use client";
import { Fragment, useEffect, useState, useRef } from "react";
import { <PERSON>alog, Popover, Tab, Transition } from "@headlessui/react";
import { RiMenu3Line, RiCloseLine } from "react-icons/ri";
import { Shopping<PERSON><PERSON>, Loader2, Trash2, User } from "lucide-react";
import * as React from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { FileText, BarChart3, Database, FileEdit } from "lucide-react";

import { cn } from "@/lib/utils";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/auth-contexts";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
// import { urlForImage } from "@/sanity/lib/image";
import { courseCategories, templateCategories } from "@/data/index";
import { useCartData, useCartOperations } from "@/contexts/cart-context";
import { getTokensFromSession } from "@/lib/authenticaton";
import { Role } from "@/types/auth";

function classNames(...classes: any) {
  return classes.filter(Boolean).join(" ");
}

const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a">
>(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  );
});
ListItem.displayName = "ListItem";

const ListItemImage = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a"> & { imageSrc: string }
>(({ className, title, children, imageSrc, ...props }, ref) => {
  return (
    <li className="group flex items-center space-x-4 p-3 rounded-md transition-all duration-300 hover:bg-accent hover:text-accent-foreground cursor-pointer">
      <Image
        src={imageSrc}
        alt={title || ""}
        width={100}
        height={100}
        className="w-16 h-16 rounded-md object-cover transition-transform duration-300 group-hover:scale-110"
      />
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 leading-none no-underline outline-none transition-colors duration-300",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none transition-colors duration-300 group-hover:text-accent-foreground">
            {title}
          </div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground transition-colors duration-300 group-hover:text-accent-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  );
});
ListItemImage.displayName = "ListItemImage";

const ListItemIcon = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a"> & { icon: React.ReactNode }
>(({ className, title, children, icon, ...props }, ref) => {
  return (
    <li className="group flex items-center space-x-4 p-3 rounded-md transition-all duration-300 hover:bg-accent hover:text-accent-foreground cursor-pointer">
      <div className="flex-shrink-0 transition-transform duration-300 group-hover:scale-110">
        {icon}
      </div>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 leading-none no-underline outline-none transition-colors duration-300",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none transition-colors duration-300 group-hover:text-accent-foreground">
            {title}
          </div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground transition-colors duration-300 group-hover:text-accent-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  );
});
ListItemIcon.displayName = "ListItemIcon";

const services: {
  title: string;
  href: string;
  description: string;
  imageSrc: string;
}[] = [
  {
    title: "Online Courses",
    href: "/online-courses",
    description: "Enhance knowledge with diverse courses.",
    imageSrc: "/images/services/online_courses.svg",
  },
  {
    title: "Research Help",
    href: "/research-help",
    description: "Expert guidance for your research.",
    imageSrc: "/images/services/research_help.svg",
  },
  {
    title: "Assignments Help",
    href: "/assignments-help",
    description: "Professional assistance for top assignments.",
    imageSrc: "/images/services/assignment_help.svg",
  },
  {
    title: "Assignment Templates",
    href: "/assignment-templates",
    description: "Professional templates for polished assignments.",
    imageSrc: "/images/services/assignment_templates.svg",
  },
  {
    title: "Presentation Templates",
    href: "/presentation-templates",
    description: "Eye-catching templates for standout presentations.",
    imageSrc: "/images/services/presentation_templates.svg",
  },
];

const businessServices: {
  title: string;
  href: string;
  description: string;
  imageSrc: string;
  icon: React.ReactNode;
}[] = [
  {
    title: "Business Research & Analysis",
    href: "/business-services/research-analysis",
    description: "Comprehensive research and analysis for your business",
    imageSrc: "/images/services/business_research.svg",
    icon: <BarChart3 className="h-10 w-10 text-black" />,
  },
  {
    title: "Tech & Data Services",
    href: "/business-services/tech-data",
    description: "Technical and data solutions for your business",
    imageSrc: "/images/services/tech_data.svg",
    icon: <Database className="h-10 w-10 text-black" />,
  },
  {
    title: "Business Document Templates",
    href: "/business-services/document-templates",
    description: "Professional templates for business documents",
    imageSrc: "/images/services/business_templates.svg",
    icon: <FileText className="h-10 w-10 text-black" />,
  },
  {
    title: "Content Writing & Marketing",
    href: "/business-services/content-marketing",
    description: "Professional content creation and marketing services",
    imageSrc: "/images/services/content_marketing.svg",
    icon: <FileEdit className="h-10 w-10 text-black" />,
  },
];

const CoursesCategory = {
  id: "education",
  name: "Courses",
  featured: [
    {
      name: "New Arrivals",
      href: "/courses?category=new-arrivals",
      imageSrc:
        "https://tailwindui.com/img/ecommerce-images/mega-menu-category-01.jpg",
      imageAlt:
        "Models sitting back to back, wearing Basic Tee in black and bone.",
    },
    {
      name: "Popular Courses",
      href: "/courses?category=popular-courses",
      imageSrc:
        "https://tailwindui.com/img/ecommerce-images/mega-menu-category-02.jpg",
      imageAlt:
        "Close up of Basic Tee fall bundle with off-white, ochre, olive, and black tees.",
    },
  ],
  sections: courseCategories.map((category) => ({
    id: category.id,
    name: category.name,
    items: category.subcategories.map((subcategory) => ({
      name: subcategory.name,
      href: `/courses?categories=${category.id}&subcategories=${subcategory.id}`,
    })),
  })),
};

const TemplatesCategory = {
  id: "templates",
  name: "Templates",
  featured: [
    {
      name: "New Arrivals",
      href: "/templates?category=new-arrivals",
      imageSrc:
        "https://tailwindui.com/img/ecommerce-images/mega-menu-category-01.jpg",
      imageAlt:
        "Models sitting back to back, wearing Basic Tee in black and bone.",
    },
    {
      name: "Popular Templates",
      href: "/templates?category=popular-templates",
      imageSrc:
        "https://tailwindui.com/img/ecommerce-images/mega-menu-category-02.jpg",
      imageAlt:
        "Close up of Basic Tee fall bundle with off-white, ochre, olive, and black tees.",
    },
  ],
  sections: templateCategories.map((category) => ({
    id: category.id,
    name: category.name,
    items: category.subcategories.map((subcategory) => ({
      name: subcategory.name,
      href: `/templates?categories=${category.id}&subcategories=${subcategory.id}`,
    })),
  })),
};

const navigation = {
  categories: [
    {
      id: "courses",
      name: "Courses",
      featured: [
        {
          name: "New Arrivals",
          href: "#",
          imageSrc:
            "https://tailwindui.com/img/ecommerce-images/mega-menu-category-01.jpg",
          imageAlt:
            "Models sitting back to back, wearing Basic Tee in black and bone.",
        },
        {
          name: "Basic Tees",
          href: "#",
          imageSrc:
            "https://tailwindui.com/img/ecommerce-images/mega-menu-category-02.jpg",
          imageAlt:
            "Close up of Basic Tee fall bundle with off-white, ochre, olive, and black tees.",
        },
      ],

      sections: courseCategories.map((category) => ({
        id: category.id,
        name: category.name,
        items: category.subcategories.map((subcategory) => ({
          name: subcategory.name,
          href: `/courses?categories=${category.id}&subcategories=${subcategory.id}`,
        })),
      })),
    },
    {
      id: "templates",
      name: "Templates",
      featured: [
        {
          name: "New Arrivals",
          href: "#",
          imageSrc:
            "https://tailwindui.com/img/ecommerce-images/mega-menu-category-01.jpg",
          imageAlt:
            "Models sitting back to back, wearing Basic Tee in black and bone.",
        },
        {
          name: "Basic Tees",
          href: "#",
          imageSrc:
            "https://tailwindui.com/img/ecommerce-images/mega-menu-category-02.jpg",
          imageAlt:
            "Close up of Basic Tee fall bundle with off-white, ochre, olive, and black tees.",
        },
      ],
      // sections: [
      //   {
      //     id: "quantitative",
      //     name: "Quantitative, Qualitative and Analytical Subjects",
      //     items: [
      //       { name: "Statistics", href: "#" },
      //       { name: "Data Science", href: "#" },
      //       { name: "Business Analytics", href: "#" },
      //       { name: "Research Methodology", href: "#" },
      //       { name: "Mathematics", href: "#" },
      //     ],
      //   },
      //   {
      //     id: "business-management",
      //     name: "Business and Management",
      //     items: [
      //       { name: "Management", href: "#" },
      //       { name: "Economics", href: "#" },
      //       { name: "Marketing", href: "#" },
      //       { name: "Business", href: "#" },
      //       { name: "Finance & Accounting", href: "#" },
      //     ],
      //   },
      //   {
      //     id: "technology-design",
      //     name: "Technology and Design",
      //     items: [
      //       { name: "Information Technology (IT)", href: "#" },
      //       { name: "Design", href: "#" },
      //       { name: "Computing", href: "#" },
      //       { name: "Computer Science", href: "#" },
      //     ],
      //   },
      //   {
      //     id: "humanities-social-sciences",
      //     name: "Humanities and Social Sciences",
      //     items: [
      //       { name: "Health", href: "#" },
      //       { name: "Psychology", href: "#" },
      //       { name: "Tourism", href: "#" },
      //       { name: "Teaching and Academics", href: "#" },
      //       { name: "Writing", href: "#" },
      //       { name: "Humanities", href: "#" },
      //       { name: "Social Science", href: "#" },
      //     ],
      //   },
      // ],
      sections: templateCategories.map((category) => ({
        id: category.id,
        name: category.name,
        items: category.subcategories.map((subcategory) => ({
          name: subcategory.name,
          href: `/templates?categories=${category.id}&subcategories=${subcategory.id}`,
        })),
      })),
    },
  ],
  pages: [
    { name: "Contact Us", href: "/contact-us" },
    { name: "About Us", href: "/about-us" },
    { name: "Blog", href: "/blog" },
  ],
};

export default function Navigation(props: { className?: string }) {
  const [open, setOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(true);
  const [isShoppingCartSideBarOpen, setIsShoppingCartSideBarOpen] =
    useState(false);

  // Cart context hooks
  const {
    cart,
    totalItems,
    totalAmount,
    isLoading: cartLoading,
  } = useCartData();
  const { removeFromCart, isRemovingFromCart } = useCartOperations();
  const router = useRouter();

  const { user, loading, logout } = useAuth();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 0) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const handleSignOut = async () => {
    try {
      await logout();
      router.push("/"); // Redirect to home page after logout
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const handleMyAccountClick = async () => {
    try {
      // Check if user is authenticated and has role information
      if (!user || !user.role) {
        // If not authenticated, redirect to sign-in
        router.push("/auth/sign-in");
        return;
      }

      // Role-based navigation
      if (user.role === Role.User) {
        // Regular users go to /account
        router.push("/account");
      } else if (user.role === Role.Instructor) {
        // Instructors go to admin portal with tokens
        const tokens = await getTokensFromSession();

        if (tokens && tokens.accessToken && tokens.refreshToken) {
          // Validate tokens are not empty strings
          if (
            tokens.accessToken.trim() === "" ||
            tokens.refreshToken.trim() === ""
          ) {
            console.error("Invalid tokens: empty token values");
            router.push("/account");
            return;
          }

          // Security consideration: Use environment variable for admin portal URL
          const adminPortalBaseUrl =
            process.env.NEXT_PUBLIC_ADMIN_PORTAL_URL ||
            "https://app.myyresearch.com";
          const adminPortalUrl = `${adminPortalBaseUrl}/login?accessToken=${encodeURIComponent(
            tokens.accessToken
          )}&refreshToken=${encodeURIComponent(tokens.refreshToken)}`;

          // Security consideration: Validate URL length to prevent potential issues
          if (adminPortalUrl.length > 2048) {
            console.error(
              "Generated URL is too long, potential security issue"
            );
            router.push("/account");
            return;
          }

          // Open admin portal in same window
          window.location.href = adminPortalUrl;
        } else {
          // If tokens are not available, redirect to regular account page as fallback
          console.error(
            "Unable to retrieve tokens for instructor - tokens are null or incomplete"
          );
          router.push("/account");
        }
      } else {
        // For any other roles (admin, etc.), default to account page
        console.log(
          `Unhandled role: ${user.role}, redirecting to account page`
        );
        router.push("/account");
      }
    } catch (error) {
      console.error("Error handling My Account click:", error);
      // Fallback to account page on error
      router.push("/account");
    }
  };

  return (
    <>
      <div className={props.className}>
        {/* Mobile menu */}
        <Transition.Root show={open} as={Fragment}>
          <Dialog
            as="div"
            className="relative z-50 lg:hidden"
            onClose={setOpen}
          >
            <Transition.Child
              as={Fragment}
              enter="transition-opacity ease-linear duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="transition-opacity ease-linear duration-300"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black bg-opacity-25" />
            </Transition.Child>

            <div className="fixed inset-0 z-40 flex">
              <Transition.Child
                as={Fragment}
                enter="transition ease-in-out duration-300 transform"
                enterFrom="-translate-x-full"
                enterTo="translate-x-0"
                leave="transition ease-in-out duration-300 transform"
                leaveFrom="translate-x-0"
                leaveTo="-translate-x-full"
              >
                <Dialog.Panel className="relative flex w-full max-w-xs flex-col overflow-y-auto bg-white pb-12 shadow-xl">
                  <div className="flex px-4 pb-2 pt-5">
                    <button
                      type="button"
                      className="relative -m-2 inline-flex items-center justify-center rounded-md p-2 text-gray-400"
                      onClick={() => setOpen(false)}
                    >
                      <span className="absolute -inset-0.5" />
                      <span className="sr-only">Close menu</span>
                      <RiCloseLine className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>

                  {/* Links */}
                  <Tab.Group as="div" className="mt-2">
                    <div className="border-b border-gray-200">
                      <Tab.List className="-mb-px flex space-x-8 px-4">
                        {navigation.categories.map((category) => (
                          <Tab
                            key={category.name}
                            className={({ selected }) =>
                              classNames(
                                selected
                                  ? "border-indigo-600 text-indigo-600"
                                  : "border-transparent text-gray-900",
                                "flex-1 whitespace-nowrap border-b-2 px-1 py-4 text-base font-medium"
                              )
                            }
                          >
                            {category.name}
                          </Tab>
                        ))}
                      </Tab.List>
                    </div>
                    <Tab.Panels as={Fragment}>
                      {navigation.categories.map((category) => (
                        <Tab.Panel
                          key={category.name}
                          className="space-y-10 px-4 pb-8 pt-10"
                        >
                          {/* <div className="grid grid-cols-2 gap-x-4">
                            {category.featured.map((item) => (
                              <div
                                key={item.name}
                                className="group relative text-sm"
                              >
                                <div className="aspect-h-1 aspect-w-1 overflow-hidden rounded-lg bg-gray-100 group-hover:opacity-75">
                                  <img
                                    src={item.imageSrc}
                                    alt={item.imageAlt}
                                    className="object-cover object-center"
                                  />
                                </div>
                                <a
                                  href={item.href}
                                  className="mt-6 block font-medium text-gray-900"
                                >
                                  <span
                                    className="absolute inset-0 z-10"
                                    aria-hidden="true"
                                  />
                                  {item.name}
                                </a>
                                <p aria-hidden="true" className="mt-1">
                                  Shop now
                                </p>
                              </div>
                            ))}
                          </div> */}
                          {category.sections.map((section) => (
                            <div key={section.name}>
                              <p
                                id={`${category.id}-${section.id}-heading-mobile`}
                                className="font-medium text-gray-900"
                              >
                                {section.name}
                              </p>
                              <ul
                                role="list"
                                aria-labelledby={`${category.id}-${section.id}-heading-mobile`}
                                className="mt-6 flex flex-col space-y-6"
                              >
                                {section.items.map((item) => (
                                  <li key={item.name} className="flow-root">
                                    <a
                                      href={item.href}
                                      className="-m-2 block p-2 text-gray-500"
                                    >
                                      {item.name}
                                    </a>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          ))}
                        </Tab.Panel>
                      ))}
                    </Tab.Panels>
                  </Tab.Group>

                  <div className="space-y-6 border-t border-gray-200 px-4 py-6">
                    {navigation.pages.map((page) => (
                      <div key={page.name} className="flow-root">
                        <a
                          href={page.href}
                          className="-m-2 block p-2 font-medium text-gray-900"
                        >
                          {page.name}
                        </a>
                      </div>
                    ))}
                  </div>

                  <div className="space-y-6 border-t border-gray-200 px-4 py-6">
                    <div className="flow-root">
                      <a
                        href="/auth/sign-in"
                        className="-m-2 block p-2 font-medium text-gray-900"
                      >
                        Sign in
                      </a>
                    </div>
                    <div className="flow-root">
                      <a
                        href="/auth/sign-up"
                        className="-m-2 block p-2 font-medium text-gray-900"
                      >
                        Create account
                      </a>
                    </div>
                  </div>

                  {/* <div className="border-t border-gray-200 px-4 py-6">
                    <a href="#" className="-m-2 flex items-center p-2">
                      <img
                        src="https://tailwindui.com/img/flags/flag-canada.svg"
                        alt=""
                        className="block h-auto w-5 flex-shrink-0"
                      />
                      <span className="ml-3 block text-base font-medium text-gray-900">
                        CAD
                      </span>
                      <span className="sr-only">, change currency</span>
                    </a>
                  </div> */}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </Dialog>
        </Transition.Root>

        <header
          className={`w-full z-40 bg-white ${isScrolled ? "shadow-lg" : ""}`}
        >
          <nav aria-label="Top" className="mx-auto container px-4 lg:px-8">
            <div className="flex h-[80px] items-center">
              {/* Mobile menu button and Logo */}
              <div className="flex items-center">
                <button
                  type="button"
                  className="relative rounded-md bg-white p-2 text-black lg:hidden"
                  onClick={() => setOpen(true)}
                >
                  <span className="absolute -inset-0.5" />
                  <span className="sr-only">Open menu</span>
                  <RiMenu3Line className="h-6 w-6" aria-hidden="true" />
                </button>

                <div className="ml-2 flex-initial lg:ml-0">
                  <Link href="/">
                    <span className="sr-only">Your Company</span>
                    <img
                      src="/logo/Logo.png"
                      alt=""
                      width={140}
                      height={100}
                      // className="lg:w-[170px]"
                    />
                  </Link>
                </div>
              </div>

              {/* Navigation Items - positioned to the right of logo and left-aligned */}
              <div className="flex-1 flex items-center justify-between">
                <NavigationMenu className="hidden lg:ml-8 lg:block">
                  <NavigationMenuList>
                    <NavigationMenuItem>
                      <NavigationMenuTrigger>Company</NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <ul className="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                          <li className="row-span-3">
                            <NavigationMenuLink asChild>
                              <Link
                                className="flex h-full w-full select-none flex-col justify-end rounded-md bg-primary p-6 no-underline outline-none focus:shadow-md text-white"
                                href="/"
                              >
                                <div className="flex flex-col items-start">
                                  <img
                                    src="/logo/Logo.png"
                                    alt="My Research Logo"
                                    width={120}
                                    height={70}
                                    className="mb-4 brightness-0 invert"
                                  />

                                  <p className="text-sm leading-tight text-white/80">
                                    My Research - Your Smart Start
                                  </p>
                                </div>
                              </Link>
                            </NavigationMenuLink>
                          </li>
                          <ListItem href="/about-us" title="About Us">
                            We offer personalized support, practical resources,
                            and innovative tools to help individuals and
                            businesses reach their full potential.
                          </ListItem>
                          <ListItem href="/testimonials" title="Testimonials">
                            Hear from our clients about their experiences with
                            us.
                          </ListItem>
                          <ListItem href="/contact-us" title="Contact Us">
                            Reach <NAME_EMAIL> for any
                            inquiries.
                          </ListItem>
                        </ul>
                      </NavigationMenuContent>
                    </NavigationMenuItem>
                    <NavigationMenuItem>
                      <NavigationMenuTrigger>Courses</NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <div className=" w-[400px] md:w-[500px] lg:w-[1000px]">
                          <div className="relative bg-white">
                            <div className="mx-auto max-w-7xl px-8">
                              <div className="py-8">
                                <div className="flex justify-between items-center pb-6 border-b mb-6">
                                  <div>
                                    <h3 className="text-lg font-bold text-gray-900">
                                      Our Courses
                                    </h3>
                                    <p className="text-sm text-gray-600 mt-1">
                                      Explore our comprehensive range of courses
                                    </p>
                                  </div>
                                  <a
                                    href="/courses"
                                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                  >
                                    View All
                                  </a>
                                </div>
                                <div className="grid grid-cols-3 gap-x-8 gap-y-10 text-sm">
                                  {CoursesCategory.sections.map((section) => (
                                    <div key={section.name}>
                                      <p
                                        id={`${section.name}-heading`}
                                        className="font-bold text-gray-900"
                                      >
                                        {section.name}
                                      </p>
                                      <ul
                                        role="list"
                                        aria-labelledby={`${section.name}-heading`}
                                        className="mt-6 space-y-6 sm:mt-4 sm:space-y-4"
                                      >
                                        {section.items.map((item) => (
                                          <li key={item.name} className="flex">
                                            <a
                                              href={item.href}
                                              className="hover:text-gray-800"
                                            >
                                              {item.name}
                                            </a>
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </NavigationMenuContent>
                    </NavigationMenuItem>
                    <NavigationMenuItem>
                      <NavigationMenuTrigger>Templates</NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <div className=" w-[400px] md:w-[500px] lg:w-[1000px]">
                          <div className="relative bg-white">
                            <div className="mx-auto max-w-7xl px-8">
                              <div className="py-8">
                                <div className="flex justify-between items-center pb-6 border-b mb-6">
                                  <div>
                                    <h3 className="text-lg font-bold text-gray-900">
                                      Our Templates
                                    </h3>
                                    <p className="text-sm text-gray-600 mt-1">
                                      Discover our collection of ready-to-use
                                      templates
                                    </p>
                                  </div>
                                  <a
                                    href="/templates"
                                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                  >
                                    View All
                                  </a>
                                </div>
                                <div className="grid grid-cols-3 gap-x-8 gap-y-10 text-sm">
                                  {TemplatesCategory.sections.map((section) => (
                                    <div key={section.name}>
                                      <p
                                        id={`${section.name}-heading`}
                                        className="font-bold text-gray-900"
                                      >
                                        {section.name}
                                      </p>
                                      <ul
                                        role="list"
                                        aria-labelledby={`${section.name}-heading`}
                                        className="mt-6 space-y-6 sm:mt-4 sm:space-y-4"
                                      >
                                        {section.items.map((item) => (
                                          <li key={item.name} className="flex">
                                            <a
                                              href={item.href}
                                              className="hover:text-gray-800"
                                            >
                                              {item.name}
                                            </a>
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </NavigationMenuContent>
                    </NavigationMenuItem>

                    <NavigationMenuItem>
                      <NavigationMenuTrigger>
                        Business Services
                      </NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <div className="w-[400px] md:w-[500px] lg:w-[600px]">
                          <div className="p-4">
                            <div className="flex justify-between items-center pb-4 border-b mb-4">
                              <div>
                                <h3 className="text-lg font-bold text-gray-900">
                                  Business Services
                                </h3>
                                <p className="text-sm text-gray-600 mt-1">
                                  Professional services to support your business
                                  needs
                                </p>
                              </div>
                              <a
                                href="/contact-us"
                                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                              >
                                Contact Us
                              </a>
                            </div>
                            <ul className="grid gap-3 md:grid-cols-2">
                              {businessServices.map((service) => (
                                <ListItemIcon
                                  key={service.title}
                                  title={service.title}
                                  href={service.href}
                                  icon={service.icon}
                                >
                                  {service.description}
                                </ListItemIcon>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </NavigationMenuContent>
                    </NavigationMenuItem>

                    <NavigationMenuItem>
                      <NavigationMenuTrigger>
                        Academic Services
                      </NavigationMenuTrigger>
                      <NavigationMenuContent>
                        <div className="w-[400px] md:w-[500px] lg:w-[600px]">
                          <div className="p-4">
                            <div className="flex justify-between items-center pb-4 border-b mb-4">
                              <div>
                                <h3 className="text-lg font-bold text-gray-900">
                                  Academic Services
                                </h3>
                                <p className="text-sm text-gray-600 mt-1">
                                  Professional services to support your research
                                </p>
                              </div>
                              <a
                                href="/contact-us"
                                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                              >
                                Contact Us
                              </a>
                            </div>
                            <ul className="grid gap-3 md:grid-cols-2">
                              {services.map((service) => (
                                <ListItemImage
                                  key={service.title}
                                  title={service.title}
                                  href={service.href}
                                  imageSrc={service.imageSrc}
                                >
                                  {service.description}
                                </ListItemImage>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </NavigationMenuContent>
                    </NavigationMenuItem>

                    <NavigationMenuItem>
                      <Link href="/blog" legacyBehavior passHref>
                        <NavigationMenuLink
                          className={navigationMenuTriggerStyle()}
                        >
                          Blog
                        </NavigationMenuLink>
                      </Link>
                    </NavigationMenuItem>
                  </NavigationMenuList>
                </NavigationMenu>
              </div>

              <div className="flex items-center space-x-2 lg:space-x-4 lg:pr-5">
                {/* Mobile My Account Icon */}
                <div className="lg:hidden">
                  <button
                    onClick={handleMyAccountClick}
                    disabled={loading}
                    className="group -m-2 flex items-center p-2 relative outline-none disabled:opacity-50"
                  >
                    {loading ? (
                      <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                    ) : (
                      <User className="h-6 w-6 flex-shrink-0 text-gray-700 group-hover:text-gray-500" />
                    )}
                    <span className="sr-only">My Account</span>
                  </button>
                </div>

                {/* Mobile Cart - Right aligned at far end */}
                <Popover className="lg:hidden relative">
                  {({ close }) => (
                    <>
                      <Popover.Button className="group -m-2 flex items-center p-2 relative outline-none">
                        <ShoppingCart className="h-6 w-6 flex-shrink-0 text-gray-700 group-hover:text-gray-500" />
                        {cartLoading ? (
                          <Loader2 className="absolute -top-1 -right-1 h-4 w-4 animate-spin text-gray-500" />
                        ) : totalItems > 0 ? (
                          <Badge
                            variant="default"
                            className="absolute -top-1 -right-1 flex items-center justify-center rounded-full w-5 h-5 text-xs"
                          >
                            {totalItems > 99 ? "99+" : totalItems}
                          </Badge>
                        ) : null}
                        <span className="sr-only">items in cart, view bag</span>
                      </Popover.Button>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-150"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                      >
                        <Popover.Panel className="absolute right-0 top-16 mt-px bg-white pb-6 shadow-lg px-2 w-80 rounded-lg ring-1 ring-black ring-opacity-5 z-50">
                          <div className="mx-auto max-w-2xl px-4">
                            <h2 className="sr-only">Shopping Cart</h2>
                            {cartLoading ? (
                              <div className="py-6 text-center">
                                <Loader2 className="h-6 w-6 animate-spin mx-auto text-gray-500" />
                                <p className="mt-2 text-gray-500">
                                  Loading cart...
                                </p>
                              </div>
                            ) : totalItems === 0 ? (
                              <p className="py-6 text-center text-gray-500">
                                Your cart is empty
                              </p>
                            ) : (
                              <>
                                <ScrollArea className="h-[300px] w-full">
                                  <ul
                                    role="list"
                                    className="divide-y divide-gray-200"
                                  >
                                    {cart?.items.map((cartItem) => (
                                      <li
                                        key={cartItem._id}
                                        className="flex items-center py-6"
                                      >
                                        <Image
                                          src={
                                            cartItem.thumbnail
                                              ? `${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${cartItem.thumbnail}`
                                              : "/placeholder-image.jpg"
                                          }
                                          alt={cartItem.title}
                                          width={64}
                                          height={64}
                                          className="h-16 w-16 flex-none rounded-md border border-gray-200 object-cover"
                                        />
                                        <div className="ml-4 flex-auto">
                                          <h4 className="font-medium text-gray-900">
                                            {cartItem.title}
                                          </h4>
                                          <p className="text-gray-500">
                                            $
                                            {cartItem.discountPrice ||
                                              cartItem.price}
                                          </p>
                                        </div>
                                        <button
                                          onClick={() =>
                                            removeFromCart(cartItem._id)
                                          }
                                          disabled={isRemovingFromCart}
                                          className="text-sm font-medium text-red-600 hover:text-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                                        >
                                          {isRemovingFromCart ? (
                                            <Loader2 className="h-3 w-3 animate-spin" />
                                          ) : (
                                            <Trash2 className="h-3 w-3" />
                                          )}
                                          <span>Remove</span>
                                        </button>
                                      </li>
                                    ))}
                                  </ul>
                                </ScrollArea>
                                <div className="mt-6">
                                  <div className="flex justify-between text-base font-medium text-gray-900">
                                    <p>Subtotal ({totalItems} items)</p>
                                    <p>${totalAmount.toFixed(2)}</p>
                                  </div>
                                  <p className="mt-0.5 text-sm text-gray-500">
                                    Shipping and taxes calculated at checkout.
                                  </p>
                                  <Button
                                    className="w-full mt-6"
                                    onClick={() => {
                                      close();
                                      // You can add navigation logic here if needed
                                    }}
                                  >
                                    <Link href="/checkout">Checkout</Link>
                                  </Button>
                                </div>
                              </>
                            )}
                          </div>
                        </Popover.Panel>
                      </Transition>
                    </>
                  )}
                </Popover>

                <Popover className="hidden lg:block ml-4 text-sm lg:relative lg:ml-8">
                  {({ close }) => (
                    <>
                      <Popover.Button className="group -m-2 flex items-center p-2 relative outline-none mr-1 lg:mr-2">
                        <ShoppingCart className="h-6 w-6 flex-shrink-0 text-gray-700 group-hover:text-gray-500" />
                        {cartLoading ? (
                          <Loader2 className="absolute -top-1 -right-1 h-4 w-4 animate-spin text-gray-500" />
                        ) : totalItems > 0 ? (
                          <Badge
                            variant="default"
                            className="absolute -top-1 -right-1 flex items-center justify-center rounded-full w-5 h-5 text-xs"
                          >
                            {totalItems > 99 ? "99+" : totalItems}
                          </Badge>
                        ) : null}
                        <span className="sr-only">items in cart, view bag</span>
                      </Popover.Button>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-200"
                        enterFrom="opacity-0 translate-y-1"
                        enterTo="opacity-100 translate-y-0"
                        leave="transition ease-in duration-150"
                        leaveFrom="opacity-100 translate-y-0"
                        leaveTo="opacity-0 translate-y-1"
                      >
                        <Popover.Panel className="absolute inset-x-0 top-16 mt-px bg-white pb-6 shadow-lg sm:px-2 lg:left-auto lg:right-0 lg:top-full lg:-mr-1.5 lg:mt-3 lg:w-80 lg:rounded-lg lg:ring-1 lg:ring-black lg:ring-opacity-5">
                          <div className="mx-auto max-w-2xl px-4">
                            <h2 className="sr-only">Shopping Cart</h2>
                            {cartLoading ? (
                              <div className="py-6 text-center">
                                <Loader2 className="h-6 w-6 animate-spin mx-auto text-gray-500" />
                                <p className="mt-2 text-gray-500">
                                  Loading cart...
                                </p>
                              </div>
                            ) : totalItems === 0 ? (
                              <p className="py-6 text-center text-gray-500">
                                Your cart is empty
                              </p>
                            ) : (
                              <>
                                <ScrollArea className="h-[300px] w-full">
                                  <ul
                                    role="list"
                                    className="divide-y divide-gray-200"
                                  >
                                    {cart?.items.map((cartItem) => (
                                      <li
                                        key={cartItem._id}
                                        className="flex items-center py-6"
                                      >
                                        <Image
                                          src={
                                            cartItem.thumbnail
                                              ? `${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${cartItem.thumbnail}`
                                              : "/placeholder-image.jpg"
                                          }
                                          alt={cartItem.title}
                                          width={64}
                                          height={64}
                                          className="h-16 w-16 flex-none rounded-md border border-gray-200 object-cover"
                                        />
                                        <div className="ml-4 flex-auto">
                                          <h4 className="font-medium text-gray-900">
                                            {cartItem.title}
                                          </h4>
                                          <p className="text-gray-500">
                                            $
                                            {cartItem.discountPrice ||
                                              cartItem.price}
                                          </p>
                                        </div>
                                        <button
                                          onClick={() =>
                                            removeFromCart(cartItem._id)
                                          }
                                          disabled={isRemovingFromCart}
                                          className="text-sm font-medium text-red-600 hover:text-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                                        >
                                          {isRemovingFromCart ? (
                                            <Loader2 className="h-3 w-3 animate-spin" />
                                          ) : (
                                            <Trash2 className="h-3 w-3" />
                                          )}
                                          <span>Remove</span>
                                        </button>
                                      </li>
                                    ))}
                                  </ul>
                                </ScrollArea>
                                <div className="mt-6">
                                  <div className="flex justify-between text-base font-medium text-gray-900">
                                    <p>Subtotal ({totalItems} items)</p>
                                    <p>${totalAmount.toFixed(2)}</p>
                                  </div>
                                  <p className="mt-0.5 text-sm text-gray-500">
                                    Shipping and taxes calculated at checkout.
                                  </p>
                                  <Button
                                    className="w-full mt-6"
                                    onClick={() => {
                                      close();
                                      // You can add navigation logic here if needed
                                    }}
                                  >
                                    <Link href="/checkout">Checkout</Link>
                                  </Button>
                                </div>
                              </>
                            )}
                          </div>
                        </Popover.Panel>
                      </Transition>
                    </>
                  )}
                </Popover>

                <div className="hidden lg:block">
                  <Button
                    variant="outline"
                    onClick={handleMyAccountClick}
                    disabled={loading}
                  >
                    My Account
                  </Button>
                </div>

                {/* Contact Us Button - right-aligned */}
                <div className="hidden lg:block">
                  <Link href="/contact-us">
                    <Button
                      variant="default"
                      className="rounded-md font-medium bg-primary hover:bg-primary/90 text-white shadow-sm transition-all duration-200 px-6 py-2 h-10"
                    >
                      Contact Us
                    </Button>
                  </Link>
                </div>

                {/* {user ? (
                  <Popover className="relative">
                    <Popover.Button className="flex items-center space-x-2 focus:outline-none">
                      <Avatar>
                        <AvatarImage
                          src={user.avatar || "/default-avatar.png"}
                          alt="User avatar"
                        />
                        <AvatarFallback>
                          {user.username?.charAt(0) || "U"}
                        </AvatarFallback>
                      </Avatar>
                      <span className="hidden lg:inline-block">
                        {user.username}
                      </span>
                    </Popover.Button>
                    <Transition
                      as={Fragment}
                      enter="transition ease-out duration-200"
                      enterFrom="opacity-0 translate-y-1"
                      enterTo="opacity-100 translate-y-0"
                      leave="transition ease-in duration-150"
                      leaveFrom="opacity-100 translate-y-0"
                      leaveTo="opacity-0 translate-y-1"
                    >
                      <Popover.Panel className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <Link
                          href="/account"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          Account
                        </Link>
                        <button
                          onClick={handleSignOut}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          Sign out
                        </button>
                      </Popover.Panel>
                    </Transition>
                  </Popover>
                ) : (
                  <>
                    <Link
                      href="/auth/sign-in"
                      className="hidden lg:inline-flex items-center"
                    >
                      <Button
                        variant="ghost"
                        className="rounded-md font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200"
                      >
                        Sign in
                      </Button>
                    </Link>
                    <Link
                      href="/auth/sign-up"
                      className="inline-flex items-center"
                    >
                      <Button
                        variant="default"
                        className="rounded-md font-medium bg-primary hover:bg-primary/90 text-white shadow-sm transition-all duration-200 px-6 py-2 h-10"
                      >
                        Sign up
                      </Button>
                    </Link>
                  </>
                )} */}
                {/* 
                <>
                  <Link
                    href="/auth/sign-in"
                    className="hidden lg:inline-flex items-center"
                  >
                    <Button
                      variant="ghost"
                      className="rounded-md font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200"
                    >
                      Sign in
                    </Button>
                  </Link>
                  <Link
                    href="/auth/sign-up"
                    className="inline-flex items-center"
                  >
                    <Button
                      variant="default"
                      className="rounded-md font-medium bg-primary hover:bg-primary/90 text-white shadow-sm transition-all duration-200 px-6 py-2 h-10"
                    >
                      Sign up
                    </Button>
                  </Link>
                </> */}
              </div>
            </div>
          </nav>
        </header>
      </div>
    </>
  );
}
