import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function TemplateCardSkeleton() {
  return (
    <Card className="h-[400px]">
      <CardHeader className="space-y-2">
        {/* Thumbnail skeleton */}
        <Skeleton className="w-full h-48" />
        {/* Title skeleton */}
        <Skeleton className="h-6 w-4/5" />
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Description skeleton */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-4/5" />
        </div>
        {/* Price skeleton */}
        <div className="flex items-center space-x-2">
          <Skeleton className="h-5 w-16" />
          <Skeleton className="h-4 w-12" />
        </div>
        {/* Rating and duration skeleton */}
        <div className="flex items-center space-x-3">
          <Skeleton className="h-4 w-12" />
          <Skeleton className="h-4 w-20" />
        </div>
      </CardContent>
    </Card>
  );
}
