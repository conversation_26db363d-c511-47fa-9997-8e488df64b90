import { DocumentTextIcon } from "@sanity/icons";
import { defineField, defineType } from "sanity";

const team = defineField({
  name: "team",
  type: "object",
  title: "Team Page",
  fields: [
    defineField({
      name: "team",
      type: "array",
      of: [
        defineField({
          name: "member",
          type: "object",
          title: "Team Member",
          fields: [
            defineField({
              name: "name",
              type: "string",
              title: "Name",
              validation: (Rule: any) =>
                Rule.custom((name: any, context: any) => {
                  console.log(context.document.page);
                  if (context.document.page === "team") {
                    if (!name) {
                      return "Name is required for team members";
                    }
                  }
                  return true;
                }),
            }),
            defineField({
              name: "role",
              type: "string",
              title: "Role",
              validation: (Rule: any) =>
                Rule.custom((role: any, context: any) => {
                  if (context.document.page === "team") {
                    if (!role) {
                      return "Role is required for team members";
                    }
                  }
                  return true;
                }),
            }),
            defineField({
              name: "image",
              type: "image",
              title: "Image",
              options: {
                hotspot: true,
              },
              validation: (Rule: any) =>
                Rule.custom((image: any, context: any) => {
                  if (context.document.page === "team") {
                    if (!image) {
                      return "Image is required for team members";
                    }
                  }
                  return true;
                }),
            }),
          ],
        }),
      ],
    }),
  ],
  hidden: ({ parent }) => parent?.page !== "team",
  validation: (Rule: any) =>
    Rule.custom((data: any, context: any) => {
      if (context.document.page === "team") {
        if (!data?.team || data.team.length === 0) {
          return "At least one team member is required for the Team Page";
        }
      }
      return true;
    }),
});

const homePage = defineField({
  name: "homePage",
  type: "object",
  title: "Home Page",
  fields: [
    defineField({
      name: "hero",
      type: "object",
      title: "Hero Section",
      fields: [
        defineField({
          name: "image",
          type: "image",
          title: "Hero Image",
          options: {
            hotspot: true,
          },
          validation: (Rule: any) =>
            Rule.custom((image: any, context: any) => {
              if (context.document.page === "homePage") {
                if (!image) {
                  return "Hero Image is required for the Home Page";
                }
              }
              return true;
            }),
        }),
        defineField({
          name: "title",
          type: "string",
          title: "Hero Title",
          validation: (Rule: any) =>
            Rule.custom((title: any, context: any) => {
              if (context.document.page === "homePage") {
                if (!title) {
                  return "Hero Title is required for the Home Page";
                }
              }
              return true;
            }),
        }),
        defineField({
          name: "description",
          type: "text",
          title: "Hero Description",
          validation: (Rule: any) =>
            Rule.custom((description: any, context: any) => {
              if (context.document.page === "homePage") {
                if (!description) {
                  return "Hero Description is required for the Home Page";
                }
              }
              return true;
            }),
        }),
      ],
    }),
    defineField({
      name: "features",
      type: "array",
      of: [
        defineField({
          name: "feature",
          type: "object",
          title: "Feature",
          fields: [
            defineField({
              name: "title",
              type: "string",
              title: "Feature Title",
              validation: (Rule: any) =>
                Rule.custom((title: any, context: any) => {
                  if (context.document.page === "homePage") {
                    if (!title) {
                      return "Feature Title is required for the Home Page";
                    }
                  }
                  return true;
                }),
            }),
            defineField({
              name: "description",
              type: "text",
              title: "Feature Description",
              validation: (Rule: any) =>
                Rule.custom((description: any, context: any) => {
                  if (context.document.page === "homePage") {
                    if (!description) {
                      return "Feature Description is required for the Home Page";
                    }
                  }
                  return true;
                }),
            }),
          ],
        }),
      ],
    }),
    defineField({
      name: "cta",
      type: "object",
      title: "Call to Action",
      fields: [
        defineField({
          name: "title",
          type: "string",
          title: "Call to Action Title",
          validation: (Rule: any) =>
            Rule.custom((title: any, context: any) => {
              if (context.document.page === "homePage") {
                if (!title) {
                  return "Call to Action Title is required for the Home Page";
                }
              }
              return true;
            }),
        }),
        defineField({
          name: "description",
          type: "text",
          title: "Call to Action Description",
          validation: (Rule: any) =>
            Rule.custom((description: any, context: any) => {
              if (context.document.page === "homePage") {
                if (!description) {
                  return "Call to Action Description is required for the Home Page";
                }
              }
              return true;
            }),
        }),
        defineField({
          name: "buttonText",
          type: "string",
          title: "Call to Action Button Text",
          validation: (Rule: any) =>
            Rule.custom((buttonText: any, context: any) => {
              if (context.document.page === "homePage") {
                if (!buttonText) {
                  return "Call to Action Button Text is required for the Home Page";
                }
              }
              return true;
            }),
        }),
        defineField({
          name: "buttonLink",
          type: "url",
          title: "Call to Action Button Link",
          validation: (Rule: any) =>
            Rule.custom((buttonLink: any, context: any) => {
              if (context.document.page === "homePage") {
                if (!buttonLink) {
                  return "Call to Action Button Link is required for the Home Page";
                }
              }
              return true;
            }),
        }),
      ],
    }),
    // newsletter
    defineField({
      name: "newsletter",
      type: "object",
      title: "Newsletter",
      fields: [
        defineField({
          name: "title",
          type: "string",
          title: "Newsletter Title",
          validation: (Rule: any) =>
            Rule.custom((title: any, context: any) => {
              if (context.document.page === "homePage") {
                if (!title) {
                  return "Newsletter Title is required for the Home Page";
                }
              }
              return true;
            }),
        }),
        defineField({
          name: "description",
          type: "text",
          title: "Newsletter Description",
          validation: (Rule: any) =>
            Rule.custom((description: any, context: any) => {
              if (context.document.page === "homePage") {
                if (!description) {
                  return "Newsletter Description is required for the Home Page";
                }
              }
              return true;
            }),
        }),
      ],
    }),
    // founderMessage
    defineField({
      name: "founderMessage",
      type: "object",
      title: "Founder Message",
      fields: [
        defineField({
          name: "title",
          type: "string",
          title: "Founder Message Title",
          validation: (Rule: any) =>
            Rule.custom((title: any, context: any) => {
              if (context.document.page === "homePage") {
                if (!title) {
                  return "Founder Message Title is required for the Home Page";
                }
              }
              return true;
            }),
        }),
        defineField({
          name: "description",
          type: "text",
          title: "Founder Message Description",
          validation: (Rule: any) =>
            Rule.custom((description: any, context: any) => {
              if (context.document.page === "homePage") {
                if (!description) {
                  return "Founder Message Description is required for the Home Page";
                }
              }
              return true;
            }),
        }),
      ],
    }),
  ],
  hidden: ({ parent }) => parent?.page !== "homePage",
  validation: (Rule: any) =>
    Rule.custom((data: any, context: any) => {
      if (context.document.page === "homePage") {
        if (
          !data.hero?.image ||
          !data.hero?.title ||
          !data.hero?.description ||
          !data.features ||
          data.features.length === 0 ||
          !data.cta?.title ||
          !data.cta?.description ||
          !data.cta?.buttonText ||
          !data.cta?.buttonLink ||
          !data.newsletter?.title ||
          !data.newsletter?.description ||
          !data.founderMessage?.title ||
          !data.founderMessage?.description
        ) {
          return "All sections and their fields are required for the Home Page";
        }
      }
      return true;
    }),
});

const galleryPage = defineField({
  name: "galleryPage",
  type: "object",
  title: "Gallery Page",
  fields: [
    defineField({
      name: "gallery",
      type: "array",
      of: [
        defineField({
          name: "image",
          type: "image",
          fields: [
            defineField({
              name: "alt",
              type: "string",
              title: "Alternative Text",
              validation: (Rule: any) =>
                Rule.custom((alt: any, context: any) => {
                  if (context.document.page === "gallery") {
                    if (!alt) {
                      return "Alternative Text is required for gallery images";
                    }
                  }
                  return true;
                }),
            }),
          ],
        }),
      ],
    }),
  ],
  hidden: ({ parent }) => parent?.page !== "gallery",
  validation: (Rule: any) =>
    Rule.custom((data: any, context: any) => {
      if (context.document.page === "gallery") {
        if (!data?.gallery || data.gallery.length === 0) {
          return "At least one image is required for the Gallery Page";
        }
      }
      return true;
    }),
});

const blockContentPage = defineField({
  name: "body",
  type: "blockContent",
  validation: (Rule: any) =>
    Rule.custom((body: any, context: any) => {
      if (
        ["privacyPolicyPage", "termsAndConditionsPage"].includes(
          context.document.page
        )
      ) {
        if (!body || body.length === 0) {
          return "Content is required for this page";
        }
      }
      return true;
    }),
  hidden: ({ parent }) =>
    !["privacyPolicyPage", "termsAndConditionsPage"].includes(parent?.page),
});

const faqPage = defineField({
  name: "faqPage",
  type: "object",
  title: "FAQ Page",
  fields: [
    defineField({
      name: "faq",
      type: "array",
      of: [
        defineField({
          name: "faqItem",
          type: "object",
          title: "FAQ Item",
          fields: [
            defineField({
              name: "question",
              type: "string",
              title: "Question",
              validation: (Rule: any) =>
                Rule.custom((question: any, context: any) => {
                  if (context.document.page === "faq") {
                    if (!question) {
                      return "Question is required for FAQ items";
                    }
                  }
                  return true;
                }),
            }),
            defineField({
              name: "answer",
              type: "text",
              title: "Answer",
              validation: (Rule: any) =>
                Rule.custom((answer: any, context: any) => {
                  if (context.document.page === "faq") {
                    if (!answer) {
                      return "Answer is required for FAQ items";
                    }
                  }
                  return true;
                }),
            }),
          ],
        }),
      ],
    }),
  ],
  hidden: ({ parent }) => parent?.page !== "faq",
  validation: (Rule: any) =>
    Rule.custom((data: any, context: any) => {
      if (context.document.page === "faq") {
        if (!data?.faq || data.faq.length === 0) {
          return "At least one FAQ item is required for the FAQ Page";
        }
      }
      return true;
    }),
});

const testimonialsPage = defineField({
  name: "testimonialsPage",
  type: "object",
  title: "Testimonials Page",
  fields: [
    defineField({
      name: "testimonials",
      type: "array",
      of: [
        defineField({
          name: "testimonial",
          type: "object",
          title: "Testimonial",
          fields: [
            defineField({
              name: "name",
              type: "string",
              title: "Name",
              validation: (Rule: any) =>
                Rule.custom((name: any, context: any) => {
                  if (context.document.page === "testimonials") {
                    if (!name) {
                      return "Name is required for testimonials";
                    }
                  }
                  return true;
                }),
            }),
            defineField({
              name: "role",
              type: "string",
              title: "Role",
              validation: (Rule: any) =>
                Rule.custom((role: any, context: any) => {
                  if (context.document.page === "testimonials") {
                    if (!role) {
                      return "Role is required for testimonials";
                    }
                  }
                  return true;
                }),
            }),
            defineField({
              name: "testimonial",
              type: "text",
              title: "Testimonial",
              validation: (Rule: any) =>
                Rule.custom((testimonial: any, context: any) => {
                  if (context.document.page === "testimonials") {
                    if (!testimonial) {
                      return "Testimonial is required for testimonials";
                    }
                  }
                  return true;
                }),
            }),
            defineField({
              name: "image",
              type: "image",
              title: "Image",
              options: {
                hotspot: true,
              },
              validation: (Rule: any) =>
                Rule.custom((image: any, context: any) => {
                  if (context.document.page === "testimonials") {
                    if (!image) {
                      return "Image is required for testimonials";
                    }
                  }
                  return true;
                }),
            }),
          ],
        }),
      ],
    }),
  ],
  hidden: ({ parent }) => parent?.page !== "testimonials",
  validation: (Rule: any) =>
    Rule.custom((data: any, context: any) => {
      if (context.document.page === "testimonials") {
        if (!data?.testimonials || data.testimonials.length === 0) {
          return "At least one testimonial is required for the Testimonials Page";
        }
      }
      return true;
    }),
});

export const webPageType = defineType({
  name: "webPage",
  title: "Web Page",
  type: "document",
  icon: DocumentTextIcon,
  fields: [
    defineField({
      title: "Select Page",
      name: "page",
      type: "string",
      options: {
        list: [
          // testimonials
          { title: "Testimonials", value: "testimonials" },
          { title: "FAQ", value: "faq" },
          // home page
          { title: "Home Page", value: "homePage" },
          // about page
          { title: "About Page", value: "aboutPage" },
          // contact page
          { title: "Contact Page", value: "contactPage" },
          // privacy policy page
          { title: "Privacy Policy Page", value: "privacyPolicyPage" },
          {
            title: "Terms and Conditions Page",
            value: "termsAndConditionsPage",
          }, // Add this line
          { title: "Gallery Page", value: "gallery" },
          //homePage
          { title: "Home Page", value: "homePage" },
          { title: "Team", value: "team" },
        ], // <-- predefined values
      },
    }),
    blockContentPage,
    faqPage,
    testimonialsPage,
    galleryPage,
    homePage,
    team,
    // defineField({
    //   name: "content",
    //   type: "privacyPolicyType",
    // }),
  ],
  preview: {
    select: {
      title: "page",
    },
  },
});
