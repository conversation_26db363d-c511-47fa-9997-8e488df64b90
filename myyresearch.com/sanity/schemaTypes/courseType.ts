import { defineField, defineType } from "sanity";
import { FaGraduationCap } from "react-icons/fa";
import { courseCategories, coursesLevels, tags } from "../../data/index"; // Add this import

export const courseType = defineType({
  name: "course",
  title: "Courses",
  type: "document",
  icon: FaGraduationCap,
  fields: [
    defineField({
      name: "title",
      title: "Title",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "slug",
      type: "slug",
      options: {
        source: "title",
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "rating",
      title: "Rating",
      type: "number",
      validation: (Rule) => Rule.required().min(1).max(5),
    }),
    defineField({
      name: "ratingCount",
      title: "Rating Count",
      type: "number",
      validation: (Rule) => Rule.required(),
    }),
    //value
    defineField({
      name: "value",
      title: "Value",
      type: "number",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "shortDescription",
      title: "Short Description",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "thumbnail",
      title: "Thumbnail",
      type: "image",
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: "alt",
          type: "string",
          title: "Alternative text",
        },
      ],
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "description",
      title: "Description",
      type: "blockContent",
    }),
    defineField({
      name: "price",
      title: "Price",
      type: "number",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "discountPrice",
      title: "Discount Price",
      type: "number",
    }),
    defineField({
      name: "duration",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "level",
      type: "string",
      options: {
        list: coursesLevels,
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "category",
      title: "Category",
      type: "string",
      options: {
        list: courseCategories.map((category) => ({
          title: category.name,
          value: category.id,
        })),
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "subcategory",
      title: "Subcategory",
      type: "string",
      options: {
        list: courseCategories.flatMap((category) =>
          category.subcategories.map((subcategory) => ({
            title: subcategory.name,
            value: subcategory.id,
          }))
        ),
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "udemyUrl",
      title: "Udemy URL",
      type: "url",
      validation: (Rule) =>
        Rule.required().uri({
          scheme: ["http", "https"],
        }),
    }),
    defineField({
      name: "tag",
      title: "Tag",
      type: "string",
      options: {
        list: tags,
        layout: "radio",
      },
      initialValue: "None",
    }),
  ],
  preview: {
    select: {
      title: "title",
      media: "thumbnail",
    },
  },
});
