import { define<PERSON><PERSON>y<PERSON><PERSON>ber, defineField, defineType } from "sanity";
import { FaFileAlt } from "react-icons/fa";
import { tags, templateCategories, templateMediaTypes } from "../../data/index"; // Add this import

export const templateType = defineType({
  name: "template",
  title: "Template",
  type: "document",
  icon: FaFileAlt,
  fields: [
    defineField({
      name: "title",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "slug",
      type: "slug",
      options: {
        source: "title",
      },
      validation: (Rule) => Rule.required(),
    }),
    // value
    defineField({
      name: "value",
      title: "Value",
      type: "number",
      validation: (Rule) => Rule.required(),
    }),
    // rating
    defineField({
      name: "rating",
      title: "Rating",
      type: "number",
      validation: (Rule) => Rule.required(),
    }),
    // numReviews
    defineField({
      name: "ratingCount",
      title: "Rating Count",
      type: "number",
      validation: (Rule) => Rule.required(),
    }),

    defineField({
      name: "image",
      title: "Image",
      type: "image",
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: "alt",
          type: "string",
          title: "Alternative text",
        },
      ],
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "shortDescription",
      title: "Short Description",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "description",
      title: "Description",
      type: "blockContent",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "category",
      title: "Category",
      type: "string",
      options: {
        list: templateCategories.map((category) => ({
          title: category.name,
          value: category.id,
        })),
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "subcategory",
      title: "Subcategory",
      type: "string",
      options: {
        list: templateCategories.flatMap((category) =>
          category.subcategories.map((subcategory) => ({
            title: subcategory.name,
            value: subcategory.id,
          }))
        ),
      },
      validation: (Rule) => Rule.required(),
    }),

    defineField({
      name: "price",
      title: "Price",
      type: "number",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "discountPrice",
      title: "Discount Price",
      type: "number",
    }),
    defineField({
      name: "templateMediaType",
      title: "Template Media Type",
      type: "string",
      options: {
        list: templateMediaTypes,
      },
      validation: (Rule) => Rule.required(),
    }),
    // version
    defineField({
      name: "version",
      title: "Version",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "tag",
      title: "Tag",
      type: "string",
      options: {
        list: tags,
        layout: "radio",
      },
      initialValue: "None",
    }),
  ],
  preview: {
    select: {
      title: "title",
      media: "image",
    },
  },
});
