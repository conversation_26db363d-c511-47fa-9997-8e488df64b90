import { type SchemaTypeDefinition } from "sanity";

import { blockContentType } from "./blockContentType";
import { categoryType } from "./categoryType";
import { postType } from "./postType";
import { authorType } from "./authorType";
import { advertiestmentType } from "./advertiestmentType";
import { webPageType } from "./webPageType";
import { privacyPolicyType } from "./webpages/PrivacyPolicy";
import { courseType } from "./courseType";
import { templateType } from "./templateType";
export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    blockContentType,
    categoryType,
    postType,
    authorType,
    advertiestmentType,
    webPageType,
    courseType,
    templateType,
  ],
};
