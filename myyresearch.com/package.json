{"name": "e9-web-builder", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.1.8", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@sanity/client": "^6.21.3", "@sanity/image-url": "^1.0.2", "@sanity/vision": "^3.57.1", "@sanity/webhook": "^3.0.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^5.7.0", "@t3-oss/env-nextjs": "^0.11.1", "@tanstack/react-query": "^5.67.2", "@tiptap/core": "^2.10.3", "@tiptap/extension-blockquote": "^2.10.3", "@tiptap/extension-bold": "^2.10.3", "@tiptap/extension-bullet-list": "^2.10.3", "@tiptap/extension-code": "^2.10.3", "@tiptap/extension-document": "^2.10.3", "@tiptap/extension-dropcursor": "^2.10.3", "@tiptap/extension-gapcursor": "^2.10.3", "@tiptap/extension-heading": "^2.10.3", "@tiptap/extension-history": "^2.10.3", "@tiptap/extension-horizontal-rule": "^2.10.3", "@tiptap/extension-image": "^2.10.3", "@tiptap/extension-italic": "^2.10.3", "@tiptap/extension-link": "^2.10.3", "@tiptap/extension-ordered-list": "^2.10.3", "@tiptap/extension-paragraph": "^2.10.3", "@tiptap/extension-placeholder": "^2.10.3", "@tiptap/extension-strike": "^2.10.3", "@tiptap/extension-subscript": "^2.10.3", "@tiptap/extension-superscript": "^2.10.3", "@tiptap/extension-task-item": "^2.10.3", "@tiptap/extension-task-list": "^2.10.3", "@tiptap/extension-text": "^2.10.3", "@tiptap/extension-text-align": "^2.10.3", "@tiptap/extension-text-style": "^2.10.3", "@tiptap/extension-underline": "^2.10.3", "@tiptap/extension-youtube": "^2.10.3", "@tiptap/pm": "^2.10.3", "@tiptap/react": "^2.10.3", "@tiptap/starter-kit": "^2.10.3", "@types/js-cookie": "^3.0.6", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crisp-sdk-web": "^1.0.25", "dicons": "^1.1.7", "dotted-map": "^2.2.3", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-react": "^8.2.1", "firebase": "^10.14.0", "framer-motion": "^12.4.3", "input-otp": "^1.4.2", "jose": "^6.0.7", "js-cookie": "^3.0.5", "lucide-react": "^0.439.0", "mongoose": "^8.6.3", "next": "15.0.4", "next-sanity": "^9.4.7", "next-themes": "^0.3.0", "nextjs-toploader": "^3.6.15", "posthog-js": "^1.160.3", "react": "19.0.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.0", "react-icons": "^5.3.0", "react-phone-number-input": "^3.4.8", "sanity": "^3.66.1", "sonner": "^2.0.3", "stripe": "^17.7.0", "styled-components": "^6.1.13", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.4.0", "vaul": "^0.9.2", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^20", "@types/react": "19.0.1", "@types/react-dom": "19.0.1", "eslint": "^9.16.0", "eslint-config-next": "15.0.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "resolutions": {"@types/react": "19.0.1", "@types/react-dom": "19.0.1"}}