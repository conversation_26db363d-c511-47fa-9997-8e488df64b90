{
  "compilerOptions": {
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": [
        "./*"
      ],
      "@/types/*": [
        "./types/*"
      ],
      "@/components/*": [
        "./components/*"
      ],
      "@/lib/*": [
        "./lib/*"
      ],
      "@/hooks/*": [
        "./hooks/*"
      ],
      "@/utils/*": [
        "./utils/*"
      ],
      "@/contexts/*": [
        "./contexts/*"
      ],
      "@/pages/*": [
        "./pages/*"
      ],
      //schema
      "@/schema/*": [
        "./schema/*"
      ],
      //server
      "@/server/*": [
        "./server/*"
      ]
    },
    "target": "ES2017"
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
, "../app.myyresearch.com/src/components/table.tsx"  ],
  "exclude": [
    "node_modules"
  ]
}
