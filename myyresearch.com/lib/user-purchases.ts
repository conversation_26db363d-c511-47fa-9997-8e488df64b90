import { ApiResponse } from "@/types/common";
import { OrderItemDetails, OrderItemType, CreatorType } from "@/types/orders";
import axios from "@/utils/axios";

// Filter interface for user purchase queries
export interface UserPurchaseFilter {
  search?: string;
  itemType?: OrderItemType;
  creatorType?: CreatorType;
  category?: string;
  subCategory?: string;
  from?: string;
  to?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Response interface for user purchase data
export interface UserPurchaseResponse {
  success: boolean;
  message: string;
  data: OrderItemDetails[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Get all user purchases with filtering
export async function getUserPurchases(
  filters: UserPurchaseFilter = {}
): Promise<ApiResponse<UserPurchaseResponse>> {
  try {
    const res = await axios.get("/orders/my-purchases", { params: filters });
    return res.data;
  } catch (error) {
    console.error("Error fetching user purchases:", error);
    return error as ApiResponse<UserPurchaseResponse>;
  }
}

// Get user's purchased courses
export async function getUserCourses(
  filters: Omit<UserPurchaseFilter, "itemType"> = {}
): Promise<ApiResponse<UserPurchaseResponse>> {
  try {
    const res = await axios.get("/orders/my-courses", { params: filters });
    return res.data;
  } catch (error) {
    console.error("Error fetching user courses:", error);
    return error as ApiResponse<UserPurchaseResponse>;
  }
}

// Get user's purchased templates
export async function getUserTemplates(
  filters: Omit<UserPurchaseFilter, "itemType"> = {}
): Promise<ApiResponse<UserPurchaseResponse>> {
  try {
    const res = await axios.get("/orders/my-templates", { params: filters });
    return res.data;
  } catch (error) {
    console.error("Error fetching user templates:", error);
    return error as ApiResponse<UserPurchaseResponse>;
  }
}

// Helper function to check if user has purchased a specific item
export async function hasUserPurchased(
  itemId: string,
  itemType: OrderItemType
): Promise<boolean> {
  try {
    const response = await getUserPurchases({
      limit: 1,
      // Note: We would need to add itemId filter to the API if needed
    });

    if (response.data) {
      return response.data.data.some(
        (item) => item.itemId === itemId && item.itemType === itemType
      );
    }

    return false;
  } catch (error) {
    console.error("Error checking user purchase:", error);
    return false;
  }
}

// Get purchase statistics for user dashboard
export async function getUserPurchaseStats(): Promise<{
  totalCourses: number;
  totalTemplates: number;
  totalSpent: number;
  recentPurchases: OrderItemDetails[];
}> {
  try {
    const [coursesResponse, templatesResponse] = await Promise.all([
      getUserCourses({ limit: 100 }), // Get all courses for stats
      getUserTemplates({ limit: 100 }), // Get all templates for stats
    ]);

    const courses = coursesResponse.data?.data || [];
    const templates = templatesResponse.data?.data || [];

    const allPurchases = [...courses, ...templates];
    const totalSpent = allPurchases.reduce(
      (sum, item) => sum + item.totalPrice,
      0
    );

    // Get recent purchases (last 5)
    const recentPurchases = allPurchases
      .sort(
        (a, b) =>
          new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime()
      )
      .slice(0, 5);

    return {
      totalCourses: courses.length,
      totalTemplates: templates.length,
      totalSpent,
      recentPurchases,
    };
  } catch (error) {
    console.error("Error fetching user purchase stats:", error);
    return {
      totalCourses: 0,
      totalTemplates: 0,
      totalSpent: 0,
      recentPurchases: [],
    };
  }
}
