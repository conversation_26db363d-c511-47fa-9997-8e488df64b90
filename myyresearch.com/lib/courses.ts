import { ApiResponse } from "@/types/common";
import {
  CategoryFilterDto,
  CategoryCoursesResponse,
  CourseFilters,
  FilteredCoursesResponse,
  PublicCourseResponse,
} from "@/types/course";
import axios from "@/utils/axios";

export async function getCoursesByCategory(
  filter: CategoryFilterDto
): Promise<ApiResponse<CategoryCoursesResponse>> {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append("category", filter.category);
    if (filter.subcategory)
      queryParams.append("subcategory", filter.subcategory);
    if (filter.page) queryParams.append("page", filter.page.toString());
    if (filter.limit) queryParams.append("limit", filter.limit.toString());

    const res = await axios.get(
      `/courses/public/by-category?${queryParams.toString()}`
    );
    console.log("res.data getCoursesByCategory  :::  ", res.data);
    return res.data;
  } catch (error) {
    console.error("Error fetching courses: ", error);
    return error as ApiResponse<CategoryCoursesResponse>;
  }
}

export async function getFilteredCourses(
  filters: CourseFilters
): Promise<ApiResponse<FilteredCoursesResponse>> {
  try {
    const queryParams = new URLSearchParams();

    // Add categories if present
    if (filters.categories && filters.categories.length > 0) {
      filters.categories.forEach((category) => {
        queryParams.append("categories", category);
      });
    }

    // Add subcategories if present
    if (filters.subcategories && filters.subcategories.length > 0) {
      filters.subcategories.forEach((subcategory) => {
        queryParams.append("subcategories", subcategory);
      });
    }

    // Add pagination parameters
    if (filters.page) queryParams.append("page", filters.page.toString());
    if (filters.limit) queryParams.append("limit", filters.limit.toString());

    const res = await axios.get(
      `/courses/public/filter?${queryParams.toString()}`
    );
    return res.data;
  } catch (error) {
    console.error("Error fetching filtered courses: ", error);
    return error as ApiResponse<FilteredCoursesResponse>;
  }
}

export async function getPublicCourseDetails(
  publicId: string
): Promise<ApiResponse<PublicCourseResponse>> {
  try {
    const res = await axios.get(`/courses/public/${publicId}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching course details: ", error);
    return error as ApiResponse<PublicCourseResponse>;
  }
}
