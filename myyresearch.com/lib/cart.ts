import axios from "@/utils/axios";
import {
  AddToCartRequest,
  AddToCartResponse,
  RemoveFromCartResponse,
  UpdateCartItemQuantityResponse,
  ClearCartResponse,
  UpdateCartItemRequest,
  RemoveFromCartRequest,
  BackendCartResponse,
} from "@/types/cart";
import { v4 as uuidv4 } from "uuid";

// Cookie-based cart ID management
const CART_ID_COOKIE_NAME = "myy_cart_id";
const CART_ID_COOKIE_MAX_AGE = 30 * 24 * 60 * 60; // 30 days in seconds

/**
 * Generate a new UUID for cart ID
 */
export function generateCartId(): string {
  return uuidv4();
}

/**
 * Get cart ID from cookies
 */
export function getCartIdFromCookies(): string | null {
  if (typeof document === "undefined") return null;

  const cookies = document.cookie.split(";");
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split("=");
    if (name === CART_ID_COOKIE_NAME) {
      return decodeURIComponent(value);
    }
  }
  return null;
}

/**
 * Set cart ID in cookies
 */
export function setCartIdInCookies(cartId: string): void {
  if (typeof document === "undefined") return;

  const expires = new Date();
  expires.setTime(expires.getTime() + CART_ID_COOKIE_MAX_AGE * 1000);

  document.cookie = `${CART_ID_COOKIE_NAME}=${encodeURIComponent(cartId)}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
}

/**
 * Remove cart ID from cookies
 */
export function removeCartIdFromCookies(): void {
  if (typeof document === "undefined") return;

  document.cookie = `${CART_ID_COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}

/**
 * Get or create cart ID for unauthenticated users
 */
export function getOrCreateCartId(): string {
  let cartId = getCartIdFromCookies();
  if (!cartId) {
    cartId = generateCartId();
    setCartIdInCookies(cartId);
  }
  return cartId;
}

/**
 * Get cart from backend (requires cartId)
 */
export async function getCart(cartId: string): Promise<BackendCartResponse> {
  try {
    const response = await axios.get("/cart", { params: { cartId } });

    console.log("Cart response: 99999", response.data);
    return response.data.data;
  } catch (error) {
    console.error("Error fetching cart:", error);
    throw error;
  }
}

/**
 * Add item to cart (requires cartId)
 */
export async function addToCart(
  data: AddToCartRequest
): Promise<AddToCartResponse> {
  try {
    const response = await axios.post("/cart/add", data);
    return response.data;
  } catch (error) {
    console.error("Error adding to cart:", error);
    throw error;
  }
}

/**
 * Remove item from cart using cart item ID (requires cartId)
 */
export async function removeFromCart(
  cartItemId: string,
  cartId: string
): Promise<RemoveFromCartResponse> {
  try {
    const requestData: RemoveFromCartRequest = {
      itemId: cartItemId,
      cartId,
    };
    const response = await axios.delete("/cart/remove", { data: requestData });
    return response.data;
  } catch (error) {
    console.error("Error removing from cart:", error);
    throw error;
  }
}

/**
 * Clear entire cart (requires cartId)
 */
export async function clearCart(cartId: string): Promise<ClearCartResponse> {
  try {
    const requestData = { cartId };
    const response = await axios.delete("/cart/clear", { data: requestData });
    return response.data;
  } catch (error) {
    console.error("Error clearing cart:", error);
    throw error;
  }
}

/**
 * Update cart item quantity using cart item ID (requires cartId)
 */
export async function updateCartItemQuantity(
  cartItemId: string,
  quantity: number,
  cartId: string
): Promise<UpdateCartItemQuantityResponse> {
  try {
    const requestData: UpdateCartItemRequest = {
      itemId: cartItemId,
      quantity: quantity,
      cartId,
    };
    const response = await axios.put("/cart/update", requestData);
    return response.data;
  } catch (error) {
    console.error("Error updating cart item quantity:", error);
    throw error;
  }
}

/**
 * Merge guest cart with user cart when user logs in
 */
export async function mergeGuestCartWithUserCart(
  guestCartId: string
): Promise<BackendCartResponse> {
  try {
    const response = await axios.post("/cart/merge", { guestCartId });
    return response.data;
  } catch (error) {
    console.error("Error merging guest cart:", error);
    throw error;
  }
}
