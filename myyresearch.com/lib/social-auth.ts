"use client";

export async function googleAuth(): Promise<void> {
  try {
    window.location.href = `${process.env.NEXT_PUBLIC_API_URL}/api/v1/auth/google`;
  } catch (error) {
    throw error;
  }
}

export async function facebookAuth(): Promise<void> {
  try {
    window.location.href = `${process.env.NEXT_PUBLIC_API_URL}/api/v1/auth/facebook`;
  } catch (error) {
    throw error;
  }
}

export async function githubAuth(): Promise<void> {
  try {
    window.location.href = `${process.env.NEXT_PUBLIC_API_URL}/api/v1/auth/github`;
  } catch (error) {
    throw error;
  }
}
