import { ApiResponse, ApiStatus } from "@/types/common";
import axios from "axios";

// Types for instructor payments
export interface InstructorPaymentDetails {
  _id: string;
  instructorId: string;
  preferredPaymentMethod: PaymentMethod;
  paypalEmail?: string;
  bankAccountNumber?: string;
  bankRoutingNumber?: string;
  bankName?: string;
  accountHolderName?: string;
  swiftCode?: string;
  stripeAccountId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateInstructorPaymentDetailsRequest {
  preferredPaymentMethod: PaymentMethod;
  paypalEmail?: string;
  bankAccountNumber?: string;
  bankRoutingNumber?: string;
  bankName?: string;
  accountHolderName?: string;
  swiftCode?: string;
  stripeAccountId?: string;
}

export interface UpdateInstructorPaymentDetailsRequest {
  preferredPaymentMethod?: PaymentMethod;
  paypalEmail?: string;
  bankAccountNumber?: string;
  bankRoutingNumber?: string;
  bankName?: string;
  accountHolderName?: string;
  swiftCode?: string;
  stripeAccountId?: string;
}

export interface PaymentRecord {
  _id: string;
  instructorId: string;
  amount: number;
  currency: string;
  paymentMethod: PaymentMethod;
  paymentDate: Date;
  transactionId?: string;
  notes?: string;
  orderItemDetailsIds: string[];
  lastPaidOrderItemDetailsId: string;
  processedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentRequest {
  _id: string;
  instructorId: string;
  requestedAmount: number;
  currency: string;
  status: PaymentRequestStatus;
  requestNotes?: string;
  adminNotes?: string;
  approvedAt?: Date;
  rejectedAt?: Date;
  paidAt?: Date;
  processedBy?: string;
  paymentRecordId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreatePaymentRequestRequest {
  requestedAmount: number;
  currency: string;
  requestNotes?: string;
}

export interface UnpaidOrderItem {
  _id: string;
  itemType: string;
  itemTitle: string;
  totalPrice: number;
  instructorEarning: number;
  orderDate: Date;
  publicId: string;
}

export interface PaymentHistory {
  _id: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  paymentDate: Date;
  transactionId?: string;
  notes?: string;
  orderItemsCount: number;
}

export interface InstructorBalanceDetails {
  instructorId: string;
  totalEarnings: number;
  totalPaid: number;
  currentBalance: number;
  currency: string;
  unpaidOrderItems: UnpaidOrderItem[];
  paymentHistory: PaymentHistory[];
}

export enum PaymentMethod {
  PAYPAL = "paypal",
  BANK_TRANSFER = "bank_transfer",
  STRIPE = "stripe",
}

export enum PaymentRequestStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
  PAID = "paid",
}

// API base URL
const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080/api";

// Create axios instance with auth token
const createAxiosInstance = () => {
  const token = localStorage.getItem("accessToken");
  return axios.create({
    baseURL: API_BASE_URL,
    headers: {
      Authorization: token ? `Bearer ${token}` : "",
      "Content-Type": "application/json",
    },
  });
};

// Instructor Payment Details API Functions
export async function createInstructorPaymentDetails(
  data: CreateInstructorPaymentDetailsRequest
): Promise<ApiResponse<InstructorPaymentDetails>> {
  try {
    const axiosInstance = createAxiosInstance();
    const res = await axiosInstance.post(
      "/instructor-payments/payment-details",
      data
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message:
        error.response?.data?.message || "Failed to create payment details",
      data: null,
    };
  }
}

export async function updateInstructorPaymentDetails(
  data: UpdateInstructorPaymentDetailsRequest
): Promise<ApiResponse<InstructorPaymentDetails>> {
  try {
    const axiosInstance = createAxiosInstance();
    const res = await axiosInstance.put(
      "/instructor-payments/payment-details",
      data
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message:
        error.response?.data?.message || "Failed to update payment details",
      data: null,
    };
  }
}

export async function getInstructorPaymentDetails(): Promise<
  ApiResponse<InstructorPaymentDetails>
> {
  try {
    const axiosInstance = createAxiosInstance();
    const res = await axiosInstance.get("/instructor-payments/payment-details");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message:
        error.response?.data?.message || "Failed to fetch payment details",
      data: null,
    };
  }
}

// Instructor Balance and History API Functions
export async function getInstructorBalance(): Promise<
  ApiResponse<InstructorBalanceDetails>
> {
  try {
    const axiosInstance = createAxiosInstance();
    const res = await axiosInstance.get("/instructor-payments/balance");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Failed to fetch balance",
      data: null,
    };
  }
}

export async function getInstructorPaymentHistory(): Promise<
  ApiResponse<PaymentRecord[]>
> {
  try {
    const axiosInstance = createAxiosInstance();
    const res = await axiosInstance.get("/instructor-payments/payment-history");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message:
        error.response?.data?.message || "Failed to fetch payment history",
      data: null,
    };
  }
}

// Payment Request API Functions
export async function createPaymentRequest(
  data: CreatePaymentRequestRequest
): Promise<ApiResponse<PaymentRequest>> {
  try {
    const axiosInstance = createAxiosInstance();
    const res = await axiosInstance.post(
      "/instructor-payments/payment-request",
      data
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message:
        error.response?.data?.message || "Failed to create payment request",
      data: null,
    };
  }
}

export async function getInstructorPaymentRequests(): Promise<
  ApiResponse<PaymentRequest[]>
> {
  try {
    const axiosInstance = createAxiosInstance();
    const res = await axiosInstance.get(
      "/instructor-payments/payment-requests"
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message:
        error.response?.data?.message || "Failed to fetch payment requests",
      data: null,
    };
  }
}
