"use client";

import { useQuery } from "@tanstack/react-query";
import {
  getTemplatesByCategory,
  getPublicTemplateDetails,
  getFilteredTemplates,
} from "@/lib/templates";
import { CategoryFilterDto, TemplateFilters } from "@/types/template";
import { useSearchParams } from "next/navigation";

// Query keys for caching and invalidation
export const templateKeys = {
  all: ["templates"] as const,
  byCategory: (filter: CategoryFilterDto) =>
    [
      ...templateKeys.all,
      "byCategory",
      filter.category,
      filter.subcategory,
      filter.page,
      filter.limit,
    ] as const,
  byId: (id: string) => [...templateKeys.all, "byId", id] as const,
  filtered: (filters: TemplateFilters) =>
    [...templateKeys.all, "filtered", JSON.stringify(filters)] as const,
};

// Hook for fetching templates by category
export function useTemplatesByCategory(filter: CategoryFilterDto) {
  return useQuery({
    queryKey: templateKeys.byCategory(filter),
    queryFn: () => getTemplatesByCategory(filter),
    enabled: !!filter.category,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for fetching template details
export function useTemplateDetails(publicId: string) {
  return useQuery({
    queryKey: templateKeys.byId(publicId),
    queryFn: () => getPublicTemplateDetails(publicId),
    enabled: !!publicId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for fetching filtered templates
export function useFilteredTemplates(filters: TemplateFilters) {
  return useQuery({
    queryKey: templateKeys.filtered(filters),
    queryFn: () => getFilteredTemplates(filters),
    // enabled: !!(
    //   filters.categories?.length ||
    //   filters.subcategories?.length ||
    //   filters.mediaTypes?.length
    // ),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for fetching templates with search params
export function useTemplatesFromSearchParams() {
  const searchParams = useSearchParams();

  // Parse search parameters
  const page = Number.parseInt(searchParams.get("page") || "1");
  const limit = Number.parseInt(searchParams.get("limit") || "10");
  const categories =
    searchParams.get("categories")?.split(",").filter(Boolean) || [];
  const subcategories =
    searchParams.get("subcategories")?.split(",").filter(Boolean) || [];
  const mediaTypesParam =
    searchParams.get("mediaTypes")?.split(",").filter(Boolean) || [];

  // Create filters object
  const filters: TemplateFilters = {
    categories: categories.length > 0 ? categories : undefined,
    subcategories: subcategories.length > 0 ? subcategories : undefined,
    mediaTypes:
      mediaTypesParam.length > 0 ? (mediaTypesParam as any) : undefined,
    page,
    limit,
  };

  // Use the filtered templates hook
  return {
    ...useFilteredTemplates(filters),
    filters,
    page,
    limit,
  };
}
