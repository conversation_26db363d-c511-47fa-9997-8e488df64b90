"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  getCart,
  addToCart,
  removeFromCart,
  updateCartItemQuantity,
  clearCart,
  mergeGuestCartWithUserCart,
} from "@/lib/cart";
import {
  AddToCartRequest,
  AddToCartResponse,
  RemoveFromCartResponse,
  UpdateCartItemQuantityResponse,
  ClearCartResponse,
  BackendCartResponse,
} from "@/types/cart";

// Query keys for cart operations
export const cartKeys = {
  all: ["cart"] as const,
  byCartId: (cartId: string) => [...cartKeys.all, cartId] as const,
};

/**
 * Hook to get cart data with caching and automatic refetching
 */
export function useCart(cartId: string) {
  return useQuery({
    queryKey: cartKeys.byCartId(cartId),
    queryFn: () => getCart(cartId),
    enabled: !!cartId,
    staleTime: 2 * 60 * 1000, // 2 minutes for cart data
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
}

/**
 * Hook to add items to cart with optimistic updates
 */
export function useAddToCart(cartId: string) {
  const queryClient = useQueryClient();

  return useMutation<
    AddToCartResponse,
    Error,
    Omit<AddToCartRequest, "cartId">,
    { previousCart?: BackendCartResponse }
  >({
    mutationFn: (data: Omit<AddToCartRequest, "cartId">) =>
      addToCart({ ...data, cartId }),
    onMutate: async (newItem) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: cartKeys.byCartId(cartId) });

      // Snapshot the previous value
      const previousCart = queryClient.getQueryData<BackendCartResponse>(
        cartKeys.byCartId(cartId)
      );

      // Optimistically update the cart
      if (previousCart?.cart) {
        const optimisticCart: BackendCartResponse = {
          ...previousCart,
          cart: {
            ...previousCart.cart,
            totalItems: previousCart.cart.totalItems + (newItem.quantity || 1),
            // Note: We don't update items array optimistically as we need server response for cart item ID
          },
        };

        queryClient.setQueryData(cartKeys.byCartId(cartId), optimisticCart);
      }

      return { previousCart };
    },
    onError: (_err, _newItem, context) => {
      // Rollback on error
      if (context?.previousCart) {
        queryClient.setQueryData(cartKeys.byCartId(cartId), context.previousCart);
      }
    },
    onSuccess: () => {
      // Invalidate and refetch cart data to get the latest state
      queryClient.invalidateQueries({ queryKey: cartKeys.byCartId(cartId) });
    },
  });
}

/**
 * Hook to remove items from cart with optimistic updates
 */
export function useRemoveFromCart(cartId: string) {
  const queryClient = useQueryClient();

  return useMutation<
    RemoveFromCartResponse,
    Error,
    string,
    { previousCart?: BackendCartResponse }
  >({
    mutationFn: (cartItemId: string) => removeFromCart(cartItemId, cartId),
    onMutate: async (cartItemId) => {
      await queryClient.cancelQueries({ queryKey: cartKeys.byCartId(cartId) });

      const previousCart = queryClient.getQueryData<BackendCartResponse>(
        cartKeys.byCartId(cartId)
      );

      // Optimistically remove the item
      if (previousCart?.cart) {
        const itemToRemove = previousCart.cart.items.find(
          (item) => item._id === cartItemId
        );
        
        if (itemToRemove) {
          const optimisticCart: BackendCartResponse = {
            ...previousCart,
            cart: {
              ...previousCart.cart,
              items: previousCart.cart.items.filter(
                (item) => item._id !== cartItemId
              ),
              totalItems: previousCart.cart.totalItems - itemToRemove.quantity,
              totalAmount: previousCart.cart.totalAmount - 
                (itemToRemove.discountPrice || itemToRemove.price) * itemToRemove.quantity,
            },
          };

          queryClient.setQueryData(cartKeys.byCartId(cartId), optimisticCart);
        }
      }

      return { previousCart };
    },
    onError: (_err, _cartItemId, context) => {
      if (context?.previousCart) {
        queryClient.setQueryData(cartKeys.byCartId(cartId), context.previousCart);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: cartKeys.byCartId(cartId) });
    },
  });
}

/**
 * Hook to update cart item quantity with optimistic updates
 */
export function useUpdateCartItem(cartId: string) {
  const queryClient = useQueryClient();

  return useMutation<
    UpdateCartItemQuantityResponse,
    Error,
    { cartItemId: string; quantity: number },
    { previousCart?: BackendCartResponse }
  >({
    mutationFn: ({ cartItemId, quantity }: { cartItemId: string; quantity: number }) =>
      updateCartItemQuantity(cartItemId, quantity, cartId),
    onMutate: async ({ cartItemId, quantity }) => {
      await queryClient.cancelQueries({ queryKey: cartKeys.byCartId(cartId) });

      const previousCart = queryClient.getQueryData<BackendCartResponse>(
        cartKeys.byCartId(cartId)
      );

      // Optimistically update the quantity
      if (previousCart?.cart) {
        const itemIndex = previousCart.cart.items.findIndex(
          (item) => item._id === cartItemId
        );

        if (itemIndex !== -1) {
          const currentItem = previousCart.cart.items[itemIndex];
          const quantityDiff = quantity - currentItem.quantity;
          const priceDiff = (currentItem.discountPrice || currentItem.price) * quantityDiff;

          const updatedItems = [...previousCart.cart.items];
          updatedItems[itemIndex] = { ...currentItem, quantity };

          const optimisticCart: BackendCartResponse = {
            ...previousCart,
            cart: {
              ...previousCart.cart,
              items: updatedItems,
              totalItems: previousCart.cart.totalItems + quantityDiff,
              totalAmount: previousCart.cart.totalAmount + priceDiff,
            },
          };

          queryClient.setQueryData(cartKeys.byCartId(cartId), optimisticCart);
        }
      }

      return { previousCart };
    },
    onError: (_err, _variables, context) => {
      if (context?.previousCart) {
        queryClient.setQueryData(cartKeys.byCartId(cartId), context.previousCart);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: cartKeys.byCartId(cartId) });
    },
  });
}

/**
 * Hook to clear entire cart
 */
export function useClearCart(cartId: string) {
  const queryClient = useQueryClient();

  return useMutation<
    ClearCartResponse,
    Error,
    void,
    { previousCart?: BackendCartResponse }
  >({
    mutationFn: () => clearCart(cartId),
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey: cartKeys.byCartId(cartId) });

      const previousCart = queryClient.getQueryData<BackendCartResponse>(
        cartKeys.byCartId(cartId)
      );

      // Optimistically clear the cart
      if (previousCart?.cart) {
        const optimisticCart: BackendCartResponse = {
          ...previousCart,
          cart: {
            ...previousCart.cart,
            items: [],
            totalItems: 0,
            totalAmount: 0,
          },
        };

        queryClient.setQueryData(cartKeys.byCartId(cartId), optimisticCart);
      }

      return { previousCart };
    },
    onError: (_err, _variables, context) => {
      if (context?.previousCart) {
        queryClient.setQueryData(cartKeys.byCartId(cartId), context.previousCart);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: cartKeys.byCartId(cartId) });
    },
  });
}

/**
 * Hook to merge guest cart with user cart (for login scenarios)
 */
export function useMergeCart() {
  const queryClient = useQueryClient();

  return useMutation<BackendCartResponse, Error, string>({
    mutationFn: (guestCartId: string) => mergeGuestCartWithUserCart(guestCartId),
    onSuccess: (data, guestCartId) => {
      // Invalidate both the guest cart and the merged cart
      queryClient.invalidateQueries({ queryKey: cartKeys.byCartId(guestCartId) });
      if (data.cart.cartId !== guestCartId) {
        queryClient.invalidateQueries({ queryKey: cartKeys.byCartId(data.cart.cartId) });
      }
    },
  });
}
