"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createPaymentIntent,
  confirmPayment,
  getPaymentHistory,
} from "@/lib/payments";
import {
  CreatePaymentIntentRequest,
  ConfirmPaymentRequest,
  PaymentFilter,
} from "@/types/payments";
import { cartKeys } from "./useCart";
import { orderKeys } from "./useOrders";

// Query keys for payment operations
export const paymentKeys = {
  all: ["payments"] as const,
  history: () => [...paymentKeys.all, "history"] as const,
  historyFiltered: (filter?: PaymentFilter) =>
    [...paymentKeys.history(), filter] as const,
};

/**
 * Hook to create Stripe payment intent for order
 */
export function useCreatePaymentIntent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePaymentIntentRequest) => createPaymentIntent(data),
    onSuccess: (data, variables) => {
      // Update order cache with payment intent ID if available
      queryClient.invalidateQueries({
        queryKey: orderKeys.detail(variables.orderId),
      });
    },
    onError: (error) => {
      console.error("Error creating payment intent:", error);
    },
  });
}

/**
 * Hook to confirm payment after Stripe processing
 */
export function useConfirmPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ConfirmPaymentRequest) => confirmPayment(data),
    onSuccess: (data) => {
      // Invalidate all cart queries since payment is successful
      queryClient.invalidateQueries({ queryKey: cartKeys.all });

      // Update order cache with payment completion
      if (data.order) {
        queryClient.setQueryData(orderKeys.detail(data.order._id), {
          success: true,
          message: "Order retrieved successfully",
          order: data.order,
        });
      }

      // Invalidate orders and payments to show updated status
      queryClient.invalidateQueries({ queryKey: orderKeys.all });
      queryClient.invalidateQueries({ queryKey: paymentKeys.all });
    },
    onError: (error) => {
      console.error("Error confirming payment:", error);
    },
  });
}

/**
 * Hook to get payment history with filtering
 */
export function usePaymentHistory(filter?: PaymentFilter) {
  return useQuery({
    queryKey: paymentKeys.historyFiltered(filter),
    queryFn: () => getPaymentHistory(filter),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true,
  });
}
