"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createOrder,
  getOrder,
  getUserOrders,
  getPurchasedTemplates,
  getPurchasedCourses,
  getAllPurchasedItems,
} from "@/lib/orders";
import {
  getUserPurchases,
  getUserCourses,
  getUserTemplates,
  getUserPurchaseStats,
  UserPurchaseFilter,
} from "@/lib/user-purchases";
import {
  CreateOrderRequest,
  CreateOrderResponse,
  GetOrderResponse,
  GetOrdersResponse,
  OrderFilter,
} from "@/types/orders";
import { cartKeys } from "./useCart";

// Query keys for order operations
export const orderKeys = {
  all: ["orders"] as const,
  lists: () => [...orderKeys.all, "list"] as const,
  list: (filter?: OrderFilter) => [...orderKeys.lists(), filter] as const,
  details: () => [...orderKeys.all, "detail"] as const,
  detail: (id: string) => [...orderKeys.details(), id] as const,
  purchased: () => [...orderKeys.all, "purchased"] as const,
  purchasedTemplates: (filter?: OrderFilter) =>
    [...orderKeys.purchased(), "templates", filter] as const,
  purchasedCourses: (filter?: OrderFilter) =>
    [...orderKeys.purchased(), "courses", filter] as const,
  purchasedAll: (filter?: OrderFilter) =>
    [...orderKeys.purchased(), "all", filter] as const,
  // User purchases keys
  userPurchases: () => [...orderKeys.all, "userPurchases"] as const,
  userPurchasesList: (filter?: UserPurchaseFilter) =>
    [...orderKeys.userPurchases(), "list", filter] as const,
  userCourses: (filter?: UserPurchaseFilter) =>
    [...orderKeys.userPurchases(), "courses", filter] as const,
  userTemplates: (filter?: UserPurchaseFilter) =>
    [...orderKeys.userPurchases(), "templates", filter] as const,
  userPurchaseStats: () => [...orderKeys.userPurchases(), "stats"] as const,
};

/**
 * Hook to create order from cart with optimistic updates
 */
export function useCreateOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOrderRequest) => createOrder(data),
    onSuccess: (data, variables) => {
      // Invalidate cart data since cart should be cleared after order creation
      queryClient.invalidateQueries({
        queryKey: cartKeys.byCartId(variables.cartId),
      });

      // Invalidate orders list to show the new order
      queryClient.invalidateQueries({ queryKey: orderKeys.lists() });

      // Set the new order in cache
      if (data.order) {
        queryClient.setQueryData(orderKeys.detail(data.order._id), data);
      }
    },
    onError: (error) => {
      console.error("Error creating order:", error);
    },
  });
}

/**
 * Hook to get specific order by ID
 */
export function useOrder(orderId: string) {
  return useQuery({
    queryKey: orderKeys.detail(orderId),
    queryFn: () => getOrder(orderId),
    enabled: !!orderId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get user orders with filtering
 */
export function useUserOrders(filter?: OrderFilter) {
  return useQuery({
    queryKey: orderKeys.list(filter),
    queryFn: () => getUserOrders(filter),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true,
  });
}

/**
 * Hook to get user purchased templates
 */
export function usePurchasedTemplates(filter?: OrderFilter) {
  return useQuery({
    queryKey: orderKeys.purchasedTemplates(filter),
    queryFn: () => getPurchasedTemplates(filter),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get user purchased courses
 */
export function usePurchasedCourses(filter?: OrderFilter) {
  return useQuery({
    queryKey: orderKeys.purchasedCourses(filter),
    queryFn: () => getPurchasedCourses(filter),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get all user purchased items (courses and templates)
 */
export function useAllPurchasedItems(filter?: OrderFilter) {
  return useQuery({
    queryKey: orderKeys.purchasedAll(filter),
    queryFn: () => getAllPurchasedItems(filter),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get user purchases using OrderItemDetails
 */
export function useUserPurchases(filter?: UserPurchaseFilter) {
  return useQuery({
    queryKey: orderKeys.userPurchasesList(filter),
    queryFn: () => getUserPurchases(filter),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get user's purchased courses using OrderItemDetails
 */
export function useUserCourses(filter?: UserPurchaseFilter) {
  return useQuery({
    queryKey: orderKeys.userCourses(filter),
    queryFn: () => getUserCourses(filter),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get user's purchased templates using OrderItemDetails
 */
export function useUserTemplates(filter?: UserPurchaseFilter) {
  return useQuery({
    queryKey: orderKeys.userTemplates(filter),
    queryFn: () => getUserTemplates(filter),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get user purchase statistics for dashboard
 */
export function useUserPurchaseStats() {
  return useQuery({
    queryKey: orderKeys.userPurchaseStats(),
    queryFn: () => getUserPurchaseStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });
}
