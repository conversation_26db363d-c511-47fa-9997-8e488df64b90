import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getUserProfile, updateUserProfile, logoutUser } from "@/lib/users";
import { UpdateProfileData } from "@/types/auth";
import { ApiStatus } from "@/types/common";
import { toast } from "@/hooks/use-toast";

// Query keys for user-related queries
export const userKeys = {
  all: ["users"] as const,
  profile: () => [...userKeys.all, "profile"] as const,
};

/**
 * Hook to get user profile
 */
export function useUserProfile() {
  return useQuery({
    queryKey: userKeys.profile(),
    queryFn: getUserProfile,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to update user profile
 */
export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateProfileData) => updateUserProfile(data),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        // Invalidate and refetch user profile
        queryClient.invalidateQueries({ queryKey: userKeys.profile() });

        toast({
          title: "Success",
          description: response.message || "Profile updated successfully",
        });
      } else {
        // Handle API-level failures (status: FAIL)
        toast({
          title: "Error",
          description: response.message || "Failed to update profile",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      // Handle network errors, validation errors, etc.
      let errorMessage = "Failed to update profile";

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to logout user
 */
export function useLogout() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: logoutUser,
    onSuccess: () => {
      // Show success toast
      toast({
        title: "Success",
        description: "You have been logged out successfully",
      });

      // Clear all cached data
      queryClient.clear();

      // Redirect to login page after a short delay to show the toast
      setTimeout(() => {
        window.location.href = "/auth/sign-in";
      }, 1000);
    },
    onError: (error: any) => {
      let errorMessage = "Failed to logout";

      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });
}
