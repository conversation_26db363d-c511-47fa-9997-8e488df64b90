"use client";

import { useQuery } from "@tanstack/react-query";
import {
  getCoursesByCategory,
  getPublicCourseDetails,
  getFilteredCourses,
} from "@/lib/courses";
import { CategoryFilterDto, CourseFilters } from "@/types/course";
import { useSearchParams } from "next/navigation";

// Query keys for caching and invalidation
export const courseKeys = {
  all: ["courses"] as const,
  byCategory: (filter: CategoryFilterDto) =>
    [
      ...courseKeys.all,
      "byCategory",
      filter.category,
      filter.subcategory,
      filter.page,
      filter.limit,
    ] as const,
  byId: (id: string) => [...courseKeys.all, "byId", id] as const,
  filtered: (filters: CourseFilters) =>
    [...courseKeys.all, "filtered", JSON.stringify(filters)] as const,
};

// Hook for fetching courses by category
export function useCoursesByCategory(filter: CategoryFilterDto) {
  return useQuery({
    queryKey: courseKeys.byCategory(filter),
    queryFn: () => getCoursesByCategory(filter),
    enabled: !!filter.category,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for fetching course details
export function useCourseDetails(publicId: string) {
  return useQuery({
    queryKey: courseKeys.byId(publicId),
    queryFn: () => getPublicCourseDetails(publicId),
    enabled: !!publicId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for fetching filtered courses
export function useFilteredCourses(filters: CourseFilters) {
  return useQuery({
    queryKey: courseKeys.filtered(filters),
    queryFn: () => getFilteredCourses(filters),
    // enabled: !!(filters.categories?.length || filters.subcategories?.length),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for fetching courses with search params
export function useCoursesFromSearchParams() {
  const searchParams = useSearchParams();

  // Parse search parameters
  const page = Number.parseInt(searchParams.get("page") || "1");
  const limit = Number.parseInt(searchParams.get("limit") || "10");
  const categories =
    searchParams.get("categories")?.split(",").filter(Boolean) || [];
  const subcategories =
    searchParams.get("subcategories")?.split(",").filter(Boolean) || [];

  // Create filters object
  const filters: CourseFilters = {
    categories: categories.length > 0 ? categories : undefined,
    subcategories: subcategories.length > 0 ? subcategories : undefined,
    page,
    limit,
  };

  // Use the filtered courses hook
  return {
    ...useFilteredCourses(filters),
    filters,
    page,
    limit,
  };
}
