import axios from "@/utils/axios";
import {
  CreatePaymentIntentRequest,
  CreatePaymentIntentResponse,
  ConfirmPaymentRequest,
  ConfirmPaymentResponse,
  GetPaymentHistoryResponse,
  PaymentFilter,
} from "@/types/payments";

/**
 * Create Stripe payment intent for order
 */
export async function createPaymentIntent(
  data: CreatePaymentIntentRequest
): Promise<CreatePaymentIntentResponse> {
  try {
    const response = await axios.post("/payments/create-intent", data);
    return response.data.data;
  } catch (error) {
    console.error("Error creating payment intent:", error);
    throw error;
  }
}

/**
 * Confirm payment after Stripe processing
 */
export async function confirmPayment(
  data: ConfirmPaymentRequest
): Promise<ConfirmPaymentResponse> {
  try {
    const response = await axios.post("/payments/confirm", data);
    return response.data.data;
  } catch (error) {
    console.error("Error confirming payment:", error);
    throw error;
  }
}

/**
 * Get user payment history with filtering
 */
export async function getPaymentHistory(
  filter?: PaymentFilter
): Promise<GetPaymentHistoryResponse> {
  try {
    const params = new URLSearchParams();

    if (filter?.status) params.append("status", filter.status);
    if (filter?.page) params.append("page", filter.page.toString());
    if (filter?.limit) params.append("limit", filter.limit.toString());
    if (filter?.from) params.append("from", filter.from);
    if (filter?.to) params.append("to", filter.to);

    const response = await axios.get(`/payments/history?${params.toString()}`);
    return response.data.data;
  } catch (error) {
    console.error("Error fetching payment history:", error);
    throw error;
  }
}
