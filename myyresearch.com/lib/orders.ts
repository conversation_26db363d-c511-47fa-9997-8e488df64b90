import axios from "@/utils/axios";
import {
  CreateOrderRequest,
  CreateOrderResponse,
  GetOrderResponse,
  GetOrdersResponse,
  GetPurchasedItemsResponse,
  OrderFilter,
} from "@/types/orders";

/**
 * Create order from cart using cartId
 */
export async function createOrder(
  data: CreateOrderRequest
): Promise<CreateOrderResponse> {
  try {
    const response = await axios.post("/orders/create", data);
    return response.data.data;
  } catch (error) {
    console.error("Error creating order:", error);
    throw error;
  }
}

/**
 * Get specific order by ID
 */
export async function getOrder(orderId: string): Promise<GetOrderResponse> {
  try {
    const response = await axios.get(`/orders/${orderId}`);
    return response.data.data;
  } catch (error) {
    console.error("Error fetching order:", error);
    throw error;
  }
}

/**
 * Get user orders with filtering
 */
export async function getUserOrders(
  filter?: OrderFilter
): Promise<GetOrdersResponse> {
  try {
    const params = new URLSearchParams();

    if (filter?.status) params.append("status", filter.status);
    if (filter?.paymentStatus)
      params.append("paymentStatus", filter.paymentStatus);
    if (filter?.page) params.append("page", filter.page.toString());
    if (filter?.limit) params.append("limit", filter.limit.toString());
    if (filter?.from) params.append("from", filter.from);
    if (filter?.to) params.append("to", filter.to);

    const response = await axios.get(`/orders?${params.toString()}`);
    return response.data.data;
  } catch (error) {
    console.error("Error fetching user orders:", error);
    throw error;
  }
}

/**
 * Get user purchased templates
 */
export async function getPurchasedTemplates(
  filter?: OrderFilter
): Promise<GetPurchasedItemsResponse> {
  try {
    const params = new URLSearchParams();

    if (filter?.page) params.append("page", filter.page.toString());
    if (filter?.limit) params.append("limit", filter.limit.toString());
    if (filter?.from) params.append("from", filter.from);
    if (filter?.to) params.append("to", filter.to);

    const response = await axios.get(
      `/orders/purchased/templates?${params.toString()}`
    );
    return response.data.data;
  } catch (error) {
    console.error("Error fetching purchased templates:", error);
    throw error;
  }
}

/**
 * Get user purchased courses
 */
export async function getPurchasedCourses(
  filter?: OrderFilter
): Promise<GetPurchasedItemsResponse> {
  try {
    const params = new URLSearchParams();

    if (filter?.page) params.append("page", filter.page.toString());
    if (filter?.limit) params.append("limit", filter.limit.toString());
    if (filter?.from) params.append("from", filter.from);
    if (filter?.to) params.append("to", filter.to);

    const response = await axios.get(
      `/orders/purchased/courses?${params.toString()}`
    );
    return response.data.data;
  } catch (error) {
    console.error("Error fetching purchased courses:", error);
    throw error;
  }
}

/**
 * Get all user purchased items (courses and templates)
 */
export async function getAllPurchasedItems(
  filter?: OrderFilter
): Promise<GetPurchasedItemsResponse> {
  try {
    const params = new URLSearchParams();

    if (filter?.page) params.append("page", filter.page.toString());
    if (filter?.limit) params.append("limit", filter.limit.toString());
    if (filter?.from) params.append("from", filter.from);
    if (filter?.to) params.append("to", filter.to);

    const response = await axios.get(
      `/orders/purchased/all?${params.toString()}`
    );
    return response.data.data;
  } catch (error) {
    console.error("Error fetching all purchased items:", error);
    throw error;
  }
}
