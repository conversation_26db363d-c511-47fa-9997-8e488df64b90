import { ApiResponse } from "@/types/common";
import {
  CategoryFilterDto,
  CategoryTemplatesResponse,
  PublicTemplateResponse,
  TemplateMediaType,
  FilteredTemplatesResponse,
  TemplateFilters,
} from "@/types/template";
import axios from "@/utils/axios";

export async function getTemplatesByCategory(
  filter: CategoryFilterDto
): Promise<ApiResponse<CategoryTemplatesResponse>> {
  try {
    const queryParams = new URLSearchParams();
    queryParams.append("category", filter.category);
    if (filter.subcategory)
      queryParams.append("subcategory", filter.subcategory);
    if (filter.page) queryParams.append("page", filter.page.toString());
    if (filter.limit) queryParams.append("limit", filter.limit.toString());

    const res = await axios.get(
      `/templates/public/by-category?${queryParams.toString()}`
    );
    return res.data;
  } catch (error) {
    console.error("Error fetching templates: ", error);
    return error as ApiResponse<CategoryTemplatesResponse>;
  }
}

export async function getPublicTemplateDetails(
  publicId: string
): Promise<ApiResponse<PublicTemplateResponse>> {
  try {
    const res = await axios.get(`/templates/public/${publicId}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching template details: ", error);
    return error as ApiResponse<PublicTemplateResponse>;
  }
}

export async function getFilteredTemplates(
  filters: TemplateFilters
): Promise<ApiResponse<FilteredTemplatesResponse>> {
  try {
    const queryParams = new URLSearchParams();

    // Add categories if present
    if (filters.categories && filters.categories.length > 0) {
      filters.categories.forEach((category) => {
        queryParams.append("categories", category);
      });
    }

    // Add subcategories if present
    if (filters.subcategories && filters.subcategories.length > 0) {
      filters.subcategories.forEach((subcategory) => {
        queryParams.append("subcategories", subcategory);
      });
    }

    // Add mediaTypes if present
    if (filters.mediaTypes && filters.mediaTypes.length > 0) {
      filters.mediaTypes.forEach((mediaType) => {
        queryParams.append("mediaTypes", mediaType);
      });
    }

    // Add pagination parameters
    if (filters.page) queryParams.append("page", filters.page.toString());
    if (filters.limit) queryParams.append("limit", filters.limit.toString());

    const res = await axios.get(
      `/templates/public/filter?${queryParams.toString()}`
    );
    console.log("Templates response:", res.data);
    return res.data;
  } catch (error) {
    console.error("Error fetching filtered templates: ", error);
    return error as ApiResponse<FilteredTemplatesResponse>;
  }
}
