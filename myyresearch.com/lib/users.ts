import { ApiResponse } from "@/types/common";
import { User, UpdateProfileData } from "@/types/auth";
import axios from "@/utils/axios";
import { logout } from "@/lib/authenticaton";

/**
 * Get user profile
 */
export async function getUserProfile(): Promise<ApiResponse<User>> {
  try {
    const res = await axios.get("/users/profile");
    return res.data;
  } catch (error) {
    return error as ApiResponse<User>;
  }
}

/**
 * Update user profile (username, email, password)
 */
export async function updateUserProfile(
  data: UpdateProfileData
): Promise<ApiResponse<User>> {
  try {
    const res = await axios.post("/users/update-profile", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<User>;
  }
}

/**
 * Logout user and clear session
 */
export async function logoutUser(): Promise<void> {
  return logout();
}
