/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
        remotePatterns: [
            {
                hostname: "cdn.sanity.io",
                protocol: "https",
            },//https://assets.aceternity.com/
            {
                hostname: "utfs.io",
                protocol: "https",
            },
            {
                hostname: "d1cah4y6lg0mem.cloudfront.net",
                protocol: "https",
            },
            {
                hostname: "images.unsplash.com",
                protocol: "https",
            },
        ],
    },
    
    experimental: {
      missingSuspenseWithCSRBailout: false,
    },

    typescript: {
    ignoreBuildErrors: true,
    },
    async rewrites() {
        return [
          {
            source: "/ingest/:path*",
            destination: "https://app.posthog.com/:path*",
          },
        ];
      },
};

export default nextConfig;
