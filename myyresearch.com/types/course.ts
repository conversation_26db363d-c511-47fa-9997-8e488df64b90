import { UploadedFile } from ".";
import { DocumentStatus } from "./common";

export interface Course {
  // Identification
  _id?: string;
  slug: string;
  publicId: string;

  // Basic Info
  title: string;
  shortDescription: string;
  description: string | null;

  // Media
  thumbnail: UploadedFile;

  // Classification
  category: string;
  subcategory: string;
  level: CourseLevel;
  tag: CourseTag;

  // Course Details
  duration: string;
  udemyUrl: string;

  // Pricing
  price: number;
  discountPrice: number | null;
  // Metrics
  value: number;
  rating: number;
  ratingCount: number;

  // Status & Timestamps
  status: CourseStatus;
  documentStatus: DocumentStatus;
  createdAt: string;
  updatedAt: string;

  createdBy: string;
  updatedBy: string;
}

export enum CourseStatus {
  PUBLISHED = "published",
  DRAFT = "draft",
}

export enum CourseLevel {
  BEGINNER = "beginner",
  INTERMEDIATE = "intermediate",
  ADVANCED = "advanced",
}

export enum CourseTag {
  NONE = "none",
  BEST_SELLER = "best-seller",
  NEW_ARRIVAL = "new-arrival",
  TRENDING = "trending",
}

export enum LessonType {
  VIDEO = "video",
  ARTICLE = "article",
  QUIZ = "quiz",
  ASSIGNMENT = "assignment",
}

export enum CreatorType {
  ADMIN = "admin",
  INSTRUCTOR = "instructor",
}

export interface ReviewUser {
  _id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
}

export interface Review {
  _id: string;
  user: ReviewUser;
  rating: number;
  comment: string;
  createdAt: Date;
}

export interface PublicLesson {
  _id: string;
  title: string;
  type: LessonType;
  order: number;
  duration: number;
  isFree: boolean;
  content?: string;
  video?: UploadedFile;
  media?: UploadedFile;
  downloadableFiles?: DownloadableFile[];
}

export interface DownloadableFile {
  _id: string;
  name: string;
  description?: string;
  file: UploadedFile;
  size?: number;
  downloadCount?: number;
}

export interface PublicSection {
  _id: string;
  title: string;
  type?: LessonType;
  order: number;
  lessons: PublicLesson[];
}

export interface Instructor {
  _id: string;
  name: string;
  title: string;
  avatar?: string;
  bio?: string;
  courseCount?: number;
}

export interface PublicCourseResponse {
  _id: string;
  title: string;
  publicId: string;
  shortDescription: string;
  description: string;
  whatYouWillLearn: string[];
  thumbnail: string;
  preview?: UploadedFile;
  category: string;
  subcategory: string;
  price: number;
  discountPrice?: number;
  enrollmentCount: number;
  free: boolean;
  status: CourseStatus;
  creatorType: CreatorType;
  instructorUsername?: string;
  instructor?: Instructor;
  averageReview: number;
  reviewCount: number;
  reviews: Review[];
  topics: string[];
  duration: number;
  totalLessons: number;
  sections: PublicSection[];
  level?: CourseLevel;
  lastUpdated?: string;
  requirements?: string[];
  discountEnds?: string;
  purchased: boolean;
}

export interface CategoryFilterDto {
  category: string;
  subcategory?: string;
  page?: number;
  limit?: number;
}

export interface PublicMinimalCourse {
  _id: string;
  title: string;
  publicId: string;
  shortDescription: string;
  thumbnail: string;
  category: string;
  subcategory: string;
  price: number;
  discountPrice?: number;
  enrollmentCount: number;
  free: boolean;
  averageReview: number;
  reviewCount: number;
}

export interface CategoryCoursesResponse {
  data: PublicMinimalCourse[];
  total: number;
  pages: number;
}

export interface CourseFilters {
  categories?: string[];
  subcategories?: string[];
  page?: number;
  limit?: number;
}

export interface FilteredCoursesResponse {
  data: PublicMinimalCourse[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}
