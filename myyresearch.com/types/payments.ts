import { ApiResponse } from "./common";

// Payment status enums matching backend
export enum PaymentStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  SUCCEEDED = "succeeded",
  FAILED = "failed",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
  PARTIALLY_REFUNDED = "partially_refunded",
}

export enum PaymentMethod {
  CARD = "card",
  BANK_TRANSFER = "bank_transfer",
  WALLET = "wallet",
}

// Payment interface
export interface Payment {
  _id: string;
  userId?: string;
  orderId: string;
  cartId: string;
  stripePaymentIntentId: string;
  stripeChargeId?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod?: PaymentMethod;
  paymentMethodDetails?: string;
  receiptUrl?: string;
  failureReason?: string;
  refundAmount?: number;
  refundReason?: string;
  refundedAt?: Date;
  paidAt?: Date;
  stripeMetadata?: Record<string, any>;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Request DTOs
export interface CreatePaymentIntentRequest {
  orderId: string;
  cartId: string;
  metadata?: Record<string, any>;
}

export interface ConfirmPaymentRequest {
  paymentIntentId: string;
  metadata?: Record<string, any>;
}

// Response DTOs
export interface CreatePaymentIntentResponse {
  success: boolean;
  message: string;
  clientSecret: string;
  paymentIntentId: string;
  amount: number;
  currency: string;
}

export interface ConfirmPaymentResponse {
  success: boolean;
  message: string;
  payment: Payment;
  order: any; // Order object from orders module
  enrollments?: any[]; // Created enrollments
}

export interface GetPaymentHistoryResponse {
  success: boolean;
  message: string;
  payments: Payment[];
  total: number;
  page: number;
  totalPages: number;
}

// Filter interface for payment history
export interface PaymentFilter {
  status?: PaymentStatus;
  page?: number;
  limit?: number;
  from?: string;
  to?: string;
}

// Stripe-specific types
export interface StripePaymentResult {
  error?: {
    message: string;
    type: string;
    code?: string;
  };
  paymentIntent?: {
    id: string;
    status: string;
    client_secret: string;
  };
}

// Legacy types for backward compatibility
export type PaymentResponse = ApiResponse<Payment>;
export type PaymentIntentResponse = ApiResponse<CreatePaymentIntentResponse>;
