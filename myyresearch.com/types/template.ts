import { UploadedFile } from ".";
import { DocumentStatus } from "./common";

// Core enums that define template properties
export enum TemplateStatus {
  PUBLISHED = "published",
  DRAFT = "draft",
}

export enum TemplateMediaType {
  // Document types
  DOC = "doc",
  EXCEL = "excel",
  PDF = "pdf",
  PPT = "ppt",
  TXT = "txt",
  CSV = "csv",

  // Media types
  AUDIO = "audio",
  IMAGE = "image",
  VIDEO = "video",
  TEXT = "text",

  // Fallback
  OTHER = "other",
}

export enum TemplateTag {
  NONE = "none",
  BEST_SELLER = "best-seller",
  NEW_ARRIVAL = "new-arrival",
  TRENDING = "trending",
}

// Main Template interface
export interface Template {
  // Core identifiers
  _id?: string;
  title: string;
  slug: string;
  publicId: string;

  // Content details
  shortDescription: string;
  description: string | null;
  thumbnail: UploadedFile;

  // Classification
  mediaType: TemplateMediaType;
  category: string;
  subcategory: string;
  tag: TemplateTag;

  // Pricing
  price: number;
  discountPrice: number | null;
  value: number;

  downloadCount: number;
  downloadMedia: UploadedFile;
  previewMedia: UploadedFile | null;
  free: boolean;

  // Metadata
  version: string;
  createdAt: string;
  updatedAt: string;
  documentStatus: DocumentStatus;
  status: TemplateStatus;
  createdBy: string;
  updatedBy: string;
}

export interface CategoryFilterDto {
  category: string;
  subcategory?: string;
  mediaType?: TemplateMediaType;
  page?: number;
  limit?: number;
}

export interface MinimalisticTemplate {
  _id: string;
  title: string;
  publicId: string;
  shortDescription: string;
  thumbnail: string;
  mediaType: TemplateMediaType;
  category: string;
  subcategory: string;
  tag: TemplateTag;
  price: number;
  discountPrice?: number;
  downloadCount: number;
  free: boolean;
  status: TemplateStatus;
  createdAt: Date;
  updatedAt: Date;
  creatorType: string;
  instructorUsername?: string;
}

export interface CategoryTemplatesResponse {
  data: MinimalisticTemplate[];
  total: number;
  pages: number;
}

export interface ReviewUser {
  _id: string;
  firstName: string;
  lastName: string;
  avatar?: string;
}

export interface Review {
  _id: string;
  user: ReviewUser;
  rating: number;
  comment: string;
  createdAt: Date;
}

export interface PublicTemplateResponse {
  _id: string;
  title: string;
  publicId: string;
  shortDescription: string;
  description: string;
  thumbnail: UploadedFile;
  mediaType: TemplateMediaType;
  category: string;
  subcategory: string;
  tag: TemplateTag;
  price: number;
  discountPrice?: number;
  downloadCount: number;
  free: boolean;
  status: TemplateStatus;
  createdAt: Date;
  updatedAt: Date;
  creatorType: string;
  instructorUsername?: string;
  averageReview: number;
  reviewCount: number;
  reviews: Review[];
  previewMedia?: UploadedFile;
  downloadMedia?: UploadedFile;
  purchased: boolean;
}

export interface FilteredTemplatesResponse {
  data: MinimalisticTemplate[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export interface TemplateFilters {
  categories?: string[];
  subcategories?: string[];
  mediaTypes?: TemplateMediaType[];
  page?: number;
  limit?: number;
}
