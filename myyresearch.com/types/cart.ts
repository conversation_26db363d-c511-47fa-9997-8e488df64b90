import { ApiResponse } from "./common";

// Backend uses lowercase enum values
export enum ItemType {
  COURSE = "course",
  TEMPLATE = "template",
}

// Updated to match backend CartItemDto structure
export interface CartItem {
  _id: string;
  itemType: ItemType;
  itemId: string;
  publicId: string;
  quantity: number;
  price: number;
  discountPrice?: number;
  title: string;
  thumbnail?: string;
  addedAt: Date;
}

export interface CourseDetails {
  title: string;
  thumbnail: any;
  shortDescription: string;
  category: string;
  subcategory: string;
  price: number;
  discountPrice?: number;
  isFree: boolean;
}

export interface TemplateDetails {
  title: string;
  thumbnail: any;
  shortDescription: string;
  mediaType: string;
  category: string;
  subcategory: string;
  price: number;
  discountPrice?: number;
  free: boolean;
}

// Updated to match backend CartDto structure
export interface Cart {
  _id: string;
  cartId: string;
  items: CartItem[];
  totalAmount: number;
  totalItems: number;
  lastUpdated: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Backend request DTOs
export interface AddToCartRequest {
  itemType: ItemType;
  itemId: string;
  publicId: string;
  quantity?: number;
  cartId: string;
}

export interface UpdateCartItemRequest {
  itemId: string; // This is the cart item _id, not the course/template id
  quantity: number;
  cartId: string;
}

export interface RemoveFromCartRequest {
  itemId: string; // This is the cart item _id, not the course/template id
  cartId: string;
}

export interface ClearCartRequest {
  cartId: string;
}

export interface MergeCartRequest {
  guestCartId: string;
}

// Backend response structure
export interface BackendCartResponse {
  success: boolean;
  message: string;
  cart: Cart;
}

export interface BackendClearCartResponse {
  success: boolean;
  message: string;
}

// Legacy types for backward compatibility
export type CartResponse = ApiResponse<Cart>;
export type AddToCartResponse = BackendCartResponse;
export type RemoveFromCartResponse = BackendCartResponse;
export type UpdateCartItemQuantityResponse = BackendCartResponse;
export type ClearCartResponse = BackendClearCartResponse;
