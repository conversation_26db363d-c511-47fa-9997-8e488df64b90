import { z } from "zod";
import { type ClientUploadedFileData } from "uploadthing/types";

export enum AccountType {
  EMAIL = "email",
  GOOGLE = "google",
  FACEBOOK = "facebook",
}

export enum Role {
  ADMIN = "admin",
  STAFF = "staff",
}

export enum TwoFactorMethod {
  NONE = "none",
  Email = "email",
  AUTHENTICATOR = "authenticator",
}

export enum UserStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  SUSPENDED = "suspended",
}

// Define the User interface
export interface User {
  email?: string;
  password?: string;
  username: string;
  googleId?: string; // Optional field
  avatarUrl?: string; // Optional field
  emailVerified: Date | null; // Optional field
  accountType: AccountType;
  role: Role;
  twoFactorMethod: TwoFactorMethod;
  twoFactorSecret: string | null;
  status: UserStatus;
}

export type SiteConfig = {
  name: string;
  author: string;
  description: string;
  keywords: Array<string>;
  url: {
    base: string;
    author: string;
  };
  links: {
    github: string;
  };
  ogImage: string;
};

export const SignUpSchema = z
  .object({
    username: z.string().min(2).max(50),
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters long" }),
    confirmPassword: z
      .string()
      .min(8, { message: "Password must be at least 8 characters long" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export const SignInSchema = z.object({
  username: z.string().min(2).max(50),
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters long" }),
});

export const ResetPasswordSchema = z
  .object({
    password: z.string().min(8),
    confirmPassword: z.string().min(8),
    newPassword: z.string().min(8),
    logoutFromOtherDevices: z.boolean(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  })
  .refine((data) => data.newPassword !== data.password, {
    message: "New password must be different from the current password",
    path: ["newPassword"],
  });

export type CurrentUser = {
  id: string;
  name: string;
  email: string;
  picture: string;
};

export interface payload {
  name: string;
  email: string;
  picture?: string;
}

export type SubscriptionPlan = {
  name: string;
  description: string;
  stripePriceId: string;
};

// export type UserSubscriptionPlan = SubscriptionPlan &
//   Pick<User, "stripeCustomerId" | "stripeSubscriptionId"> & {
//     stripeCurrentPeriodEnd: number;
//     isPro: boolean;
//   };

export interface SendWelcomeEmailProps {
  toMail: string;
  userName: string;
}

export interface SendOTPProps extends SendWelcomeEmailProps {
  code: string;
}

export interface Option {
  label: string;
  value: string;
  icon?: React.ComponentType<{ className?: string }>;
  withCount?: boolean;
}

export interface SearchParams {
  [key: string]: string | string[] | undefined;
}

export interface DataTableFilterField<TData> {
  label: string;
  value: keyof TData;
  placeholder?: string;
  options?: Option[];
}

export interface DataTableFilterOption<TData> {
  id: string;
  label: string;
  value: keyof TData;
  options: Option[];
  filterValues?: string[];
  filterOperator?: string;
  isMulti?: boolean;
}

export interface UploadedFile<T = unknown> extends ClientUploadedFileData<T> {}
