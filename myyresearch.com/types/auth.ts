export interface User {
  _id: string;
  username: string;
  avatar?: string;
  email: string;
  emailVerified: boolean;
  accountType: AccountType;
  role: Role;
  twoFactorMethod: TwoFactorMethod;
  twoFactorEnabled: boolean;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  accountType: string;
  role: string;
  twoFactorMethod: string;
  status: string;
}

export enum DocumentStatus {
  Active = "active",
  Archived = "archived",
}

export enum Status {
  Active = "active",
  Inactive = "inactive",
}

export enum TwoFactorMethod {
  Authenticator = "authenticator",
  Email = "email",
  Phone = "phone",
  None = "none",
}

export enum AccountType {
  Email = "email",
  Google = "google",
}

export enum Role {
  User = "user",
  Admin = "admin",
  Instructor = "instructor",
}

export interface LoginCredentials {
  identifier: string;
  password: string;
}

export enum UserType {
  Student = "student",
  Instructor = "instructor",
}

export enum TeachingExperience {
  InPersonInformal = "in-person-informal",
  InPersonProfessional = "in-person-professional",
  Online = "online",
  Other = "other",
}

export enum ExperienceLevel {
  Beginner = "beginner",
  SomeKnowledge = "some-knowledge",
  Experienced = "experienced",
}

export enum AudienceReach {
  NotAtMoment = "not-at-moment",
  SmallFollowing = "small-following",
  SizeableFollowing = "sizeable-following",
}

export interface InstructorRegistrationData {
  teachingExperience: TeachingExperience[];
  teachingExperienceOther?: string;
  experienceLevel: ExperienceLevel;
  audienceReach: AudienceReach;
}

export interface RegisterCredentials {
  username: string;
  email: string;
  password: string;
  userType?: UserType;
  instructorData?: InstructorRegistrationData;
}

export interface AuthResponse {
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
  user: User;
  requires2FA?: boolean;
  userId?: string;
}

export interface RegisterResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface TwoFactorResponse {
  secret: string;
  qrCode: string;
}

export interface TwoFactorVerifyResponse {
  isValid: boolean;
  message: string;
}

export interface TwoFactorVerifyLoginResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface TwoFactorLoginVerifyResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

// UpdateProfileData,

export interface UpdateProfileData {
  username?: string;
  email?: string;
  currentPassword?: string;
  newPassword?: string;
}

// UsernameCheckResponse,

export interface UsernameCheckResponse {
  username: string;
  exists: boolean;
  available: boolean;
}

export interface ForgotPasswordResponse {
  message: string;
}

export interface ResetPasswordResponse {
  message: string;
}

export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
}
