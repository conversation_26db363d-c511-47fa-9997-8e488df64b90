"use client";

import posthog from "posthog-js";
import { PostHog<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON> } from "posthog-js/react";
// import { useSession } from "next-auth/react";
import { usePathname, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { env } from "@/env";

// based on: https://posthog.com/docs/libraries/next-js

if (typeof window !== "undefined" && env.NEXT_PUBLIC_POSTHOG_KEY) {
  posthog.init(env.NEXT_PUBLIC_POSTHOG_KEY, {
    // https://posthog.com/docs/advanced/proxy/nextjs
    api_host: `${window.location.origin}/ingest`,
    capture_pageview: false, // Disable automatic pageview capture, as we capture manually
  });
}

//@ts-ignore
export function PostHogPageview(): JSX.Element {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (pathname) {
      let url = window.origin + pathname;
      if (searchParams && searchParams.toString()) {
        url = url + `?${searchParams.toString()}`;
      }
      posthog.capture("$pageview", {
        $current_url: url,
      });
    }
  }, [pathname, searchParams]);

  return <></>;
}

//@ts-ignore
export function PostHogIdentify(): JSX.Element {
  // const session = useSession();

  // useEffect(() => {
  //   if (session?.data?.user.email)
  //     posthog.identify(session.data.user.email, {
  //       email: session.data.user.email,
  //     });
  // }, [session?.data?.user.email]);

  return <></>;
}

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  return <PHProvider client={posthog}>{children}</PHProvider>;
}
