import mongoose from "mongoose";
import { Course, CourseLevel, CourseStatus, CourseTag } from "@/types/course";
import { DocumentStatus } from "@/types/common";

// Define the TCourse type (with mongoose document fields)
type TCourse = Course & {
  _id: string;
};

const CourseSchema = new mongoose.Schema<TCourse>(
  {
    slug: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    shortDescription: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      default: null,
      nullable: true,
    },
    thumbnail: {
      type: Object,
      required: true,
    },
    category: {
      type: String,
      required: true,
    },
    subcategory: {
      type: String,
      required: true,
    },
    level: {
      type: String,
      enum: Object.values(CourseLevel),
      required: true,
    },
    tag: {
      type: String,
      enum: Object.values(CourseTag),
      required: true,
    },
    duration: {
      type: String,
      required: true,
    },
    udemyUrl: {
      type: String,
      required: true,
    },
    price: {
      type: Number,
      required: true,
    },
    discountPrice: {
      type: Number,
      default: null,
      nullable: true,
    },
    value: {
      type: Number,
      required: true,
    },
    rating: {
      type: Number,
      required: true,
    },
    ratingCount: {
      type: Number,
      required: true,
      default: 0,
    },
    status: {
      type: String,
      enum: Object.values(CourseStatus),
      default: CourseStatus.DRAFT,
      required: true,
    },
    documentStatus: {
      type: String,
      enum: Object.values(DocumentStatus),
      default: DocumentStatus.ACTIVE,
      required: true,
    },
    createdBy: {
      type: String,
      required: true,
    },
    updatedBy: {
      type: String,
      required: true,
    },
  },
  { timestamps: true }
);

// Add an index to ensure slug is unique
CourseSchema.index({ slug: 1 }, { unique: true });

export default mongoose.models.Course ||
  mongoose.model<TCourse>("Course", CourseSchema);
