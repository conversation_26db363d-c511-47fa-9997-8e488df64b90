import mongoose from "mongoose";
import {
  Template,
  TemplateStatus,
  TemplateMediaType,
  TemplateTag,
} from "@/types/template";
import { DocumentStatus } from "@/types/common";

// Define the TTemplate type (with mongoose document fields)
type TTemplate = Template & {
  _id: string;
};

const TemplateSchema = new mongoose.Schema<TTemplate>(
  {
    title: {
      type: String,
      required: true,
    },
    slug: {
      type: String,
      required: true,
    },
    shortDescription: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      default: null,
      nullable: true,
    },
    thumbnail: {
      type: Object,
      required: true,
    },
    mediaType: {
      type: String,
      enum: Object.values(TemplateMediaType),
      required: true,
    },
    category: {
      type: String,
      required: true,
    },
    subcategory: {
      type: String,
      required: true,
    },
    tag: {
      type: String,
      enum: Object.values(TemplateTag),
      required: true,
    },
    price: {
      type: Number,
      required: true,
    },
    discountPrice: {
      type: Number,
      default: null,
      nullable: true,
    },
    value: {
      type: Number,
      required: true,
    },
    version: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(TemplateStatus),
      default: TemplateStatus.DRAFT,
      required: true,
    },
    downloadCount: {
      type: Number,
      default: 0,
      required: true,
    },
    downloadMedia: {
      type: Object,
      required: true,
    },
    previewMedia: {
      type: Object,
      default: null,
      nullable: true,
    },
    free: {
      type: Boolean,
      required: true,
      default: false,
    },
    documentStatus: {
      type: String,
      enum: Object.values(DocumentStatus),
      default: DocumentStatus.ACTIVE,
      required: true,
    },
    createdBy: {
      type: String,
      required: true,
    },
    updatedBy: {
      type: String,
      required: true,
    },
  },
  { timestamps: true }
);

// Add an index to ensure slug is unique
TemplateSchema.index({ slug: 1 }, { unique: true });

export default mongoose.models.Template ||
  mongoose.model<TTemplate>("Template", TemplateSchema);
