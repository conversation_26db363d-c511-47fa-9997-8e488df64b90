import mongoose, { Schema, Document } from "mongoose";

interface Content {
  title: string;
  content?: string;
  userId: string;
  updatedAt: Date;
  createdAt: Date;
}

// Define the ResetTokenSchema
const ContentSchema: Schema = new Schema(
  {
    title: {
      type: String,
      required: true,
    },
    content: {
      type: String,
    },
    userId: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
    collection: "contents",
  },
);

// Add an index to ensure userId is unique
ContentSchema.index({ userId: 1 }, { unique: true });

// Export the ResetToken model
export default mongoose.models.Content ||
  mongoose.model<Content>("Content", ContentSchema);
