import mongoose, { Schema, Document } from "mongoose";

interface Note {
  title: string;
  content?: string;
  userId: string;
}

const noteSchema = new Schema(
  {
    title: {
      type: String,
      required: true,
    },
    content: {
      type: String,
      required: true,
    },
    userId: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
  },
);

// Add an index to ensure userId is unique
noteSchema.index({ userId: 1 }, { unique: true });

// Export the ResetToken model
export default mongoose.models.Note || mongoose.model<Note>("Note", noteSchema);
