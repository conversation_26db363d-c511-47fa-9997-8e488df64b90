const faqs = [
  {
    id: 1,
    question: "Who can join MyResearch courses?",
    answer:
      "Anyone can join whether you're a student, teacher, or professional wanting to improve your skills.",
  },
  {
    id: 2,
    question: "Are your courses self-paced?",
    answer:
      "Yes, all courses are self-paced, so you can study anytime that suits your schedule.",
  },
  {
    id: 3,
    question: "How do I sign up for a course?",
    answer:
      "Just visit our website, pick a course, and follow the easy steps to enroll.",
  },
  {
    id: 4,
    question: "Do I get a certificate after finishing a course?",
    answer:
      "Yes, after completing a course, you'll receive a certificate to showcase your achievement.",
  },
  {
    id: 5,
    question: "Are there any course requirements?",
    answer:
      "Some advanced courses may need basic knowledge, but all details are listed in the course description.",
  },
  {
    id: 6,
    question: "What kind of templates do you offer?",
    answer:
      "We offer assignment and presentation templates across subjects like business, IT, health, and more.",
  },
  {
    id: 7,
    question: "Can I get help with my research?",
    answer:
      "Yes, we provide expert research help to guide you through your academic projects.",
  },
  {
    id: 8,
    question: "Do you offer assignment help services?",
    answer:
      "Yes, we offer personalized support to help you complete assignments and improve your grades.",
  },
  {
    id: 9,
    question: "How do I contact support if I need help?",
    answer:
      "Just email our support team anytime, and we'll respond quickly to assist you.",
  },
];
export default function FaqSection() {
  return (
    <div className="bg-white">
      <div className="mx-auto max-w-7xl px-6 py-16 sm:py-24 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl sm:text-4xl font-bold font-heading leading-10 tracking-tight text-gray-900">
            Frequently asked questions
          </h2>
          <p className="mt-6 text-base leading-7 text-gray-600">
            Have a different question and can't find the answer you're looking
            for? Reach out to our support team by{" "}
            <a
              href="#"
              className="font-semibold text-indigo-600 hover:text-indigo-500"
            >
              sending us an email
            </a>{" "}
            and we'll get back to you as soon as we can.
          </p>
        </div>
        <div className="mt-20">
          <dl className="space-y-16 sm:grid sm:grid-cols-2 sm:gap-x-6 sm:gap-y-16 sm:space-y-0 lg:grid-cols-3 lg:gap-x-10">
            {faqs.map((faq) => (
              <div key={faq.id}>
                <dt className="text-base font-semibold leading-7 text-gray-900">
                  {faq.question}
                </dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  {faq.answer}
                </dd>
              </div>
            ))}
          </dl>
        </div>
      </div>
    </div>
  );
}
