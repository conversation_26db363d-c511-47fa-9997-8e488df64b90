import Image from "next/image";
import { testimonials as allTestimonials } from "@/data";
import Link from "next/link";

// // Select a featured testimonial from the data
// const featuredTestimonial = {
//   feedback:
//     allTestimonials.find((t) => t.author.name === "<PERSON>")?.feedback || "",
//   author: {
//     name: "<PERSON>",
//     country: "Australia",
//     imageUrl: "/images/testimonials/6.png",
//   },
// };

// Organize testimonials in the required structure for the layout
// 3 rows, 4 columns = 12 testimonials
const testimonials = [
  // Row 1
  [
    // Column 1
    {
      feedback:
        '"I found MyResearch while searching for online statistics courses. The platform is user-friendly and has a great curriculum. It helped me gain skills that boosted my career."',
      author: {
        name: "Trivo Julion",
        country: "UK",
        imageUrl: "/images/testimonials/1.png",
      },
    },
    // Column 2
    {
      feedback:
        '"As a marketing professional, keeping up with trends is very important. MyResearch provided a range of marketing courses that were very beneficial to my career."',
      author: {
        name: "<PERSON><PERSON>",
        country: "UK",
        imageUrl: "/images/testimonials/2.png",
      },
    },
    // Column 3
    {
      feedback:
        '"As a teacher, I needed resources to improve my teaching methods. MyResearch offered excellent courses for educators. The content was engaging and relevant."',
      author: {
        name: "Nimani Mendis",
        country: "UK",
        imageUrl: "/images/testimonials/4.png",
      },
    },
    // Column 4
    {
      feedback:
        '"My experience with MyResearch was exceptional. The platform offers a wide range of courses and allows me to study at my own pace, which fits well with my schedule."',
      author: {
        name: "Prarthana",
        country: "UK",
        imageUrl: "/images/testimonials/5.png",
      },
    },
  ],
  // Row 2
  [
    // Column 1
    {
      feedback:
        '"I wanted to learn data science but needed beginner-friendly resources. MyResearch had a great introductory course that made complex topics easy to understand."',
      author: {
        name: "Andrew",
        country: "Australia",
        imageUrl: "/images/testimonials/6.png",
      },
    },
    // Column 2
    {
      feedback:
        '"As a small business owner, I needed to improve my operations. MyResearch provided useful business courses that helped grow my business."',
      author: {
        name: "Ethan Brown",
        country: "Australia",
        imageUrl: "/images/testimonials/7.png",
      },
    },
    // Column 3
    {
      feedback:
        '"As an international student, I wanted access to top educational resources. MyResearch offered high-quality courses in various fields, helping me pursue my academic dreams."',
      author: {
        name: "De Silva",
        country: "UK",
        imageUrl: "/images/testimonials/8.png",
      },
    },
    // Column 4
    {
      feedback:
        '"As an aspiring entrepreneur, I found MyResearch\'s business courses very valuable. They helped me develop my business skills and leadership abilities."',
      author: {
        name: "Nipuni",
        country: "UK",
        imageUrl: "/images/testimonials/10.png",
      },
    },
  ],
  // Row 3
  [
    // Column 1
    {
      feedback:
        '"As a student, MyResearch\'s courses helped supplement my studies. The platform offers a wide range of subjects with interactive quizzes and assignments."',
      author: {
        name: "Sovejeta",
        country: "Albania",
        imageUrl: "/images/testimonials/12.png",
      },
    },
    // Column 2
    {
      feedback:
        '"Studying with MyResearch was pleasant. The courses were interesting and easy to understand, which helped me grasp complex concepts."',
      author: {
        name: "Jessica",
        country: "China",
        imageUrl: "/images/testimonials/13.png",
      },
    },
    // Column 3
    {
      feedback:
        '"MyResearch is a fantastic resource for my studies. The courses are rich in content and well-presented. I recommend it to teachers and learners."',
      author: {
        name: "Saravanan",
        country: "India",
        imageUrl: "/images/testimonials/15.png",
      },
    },
    // Column 4
    {
      feedback:
        '"I\'m impressed by the quality of courses on MyResearch. The platform is user-friendly, and the instructors are knowledgeable."',
      author: {
        name: "Marie",
        country: "France",
        imageUrl: "/images/testimonials/18.png",
      },
    },
  ],
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export default function TestimonialSection() {
  return (
    <div className="relative isolate bg-white pb-32 pt-24 sm:pt-32">
      <div
        className="absolute inset-x-0 top-1/2 -z-10 -translate-y-1/2 transform-gpu overflow-hidden opacity-30 blur-3xl"
        aria-hidden="true"
      >
        <div
          className="ml-[max(50%,38rem)] aspect-[1313/771] w-[82.0625rem] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc]"
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
        />
      </div>
      <div
        className="absolute inset-x-0 top-0 -z-10 flex transform-gpu overflow-hidden pt-32 opacity-25 blur-3xl sm:pt-40 xl:justify-end"
        aria-hidden="true"
      >
        <div
          className="ml-[-22rem] aspect-[1313/771] w-[82.0625rem] flex-none origin-top-right rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] xl:ml-0 xl:mr-[calc(50%-12rem)]"
          style={{
            clipPath:
              "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
          }}
        />
      </div>
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-xl text-center">
          <h2 className="text-lg font-semibold leading-8 tracking-tight text-indigo-600">
            Testimonials
          </h2>
          <p className="mt-2 font-heading text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            We have worked with thousands of amazing people
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 text-sm leading-6 text-gray-900 sm:mt-20 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:mx-0 xl:max-w-none">
          {testimonials.flat().map((testimonial, index) => (
            <figure
              key={index}
              className="rounded-2xl bg-white p-6 shadow-lg ring-1 ring-gray-900/5"
            >
              <blockquote className="text-gray-900">
                {testimonial.feedback.length > 150
                  ? `${testimonial.feedback.slice(0, 150)}...`
                  : testimonial.feedback}{" "}
              </blockquote>
              <figcaption className="mt-6 flex items-center gap-x-4">
                <Image
                  className="h-10 w-10 rounded-full bg-gray-50"
                  src={testimonial.author.imageUrl}
                  alt=""
                  width={40}
                  height={40}
                />
                <div>
                  <div className="font-semibold">{testimonial.author.name}</div>
                  <div className="text-gray-600">{`@${testimonial.author.country}`}</div>
                </div>
              </figcaption>
            </figure>
          ))}
        </div>

        <div className="mt-16 flex justify-center">
          <Link
            href="/testimonials"
            className="rounded-md bg-indigo-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-colors duration-200"
          >
            See More Testimonials
          </Link>
        </div>
      </div>
    </div>
  );
}
