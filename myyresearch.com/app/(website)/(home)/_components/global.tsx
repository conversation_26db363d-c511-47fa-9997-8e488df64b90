"use client";
import { WorldMap } from "@/components/ui/world-map";
import { motion } from "framer-motion";

const SRI_LANKA_LAT = -10.9271;
const SRI_LANKA_LNG = 82.8612;

// Country flag URLs - using country code flags from flagcdn.com
const FLAGS = {
  UK: "https://flagcdn.com/w80/gb.png",
  USA: "https://flagcdn.com/w80/us.png",
  NZ: "https://flagcdn.com/w80/nz.png",
  AU: "https://flagcdn.com/w80/au.png",
  FI: "https://flagcdn.com/w80/fi.png",
  SE: "https://flagcdn.com/w80/se.png",
  IN: "https://flagcdn.com/w80/in.png",
  CA: "https://flagcdn.com/w80/ca.png",
  DE: "https://flagcdn.com/w80/de.png",
  FR: "https://flagcdn.com/w80/fr.png",
  DK: "https://flagcdn.com/w80/dk.png",
  IT: "https://flagcdn.com/w80/it.png",
  KR: "https://flagcdn.com/w80/kr.png",
  JP: "https://flagcdn.com/w80/jp.png",
  BR: "https://flagcdn.com/w80/br.png",
  AL: "https://flagcdn.com/w80/al.png",
  ID: "https://flagcdn.com/w80/id.png",
  TH: "https://flagcdn.com/w80/th.png",
  MY: "https://flagcdn.com/w80/my.png",
  AE: "https://flagcdn.com/w80/ae.png",
  KW: "https://flagcdn.com/w80/kw.png",
  QA: "https://flagcdn.com/w80/qa.png",
  SG: "https://flagcdn.com/w80/sg.png",
  LK: "https://flagcdn.com/w80/lk.png",
};

export function Global() {
  return (
    <div className=" dark:bg-black bg-white w-full">
      <div className="max-w-7xl mx-auto text-center">
        <p className="font-bold font-heading text-xl md:text-4xl dark:text-white text-black">
          Global Research Excellence Worldwide
        </p>
        <p className="text-sm md:text-lg text-neutral-500 max-w-2xl mx-auto py-4">
          We proudly serve clients worldwide, delivering exceptional research
          services across all continents. Our global reach enables us to assist
          you wherever you are.
        </p>
      </div>
      <WorldMap
        dots={[
          // United Kingdom
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            }, // Sri Lanka
            end: {
              lat: 51.5074,
              lng: -0.1278,
              label: "London",
              avatar: FLAGS.UK,
            }, // London
          },
          // USA
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 38.9072,
              lng: -77.0369,
              label: "Washington D.C.",
              avatar: FLAGS.USA,
            }, // Washington D.C.
          },
          // New Zealand
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: -41.2865,
              lng: 174.7762,
              label: "Wellington",
              avatar: FLAGS.NZ,
            }, // Wellington
          },
          // Australia
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: -35.2809,
              lng: 149.13,
              label: "Canberra",
              avatar: FLAGS.AU,
            }, // Canberra
          },
          // Finland
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 60.1699,
              lng: 24.9384,
              label: "Helsinki",
              avatar: FLAGS.FI,
            }, // Helsinki
          },
          // Sweden
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 59.3293,
              lng: 18.0686,
              label: "Stockholm",
              avatar: FLAGS.SE,
            }, // Stockholm
          },
          // India
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 28.6139,
              lng: 77.209,
              label: "New Delhi",
              avatar: FLAGS.IN,
            }, // New Delhi
          },
          // Canada
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 45.4215,
              lng: -75.6972,
              label: "Ottawa",
              avatar: FLAGS.CA,
            }, // Ottawa
          },
          // Germany
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: { lat: 52.52, lng: 13.405, label: "Berlin", avatar: FLAGS.DE }, // Berlin
          },
          // France
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 48.8566,
              lng: 2.3522,
              label: "Paris",
              avatar: FLAGS.FR,
            }, // Paris
          },
          // Denmark
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 55.6761,
              lng: 12.5683,
              label: "Copenhagen",
              avatar: FLAGS.DK,
            }, // Copenhagen
          },
          // Italy
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 41.9028,
              lng: 12.4964,
              label: "Rome",
              avatar: FLAGS.IT,
            }, // Rome
          },
          // South Korea
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 37.5665,
              lng: 126.978,
              label: "Seoul",
              avatar: FLAGS.KR,
            }, // Seoul
          },
          // Japan
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 35.6762,
              lng: 139.6503,
              label: "Tokyo",
              avatar: FLAGS.JP,
            }, // Tokyo
          },
          // Brazil
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: -15.7975,
              lng: -47.8919,
              label: "Brasília",
              avatar: FLAGS.BR,
            }, // Brasília
          },
          // Albania
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 41.3275,
              lng: 19.8187,
              label: "Tirana",
              avatar: FLAGS.AL,
            }, // Tirana
          },
          // Indonesia
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: -6.2088,
              lng: 106.8456,
              label: "Jakarta",
              avatar: FLAGS.ID,
            }, // Jakarta
          },
          // Thailand
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 13.7563,
              lng: 100.5018,
              label: "Bangkok",
              avatar: FLAGS.TH,
            }, // Bangkok
          },
          // Malaysia
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 3.139,
              lng: 101.6869,
              label: "Kuala Lumpur",
              avatar: FLAGS.MY,
            }, // Kuala Lumpur
          },
          // Dubai (UAE)
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 25.2769,
              lng: 55.2962,
              label: "Dubai",
              avatar: FLAGS.AE,
            }, // Dubai
          },
          // Kuwait
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 29.3759,
              lng: 47.9774,
              label: "Kuwait City",
              avatar: FLAGS.KW,
            }, // Kuwait City
          },
          // Qatar
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: { lat: 25.2854, lng: 51.531, label: "Doha", avatar: FLAGS.QA }, // Doha
          },
          // Singapore
          {
            start: {
              lat: SRI_LANKA_LAT,
              lng: SRI_LANKA_LNG,
              label: "Sri Lanka",
              avatar: FLAGS.LK,
            },
            end: {
              lat: 1.3521,
              lng: 103.8198,
              label: "Singapore",
              avatar: FLAGS.SG,
            }, // Singapore
          },
        ]}
      />
    </div>
  );
}
