"use client";

import dynamic from "next/dynamic";
import { ViewMode } from "@/types/view-mode";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface CourseSelectionWrapperProps {
  isDemo?: boolean;
  viewMode?: ViewMode;
}

// Create a loading component using shadcn Skeleton
function CourseSelectionSkeleton() {
  return (
    <div className="pt-16">
      <div className="container mx-auto lg:px-8 px-4 border-b border-gray-200">
        <div className="text-left">
          <Skeleton className="h-6 w-32 mb-2" />
          <Skeleton className="h-10 w-96 mb-5" />
        </div>
        <div className="flex space-x-8 mt-5 pb-4">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-8 w-24" />
          ))}
        </div>
      </div>

      <div className="bg-[#F7F9FA] pb-10">
        <div className="container mx-auto lg:px-8 px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 pt-10 mb-8">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Skeleton key={i} className="h-16 w-full rounded-full" />
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="h-[400px]">
                <CardHeader className="space-y-2">
                  <Skeleton className="w-full h-48" />
                  <Skeleton className="h-6 w-4/5" />
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-4/5" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-5 w-16" />
                    <Skeleton className="h-4 w-12" />
                  </div>
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-4 w-12" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Dynamic import of the CourseSelection component
const DynamicCourseSelection = dynamic(() => import("./course-selection"), {
  loading: () => <CourseSelectionSkeleton />,
  ssr: false,
});

export default function CourseSelectionWrapper({
  isDemo = false,
  viewMode = ViewMode.Carousel,
}: CourseSelectionWrapperProps) {
  return <DynamicCourseSelection />;
}
