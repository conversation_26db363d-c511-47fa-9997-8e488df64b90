import Image from "next/image";

// import logoLaravel from "@/images/logos/laravel.svg";
// import logoMirage from "@/images/logos/mirage.svg";
// import logoStatamic from "@/images/logos/statamic.svg";
// import logoStaticKit from "@/images/logos/statickit.svg";
// import logoTransistor from "@/images/logos/transistor.svg";
// import logoTuple from "@/images/logos/tuple.svg";
import { Container } from "./container";
import * as React from "react";

export function Hero() {
  return (
    <Container className="text-center pt-16">
      <div className="">
        <p className="font-display text-base text-slate-900">
          Tools we use to ensure your success
        </p>
        <ul
          role="list"
          className="mt-8 flex items-center justify-center gap-x-8 sm:flex-col sm:gap-x-0 sm:gap-y-10 xl:flex-row xl:gap-x-12 xl:gap-y-0"
        >
          {[
            [
              { name: "Transistor", logo: "images/tools/minitab.png" },
              { name: "Tu<PERSON>", logo: "images/logos/mirage.svg" },
              { name: "StaticKit", logo: "images/logos/statamic.svg" },
            ],
            [
              { name: "Transistor", logo: "images/logos/laravel.svg" },
              { name: "Tuple", logo: "images/logos/mirage.svg" },
              { name: "StaticKit", logo: "images/logos/statamic.svg" },
            ],
          ].map((group, groupIndex) => (
            <li key={groupIndex}>
              <ul
                role="list"
                className="flex flex-col items-center gap-y-8 sm:flex-row sm:gap-x-12 sm:gap-y-0"
              >
                {group.map((company) => (
                  <li key={company.name} className="flex">
                    <Image
                      src={company.logo}
                      alt={company.name}
                      width={100}
                      height={100}
                      unoptimized
                      className={"grayscale"}
                    />
                  </li>
                ))}
              </ul>
            </li>
          ))}
        </ul>
      </div>
    </Container>
  );
}
