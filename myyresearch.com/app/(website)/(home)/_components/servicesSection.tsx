"use client";

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

interface Service {
  title: string;
  image: string;
  bgColor: string;
  href: string;
  description?: string;
}

// assignment-help.jpeg            online-courses.jpeg             presentation-templates.jpeg     research-help.jpeg
// assignment-templatess.jpeg      onlinsdsde-courses.jpeg         presentsdsdation-templates.jpeg

const academicServices: Service[] = [
  {
    title: "Online Courses",
    image: "/images/academic-services/online-courses.jpeg",
    bgColor: "#0F33A1", // Dark green
    href: "/online-courses",
  },
  {
    title: "Research Help",
    image: "/images/academic-services/research-help.jpeg",
    bgColor: "#8B66A0", // Orange
    href: "/research-help",
  },
  {
    title: "Assignment Help",
    image: "/images/academic-services/assignment-help.jpeg",
    bgColor: "#338462",
    href: "/assignment-help",
  },
  {
    title: "Assignment Templates",
    image: "/images/academic-services/assignment-templates.jpeg",
    bgColor: "#40AFDF", // Burgundy
    href: "/assignment-templates",
  },
  {
    title: "Presentation Templates",
    image: "/images/academic-services/presentation-templates.jpeg",
    bgColor: "#DD6B20",
    href: "/presentation-templates",
  },

  // Add more services as needed
];

// business-documents.jpeg business-research.jpeg  content-writting.jpeg   tech-data.jpeg

const businessServices: Service[] = [
  {
    title: "Business Research & Analysis",
    description: "Comprehensive research and analysis for your business",
    image: "/images/business-services/business-research.jpeg", // Reusing existing icon, replace with business-specific icon
    bgColor: "#1E5D2B", // Blue
    href: "/business-services/research-analysis",
  },
  {
    title: "Tech & Data Services",
    description: "Technical and data solutions for your business",
    image: "/images/business-services/tech-data.jpeg", // Reusing existing icon, replace with business-specific icon
    bgColor: "#BA5828", // Green
    href: "/business-services/tech-data",
  },
  {
    title: "Business Document Templates",
    description: "Professional templates for business documents",
    image: "/images/business-services/business-documents.jpeg", // Reusing existing icon, replace with business-specific icon
    bgColor: "#771939", // Purple
    href: "/business-services/document-templates",
  },
  {
    title: "Content Writing & Marketing",
    description: "Professional content creation and marketing services",
    image: "/images/business-services/content-writting.jpeg", // Reusing existing icon, replace with business-specific icon
    bgColor: "#F58948", // Orange
    href: "/business-services/content-marketing",
  },
];

const ServicesSection = () => {
  const [activeTab, setActiveTab] = useState<"academic" | "business">(
    "academic"
  );

  const services =
    activeTab === "academic" ? academicServices : businessServices;

  return (
    <section className="py-24">
      <div className="container mx-auto lg:px-8 px-4">
        <div className="text-left mb-5">
          <h2 className="text-lg font-semibold leading-8 tracking-tight text-indigo-600">
            Our Services
          </h2>
          <p className="mt-2 font-heading text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            <span className="sm:hidden">Comprehensive Solutions</span>
            <span className="hidden sm:inline">
              Comprehensive Solutions for Your Academic and Business Needs
            </span>
          </p>
          <p className="mt-4 text-lg leading-8 text-gray-600">
            Choose from our range of specialized services designed to help you
            excel in your studies and business
          </p>
        </div>

        {/* Tab bar */}
        <div className="flex justify-start mb-8 border-b">
          <button
            onClick={() => setActiveTab("academic")}
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "academic"
                ? "text-indigo-600 border-b-2 border-indigo-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Academic Services
          </button>
          <button
            onClick={() => setActiveTab("business")}
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "business"
                ? "text-indigo-600 border-b-2 border-indigo-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Business Services
          </button>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {services.map((service, index) => (
            <Link href={service.href} key={index} className="group">
              <div
                style={{ backgroundColor: service.bgColor }}
                className="rounded-[20px] flex flex-col p-2 h-full relative overflow-hidden"
              >
                <h3 className="text-white text-left text-xl pl-2 pt-2 font-bold mb-2 relative z-10">
                  {(() => {
                    const words = service.title.split(" ");
                    const firstWord = words[0];
                    const restOfTitle = words.slice(1).join(" ");
                    return (
                      <>
                        <span className="block">{firstWord}</span>
                        {restOfTitle && (
                          <span className="block">{restOfTitle}</span>
                        )}
                      </>
                    );
                  })()}
                </h3>
                {/* {service.description && (
                  <p className="text-white text-xs pl-2 opacity-80 relative z-10">
                    {service.description}
                  </p>
                )} */}
                <div className="w-full relative mt-5 flex justify-center items-center">
                  <Image
                    src={service.image}
                    alt={service.title}
                    className="rounded-[15px]"
                    width={300}
                    height={200}
                    style={{ objectFit: "contain", aspectRatio: "3/2" }}
                  />
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
