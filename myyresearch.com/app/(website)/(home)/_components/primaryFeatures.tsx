"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import { Tab } from "@headlessui/react";
import clsx from "clsx";

import { Container } from "./container";
import { ScrollArea } from "@/components/ui/scroll-area";

// Import mockup components
import {
  OnlineCoursesMockup,
  ResearchHelpMockup,
  AssignmentsHelpMockup,
  AssignmentTemplatesMockup,
  PresentationTemplatesMockup,
} from "./mockups";

const features = [
  {
    title: "Online Courses",
    description:
      "Explore a variety of subjects designed to enhance your skills and knowledge in different areas through our online course platform. ",
    mockup: OnlineCoursesMockup,
  },
  {
    title: "Research Help",
    description:
      "Explore a variety of subjects designed to enhance your skills and knowledge in different areas through our online course platform. ",
    mockup: ResearchHelpMockup,
  },
  {
    title: "Assignments Help",
    description:
      "Receive expert help and personalized support to create top-quality assignments and enhance your academic performance. ",
    mockup: AssignmentsHelpMockup,
  },
  {
    title: "Assignment Templates",
    description:
      "Use our professionally designed templates to improve the content and presentation of your assignments. ",
    mockup: AssignmentTemplatesMockup,
  },
  {
    title: "Presentation Templates",
    description:
      "Make your presentations stand out with our collection of attractive templates, designed for various topics to impress your audience. ",
    mockup: PresentationTemplatesMockup,
  },
];

// Mac window mockup component
function MacWindowMockup({
  children,
  title = "myy research",
}: {
  children: React.ReactNode;
  title?: string;
}) {
  return (
    <div className="overflow-hidden rounded-xl bg-white shadow-xl">
      {/* Mac window header */}
      <div className="relative bg-gray-100 px-4 py-2 flex items-center border-b border-gray-200">
        <div className="flex space-x-2 absolute left-4">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
        </div>
        <div className="mx-auto text-xs text-gray-500 font-medium">{title}</div>
      </div>
      {/* Content - removed ScrollArea with fixed height to prevent y-axis scrolling */}
      <div className="h-[600px] md:h-[700px] lg:h-[800px] overflow-x-auto overflow-y-hidden">
        {children}
      </div>
    </div>
  );
}

export function PrimaryFeatures() {
  let [tabOrientation, setTabOrientation] = useState<"horizontal" | "vertical">(
    "horizontal"
  );

  useEffect(() => {
    let lgMediaQuery = window.matchMedia("(min-width: 1024px)");

    function onMediaQueryChange({ matches }: { matches: boolean }) {
      setTabOrientation(matches ? "vertical" : "horizontal");
    }

    onMediaQueryChange(lgMediaQuery);
    lgMediaQuery.addEventListener("change", onMediaQueryChange);

    return () => {
      lgMediaQuery.removeEventListener("change", onMediaQueryChange);
    };
  }, []);

  return (
    <section
      id="features"
      aria-label="Features for running your books"
      className="relative overflow-hidden bg-blue-600 pb-28 pt-20 sm:py-32"
    >
      <Image
        className="absolute left-1/2 top-1/2 max-w-none translate-x-[-44%] translate-y-[-42%]"
        src={"/images/background-features.jpg"}
        alt=""
        width={2245}
        height={1636}
        unoptimized
      />
      <Container className="relative">
        <div className="max-w-2xl md:mx-auto md:text-center xl:max-w-none">
          <h2 className="font-display text-3xl font-heading tracking-tight text-white sm:text-4xl md:text-5xl">
            Unlocking Knowledge with One Click{" "}
          </h2>
          <p className="mt-6 text-lg tracking-tight text-blue-100">
            Join us to enhance your skills and achieve academic and professional
            excellence
          </p>
        </div>
        <Tab.Group
          as="div"
          className="mt-16 grid grid-cols-1 items-center gap-y-2 pt-10 sm:gap-y-6 md:mt-20 lg:grid-cols-12 lg:pt-0"
          vertical={tabOrientation === "vertical"}
        >
          {({ selectedIndex }) => (
            <>
              <div className="-mx-4 flex overflow-x-auto pb-4 sm:mx-0 sm:overflow-visible sm:pb-0 lg:col-span-5">
                <Tab.List className="relative z-10 flex gap-x-4 whitespace-nowrap px-4 sm:mx-auto sm:px-0 lg:mx-0 lg:block lg:gap-x-0 lg:gap-y-1 lg:whitespace-normal">
                  {features.map((feature, featureIndex) => (
                    <div
                      key={feature.title}
                      className={clsx(
                        "group relative rounded-full px-4 py-1 lg:rounded-l-xl lg:rounded-r-none lg:p-6",
                        selectedIndex === featureIndex
                          ? "bg-white lg:bg-white/10 lg:ring-1 lg:ring-inset lg:ring-white/10"
                          : "hover:bg-white/10 lg:hover:bg-white/5"
                      )}
                    >
                      <h3>
                        <Tab
                          className={clsx(
                            "font-display text-lg ui-not-focus-visible:outline-none outline-0",
                            selectedIndex === featureIndex
                              ? "text-blue-600 lg:text-white"
                              : "text-blue-100 hover:text-white lg:text-white"
                          )}
                        >
                          <span className="absolute inset-0 rounded-full lg:rounded-l-xl lg:rounded-r-none" />
                          {feature.title}
                        </Tab>
                      </h3>
                      <p
                        className={clsx(
                          "mt-2 hidden text-sm lg:block",
                          selectedIndex === featureIndex
                            ? "text-white"
                            : "text-blue-100 group-hover:text-white"
                        )}
                      >
                        {feature.description}
                      </p>
                    </div>
                  ))}
                </Tab.List>
              </div>
              <Tab.Panels className="lg:col-span-7">
                {features.map((feature) => (
                  <Tab.Panel key={feature.title} unmount={false}>
                    <div className="relative sm:px-6 lg:hidden">
                      <div className="absolute -inset-x-4 bottom-[-4.25rem] top-[-6.5rem] bg-white/10 ring-1 ring-inset ring-white/10 sm:inset-x-0 sm:rounded-t-xl" />
                      <p className="relative mx-auto max-w-2xl text-base text-white sm:text-center">
                        {feature.description}
                      </p>
                    </div>
                    <div className="mt-10 w-[45rem] overflow-hidden rounded-xl shadow-xl shadow-blue-900/20 sm:w-auto lg:mt-0 lg:w-[67.8125rem]">
                      <MacWindowMockup title={feature.title}>
                        <feature.mockup />
                      </MacWindowMockup>
                    </div>
                  </Tab.Panel>
                ))}
              </Tab.Panels>
            </>
          )}
        </Tab.Group>
      </Container>
    </section>
  );
}
