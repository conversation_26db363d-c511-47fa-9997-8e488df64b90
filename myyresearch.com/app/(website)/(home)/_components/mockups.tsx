import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { ChevronLeft, ChevronRight, FolderSearch } from "lucide-react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

// Define template types
interface MockTemplate {
  _id: string;
  title: string;
  publicId: string;
  shortDescription: string;
  thumbnail: string;
  mediaType: string;
  category: string;
  subcategory: string;
  tag: string;
  price: number;
  discountPrice: number | null;
  downloadCount: number;
  free: boolean;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  creatorType: string;
}

// Mock template data
const mockAssignmentTemplates: MockTemplate[] = [
  {
    _id: "at1",
    title: "Essay Template",
    publicId: "essay-template",
    shortDescription:
      "Professional template with structured sections and formatting",
    thumbnail: "templates/assignment/essay.jpg",
    mediaType: "doc",
    category: "assignment",
    subcategory: "essay",
    tag: "best-seller",
    price: 9.99,
    discountPrice: 7.99,
    downloadCount: 1250,
    free: false,
    status: "published",
    createdAt: new Date(),
    updatedAt: new Date(),
    creatorType: "admin",
  },
  {
    _id: "at2",
    title: "Research Paper Template",
    publicId: "research-paper-template",
    shortDescription: "Comprehensive template with guidelines and examples",
    thumbnail: "templates/assignment/research.jpg",
    mediaType: "doc",
    category: "assignment",
    subcategory: "research",
    tag: "trending",
    price: 12.99,
    discountPrice: null,
    downloadCount: 850,
    free: false,
    status: "published",
    createdAt: new Date(),
    updatedAt: new Date(),
    creatorType: "admin",
  },
  {
    _id: "at3",
    title: "Case Study Template",
    publicId: "case-study-template",
    shortDescription:
      "Detailed template for business and academic case studies",
    thumbnail: "templates/assignment/case-study.jpg",
    mediaType: "doc",
    category: "assignment",
    subcategory: "case-study",
    tag: "none",
    price: 14.99,
    discountPrice: 11.99,
    downloadCount: 620,
    free: false,
    status: "published",
    createdAt: new Date(),
    updatedAt: new Date(),
    creatorType: "admin",
  },
  {
    _id: "at4",
    title: "Lab Report Template",
    publicId: "lab-report-template",
    shortDescription:
      "Scientific lab report template with sections for methodology and results",
    thumbnail: "templates/assignment/lab-report.jpg",
    mediaType: "doc",
    category: "assignment",
    subcategory: "lab-report",
    tag: "new-arrival",
    price: 8.99,
    discountPrice: null,
    downloadCount: 430,
    free: false,
    status: "published",
    createdAt: new Date(),
    updatedAt: new Date(),
    creatorType: "admin",
  },
];

const mockPresentationTemplates: MockTemplate[] = [
  {
    _id: "pt1",
    title: "Business Proposal Presentation",
    publicId: "business-proposal-presentation",
    shortDescription:
      "Professional slides with modern design for business proposals",
    thumbnail: "templates/presentation/business-proposal.jpg",
    mediaType: "ppt",
    category: "presentation",
    subcategory: "business",
    tag: "best-seller",
    price: 19.99,
    discountPrice: 14.99,
    downloadCount: 2150,
    free: false,
    status: "published",
    createdAt: new Date(),
    updatedAt: new Date(),
    creatorType: "admin",
  },
  {
    _id: "pt2",
    title: "Academic Research Presentation",
    publicId: "academic-research-presentation",
    shortDescription:
      "Clean and professional template for academic presentations",
    thumbnail: "templates/presentation/academic-research.jpg",
    mediaType: "ppt",
    category: "presentation",
    subcategory: "academic",
    tag: "trending",
    price: 16.99,
    discountPrice: null,
    downloadCount: 1850,
    free: false,
    status: "published",
    createdAt: new Date(),
    updatedAt: new Date(),
    creatorType: "admin",
  },
  {
    _id: "pt3",
    title: "Project Overview Presentation",
    publicId: "project-overview-presentation",
    shortDescription: "Visual template for project presentations and updates",
    thumbnail: "templates/presentation/project-overview.jpg",
    mediaType: "ppt",
    category: "presentation",
    subcategory: "project",
    tag: "new-arrival",
    price: 14.99,
    discountPrice: 12.99,
    downloadCount: 920,
    free: false,
    status: "published",
    createdAt: new Date(),
    updatedAt: new Date(),
    creatorType: "admin",
  },
  {
    _id: "pt4",
    title: "Marketing Plan Presentation",
    publicId: "marketing-plan-presentation",
    shortDescription:
      "Creative template for marketing strategies and campaigns",
    thumbnail: "templates/presentation/marketing-plan.jpg",
    mediaType: "ppt",
    category: "presentation",
    subcategory: "marketing",
    tag: "none",
    price: 18.99,
    discountPrice: 15.99,
    downloadCount: 1230,
    free: false,
    status: "published",
    createdAt: new Date(),
    updatedAt: new Date(),
    creatorType: "admin",
  },
];

// Mock TemplateCard component (simplified version)
interface MockTemplateCardProps {
  template: MockTemplate;
  showButtons?: boolean;
}

function MockTemplateCard({
  template,
  showButtons = true,
}: MockTemplateCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
      <div className="relative w-full overflow-hidden rounded-t-lg aspect-[16/9] bg-gray-200">
        <div className="absolute inset-0 flex items-center justify-center text-gray-400">
          {template.mediaType === "doc" ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"
              />
            </svg>
          )}
        </div>
      </div>

      {/* Tag Badge */}
      {template.tag !== "none" && (
        <div className="absolute top-2 right-2">
          <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-500 text-white">
            {template.tag.replace("-", " ")}
          </span>
        </div>
      )}

      {/* Content */}
      <div className="p-4">
        <div className="flex items-start justify-between gap-2">
          <h3 className="font-semibold text-gray-900 line-clamp-1">
            {template.title}
          </h3>
          <span className="text-xs text-gray-500">{template.mediaType}</span>
        </div>

        <p className="mt-1 text-sm text-gray-500 line-clamp-2">
          {template.shortDescription}
        </p>

        {/* Price and Downloads */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            {template.discountPrice ? (
              <>
                <span className="text-lg font-bold text-blue-600">
                  ${template.discountPrice}
                </span>
                <span className="text-sm text-gray-400 line-through">
                  ${template.price}
                </span>
              </>
            ) : (
              <span className="text-lg font-bold text-blue-600">
                ${template.price}
              </span>
            )}
          </div>

          <div className="flex items-center gap-1 text-sm text-gray-500">
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3v-13"
              />
            </svg>
            <span>{template.downloadCount}</span>
          </div>
        </div>
      </div>

      {/* Buttons */}
      {showButtons && (
        <div className="mt-4 flex gap-2 p-4 pt-0">
          <Button className="flex-1" variant="default">
            Add to Cart
          </Button>
          <Button className="flex-1" variant="outline">
            Preview
          </Button>
        </div>
      )}
    </div>
  );
}

// Mock TemplateCardSkeleton component
function MockTemplateCardSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Thumbnail Skeleton */}
      <div className="relative w-full aspect-video rounded-t-lg overflow-hidden">
        <div className="w-full h-full bg-gray-200 animate-pulse" />
      </div>

      {/* Content Skeleton */}
      <div className="p-4">
        {/* Title and Media Type */}
        <div className="flex items-start justify-between gap-2">
          <div className="h-6 w-2/3 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
        </div>

        {/* Description */}
        <div className="mt-1 space-y-2">
          <div className="h-4 w-full bg-gray-200 rounded animate-pulse" />
          <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />
        </div>

        {/* Price and Downloads */}
        <div className="mt-4 flex items-center justify-between">
          <div className="h-6 w-20 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Buttons Skeleton */}
      <div className="mt-4 flex gap-2 p-4 pt-0">
        <div className="h-10 flex-1 bg-gray-200 rounded animate-pulse" />
        <div className="h-10 flex-1 bg-gray-200 rounded animate-pulse" />
      </div>
    </div>
  );
}

// Mock TemplateEmptyState component
interface MockTemplateEmptyStateProps {
  category: string;
}

function MockTemplateEmptyState({ category }: MockTemplateEmptyStateProps) {
  return (
    <div className="h-[400px] flex items-center justify-center">
      <div className="flex flex-col items-center justify-center space-y-4 p-6 text-center">
        <FolderSearch className="h-12 w-12 text-gray-400" />
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">No Templates Found</h3>
          <p className="text-sm text-gray-500">
            We couldn't find any templates in the {category} category.
          </p>
        </div>
      </div>
    </div>
  );
}

// Define course types
interface MockCourse {
  _id: string;
  title: string;
  publicId: string;
  shortDescription: string;
  thumbnail: string;
  category: string;
  subcategory: string;
  price: number;
  discountPrice?: number | null;
  enrollmentCount: number;
  free: boolean;
  averageReview: number;
  reviewCount: number;
  tag?: string;
  level?: string;
  duration?: string;
}

// Mock course data
const mockCourses: MockCourse[] = [
  {
    _id: "c1",
    title: "Introduction to Statistics",
    publicId: "intro-statistics",
    shortDescription:
      "Learn fundamental statistical concepts, probability theory, and data analysis techniques",
    thumbnail: "courses/statistics.jpg",
    category: "statistics",
    subcategory: "fundamentals",
    price: 89.99,
    discountPrice: 49.99,
    enrollmentCount: 12500,
    free: false,
    averageReview: 4.8,
    reviewCount: 2150,
    tag: "best-seller",
    level: "beginner",
    duration: "48h 30m",
  },
  {
    _id: "c2",
    title: "Business Management Essentials",
    publicId: "business-management",
    shortDescription:
      "Master essential management principles, leadership skills, and organizational behavior concepts",
    thumbnail: "courses/management.jpg",
    category: "management",
    subcategory: "leadership",
    price: 94.99,
    discountPrice: 59.99,
    enrollmentCount: 8750,
    free: false,
    averageReview: 4.7,
    reviewCount: 1850,
    tag: "trending",
    level: "intermediate",
    duration: "52h 15m",
  },
  {
    _id: "c3",
    title: "Information Systems for Business",
    publicId: "information-systems",
    shortDescription:
      "Understand how information technology supports business operations and decision-making",
    thumbnail: "courses/it.jpg",
    category: "information-technology",
    subcategory: "business-systems",
    price: 79.99,
    discountPrice: null,
    enrollmentCount: 5600,
    free: false,
    averageReview: 4.6,
    reviewCount: 980,
    tag: "new-arrival",
    level: "intermediate",
    duration: "38h 45m",
  },
  {
    _id: "c4",
    title: "Marketing Fundamentals",
    publicId: "marketing-fundamentals",
    shortDescription:
      "Learn essential marketing concepts, consumer behavior, and digital marketing strategies",
    thumbnail: "courses/marketing.jpg",
    category: "business",
    subcategory: "marketing",
    price: 69.99,
    discountPrice: 39.99,
    enrollmentCount: 15800,
    free: false,
    averageReview: 4.9,
    reviewCount: 3200,
    tag: "best-seller",
    level: "beginner",
    duration: "32h 20m",
  },
  {
    _id: "c5",
    title: "Introduction to Psychology",
    publicId: "intro-psychology",
    shortDescription:
      "Explore the human mind, behavior, and psychological theories with practical applications",
    thumbnail: "courses/psychology.jpg",
    category: "social-sciences",
    subcategory: "psychology",
    price: 74.99,
    discountPrice: 44.99,
    enrollmentCount: 9200,
    free: false,
    averageReview: 4.8,
    reviewCount: 1750,
    tag: "trending",
    level: "beginner",
    duration: "45h 30m",
  },
  {
    _id: "c6",
    title: "Creative Writing Workshop",
    publicId: "creative-writing",
    shortDescription:
      "Develop your creative writing skills across multiple genres and styles",
    thumbnail: "courses/writing.jpg",
    category: "other-subjects",
    subcategory: "arts",
    price: 59.99,
    discountPrice: 29.99,
    enrollmentCount: 7800,
    free: false,
    averageReview: 4.7,
    reviewCount: 1250,
    tag: "new-arrival",
    level: "all-levels",
    duration: "28h 45m",
  },
];

// Mock CourseCard component
interface MockCourseCardProps {
  course: MockCourse;
}

function MockCourseCard({ course }: MockCourseCardProps) {
  return (
    <div className="group relative bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 cursor-pointer">
      <div>
        {/* Thumbnail */}
        <div className="relative w-full overflow-hidden rounded-t-lg aspect-[16/9] bg-gray-200">
          <div className="absolute inset-0 flex items-center justify-center text-gray-400">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
              />
            </svg>
          </div>
        </div>

        {/* Tag Badge */}
        {course.tag === "best-seller" && (
          <div className="absolute top-2 right-2">
            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-[#eceb98] text-[#593d00]">
              Bestseller
            </span>
          </div>
        )}
        {course.tag === "new-arrival" && (
          <div className="absolute top-2 right-2">
            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
              New
            </span>
          </div>
        )}
        {course.tag === "trending" && (
          <div className="absolute top-2 right-2">
            <span className="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
              Trending
            </span>
          </div>
        )}

        {/* Content */}
        <div className="p-4">
          <div className="flex items-start justify-between gap-2">
            <h3 className="font-semibold text-gray-900 line-clamp-1">
              {course.title}
            </h3>
          </div>

          <p className="mt-1 text-sm text-gray-500 line-clamp-2">
            {course.shortDescription}
          </p>

          {/* Rating */}
          <div className="mt-2 flex items-center">
            <div className="flex text-yellow-400 text-sm">
              {[...Array(5)].map((_, i) => (
                <svg
                  key={i}
                  xmlns="http://www.w3.org/2000/svg"
                  className={`h-4 w-4 ${i < Math.floor(course.averageReview) ? "fill-current" : "stroke-current fill-none"}`}
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                  />
                </svg>
              ))}
            </div>
            <span className="ml-1 text-sm text-gray-500">
              {course.averageReview.toFixed(1)} ({course.reviewCount})
            </span>
            {course.duration && (
              <span className="ml-2 text-sm text-gray-500">
                • {course.duration}
              </span>
            )}
          </div>

          {/* Price Section */}
          <div className="mt-4 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {course.discountPrice ? (
                <>
                  <span className="text-lg font-bold text-blue-600">
                    ${course.discountPrice}
                  </span>
                  <span className="text-sm text-gray-400 line-through">
                    ${course.price}
                  </span>
                </>
              ) : (
                <span className="text-lg font-bold text-blue-600">
                  ${course.price}
                </span>
              )}
            </div>
            {course.level && (
              <span className="text-xs px-2 py-1 bg-gray-100 rounded-full text-gray-600">
                {course.level.charAt(0).toUpperCase() + course.level.slice(1)}
              </span>
            )}
          </div>

          {/* View Course Button */}
          <div className="mt-4">
            <Button className="w-full" variant="default">
              View Course
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Mock CourseCardSkeleton component
function MockCourseCardSkeleton() {
  return (
    <Card className="h-[400px]">
      <CardHeader className="space-y-2">
        {/* Thumbnail skeleton */}
        <Skeleton className="w-full h-48" />
        {/* Title skeleton */}
        <Skeleton className="h-6 w-4/5" />
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Description skeleton */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-4/5" />
        </div>
        {/* Rating skeleton */}
        <div className="flex items-center space-x-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-16" />
        </div>
        {/* Price skeleton */}
        <div className="flex items-center space-x-2">
          <Skeleton className="h-5 w-16" />
          <Skeleton className="h-4 w-12" />
        </div>
        {/* Button skeleton */}
        <Skeleton className="h-10 w-full" />
      </CardContent>
    </Card>
  );
}

// Mock CourseEmptyState component
interface MockCourseEmptyStateProps {
  category: string;
}

function MockCourseEmptyState({ category }: MockCourseEmptyStateProps) {
  return (
    <div className="h-[400px] flex items-center justify-center">
      <div className="flex flex-col items-center justify-center space-y-4 p-6 text-center">
        <FolderSearch className="h-12 w-12 text-gray-400" />
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">No Courses Found</h3>
          <p className="text-sm text-gray-500">
            We couldn't find any courses in the {category} category.
          </p>
        </div>
      </div>
    </div>
  );
}

export function OnlineCoursesMockup() {
  const [loading, setLoading] = useState(true);
  const [courses, setCourses] = useState<MockCourse[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [activeSubcategory, setActiveSubcategory] = useState<string | null>(
    null
  );

  // Subcategories for courses
  const subcategories = [
    { id: "statistics", name: "Statistics" },
    { id: "management", name: "Management" },
    { id: "information-technology", name: "Information Technology" },
    { id: "business", name: "Business" },
    { id: "social-sciences", name: "Social Sciences & Humanities" },
    { id: "other-subjects", name: "Other Subjects" },
  ];

  // Simulate loading courses
  useEffect(() => {
    // Simulate API call
    const loadCourses = async () => {
      setLoading(true);
      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setCourses(mockCourses);
      setTotalPages(2); // Mock total pages
      setLoading(false);
    };

    loadCourses();
  }, []);

  // Handle subcategory selection
  const handleSubcategoryClick = (subcategoryId: string) => {
    setActiveSubcategory(subcategoryId);
    setLoading(true);

    // Simulate API call with delay
    setTimeout(() => {
      // Filter courses based on subcategory
      const filtered = mockCourses.filter(
        (course) => course.subcategory === subcategoryId
      );
      setCourses(filtered.length > 0 ? filtered : mockCourses);
      setLoading(false);
    }, 800);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    setLoading(true);

    // Simulate API call with delay
    setTimeout(() => {
      setLoading(false);
      // We're just reusing the same courses for demo purposes
    }, 800);
  };

  return (
    <div className="bg-white p-6 h-full flex flex-col overflow-y-hidden">
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">Online Courses</h3>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" className="text-xs">
            Filter
          </Button>
          <Button variant="default" size="sm" className="text-xs">
            New
          </Button>
        </div>
      </div>

      {/* Subcategories */}
      <ScrollArea className="w-full whitespace-nowrap mb-6">
        <div className="flex space-x-4">
          {subcategories.map((subcategory) => (
            <Button
              key={subcategory.id}
              variant={
                activeSubcategory === subcategory.id ? "default" : "outline"
              }
              className="rounded-full"
              onClick={() => handleSubcategoryClick(subcategory.id)}
            >
              {subcategory.name}
            </Button>
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      {/* Courses Grid */}
      <div className="flex-grow overflow-y-hidden">
        {loading ? (
          <div className="grid grid-cols-2 gap-6 max-h-[500px]">
            {[1, 2, 3, 4].map((index) => (
              <MockCourseCardSkeleton key={index} />
            ))}
          </div>
        ) : courses.length > 0 ? (
          <>
            <div className="grid grid-cols-2 gap-6 max-h-[500px]">
              {courses.map((course) => (
                <MockCourseCard key={course._id} course={course} />
              ))}
            </div>

            {/* Pagination */}
            {/* {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-6">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="flex items-center gap-1">
                  <span className="text-sm">
                    Page {currentPage} of {totalPages}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )} */}
          </>
        ) : (
          <MockCourseEmptyState category="Online Courses" />
        )}
      </div>
    </div>
  );
}

export function ResearchHelpMockup() {
  return (
    <div className="bg-white p-6 h-full flex flex-col overflow-y-hidden">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800">
          Research Assistant
        </h3>
        <p className="text-sm text-gray-500">
          Get help with your research projects
        </p>
      </div>
      <div className="border rounded-lg p-6 bg-gray-50 flex-grow flex flex-col overflow-y-hidden">
        <div className="flex items-center mb-6">
          <div className="w-full">
            <input
              type="text"
              placeholder="Search research topics..."
              className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <button className="ml-2 p-2 bg-blue-500 text-white rounded-md">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </button>
        </div>
        <div className="space-y-4 flex-grow overflow-y-hidden max-h-[400px]">
          {[
            "Literature Review",
            "Data Analysis",
            "Methodology Support",
            "Research Design",
            "Statistical Analysis",
            "Qualitative Research",
            "Quantitative Methods",
            "Academic Writing",
          ].map((item, index) => (
            <div key={index} className="p-4 bg-white border rounded-md">
              <h4 className="text-sm font-medium text-gray-800">{item}</h4>
              <p className="text-xs text-gray-500 mt-1">
                {index % 2 === 0
                  ? "Comprehensive analysis and expert guidance"
                  : "Professional support and detailed feedback"}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export function AssignmentsHelpMockup() {
  return (
    <div className="bg-white p-6 h-full flex flex-col overflow-y-hidden">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800">
          Assignment Helper
        </h3>
        <p className="text-sm text-gray-500">
          Get expert help with your assignments
        </p>
      </div>
      <div className="border rounded-lg overflow-hidden flex-grow flex flex-col overflow-y-hidden">
        <div className="bg-gray-50 p-4 border-b">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-800">
              New Assignment
            </h4>
            <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
              Draft
            </span>
          </div>
        </div>
        <div className="p-6 flex-grow flex flex-col overflow-y-hidden max-h-[400px]">
          <div className="space-y-4 flex-grow">
            <div className="space-y-2">
              <Label htmlFor="assignment-title">Assignment Title</Label>
              <Input
                id="assignment-title"
                placeholder="Enter assignment title"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="mathematics">Mathematics</SelectItem>
                  <SelectItem value="science">Science</SelectItem>
                  <SelectItem value="literature">Literature</SelectItem>
                  <SelectItem value="history">History</SelectItem>
                  <SelectItem value="computer-science">
                    Computer Science
                  </SelectItem>
                  <SelectItem value="business">Business</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="deadline">Deadline</Label>
              <Input id="deadline" type="date" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your assignment requirements..."
                className="min-h-[100px] resize-none"
              />
            </div>

            <div className="space-y-2">
              <Label>Academic Level</Label>
              <div className="grid grid-cols-2 gap-2">
                {["High School", "Undergraduate", "Masters", "PhD"].map(
                  (level) => (
                    <div key={level} className="flex items-center space-x-2">
                      <Checkbox
                        id={`level-${level.toLowerCase().replace(" ", "-")}`}
                      />
                      <Label
                        htmlFor={`level-${level.toLowerCase().replace(" ", "-")}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {level}
                      </Label>
                    </div>
                  )
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Additional Services</Label>
              <div className="grid grid-cols-2 gap-2">
                {[
                  "Plagiarism Report",
                  "Grammar Check",
                  "Expert Review",
                  "Citation Help",
                ].map((service) => (
                  <div key={service} className="flex items-center space-x-2">
                    <Checkbox
                      id={`service-${service.toLowerCase().replace(/\s+/g, "-")}`}
                    />
                    <Label
                      htmlFor={`service-${service.toLowerCase().replace(/\s+/g, "-")}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {service}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <Button className="mt-6 w-full">Submit Request</Button>
        </div>
      </div>
    </div>
  );
}

export function AssignmentTemplatesMockup() {
  const [loading, setLoading] = useState(true);
  const [templates, setTemplates] = useState<MockTemplate[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [activeSubcategory, setActiveSubcategory] = useState<string | null>(
    null
  );

  // Subcategories for assignment templates
  const subcategories = [
    { id: "essay", name: "Essay" },
    { id: "research", name: "Research Paper" },
    { id: "case-study", name: "Case Study" },
    { id: "lab-report", name: "Lab Report" },
    { id: "literature-review", name: "Literature Review" },
  ];

  // Simulate loading templates
  useEffect(() => {
    // Simulate API call
    const loadTemplates = async () => {
      setLoading(true);
      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setTemplates(mockAssignmentTemplates);
      setTotalPages(2); // Mock total pages
      setLoading(false);
    };

    loadTemplates();
  }, []);

  // Handle subcategory selection
  const handleSubcategoryClick = (subcategoryId: string) => {
    setActiveSubcategory(subcategoryId);
    setLoading(true);

    // Simulate API call with delay
    setTimeout(() => {
      // Filter templates based on subcategory
      const filtered = mockAssignmentTemplates.filter(
        (template) => template.subcategory === subcategoryId
      );
      setTemplates(filtered.length > 0 ? filtered : mockAssignmentTemplates);
      setLoading(false);
    }, 800);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    setLoading(true);

    // Simulate API call with delay
    setTimeout(() => {
      setLoading(false);
      // We're just reusing the same templates for demo purposes
    }, 800);
  };

  return (
    <div className="bg-white p-6 h-full flex flex-col overflow-y-hidden">
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">
          Assignment Templates
        </h3>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" className="text-xs">
            Filter
          </Button>
          <Button variant="default" size="sm" className="text-xs">
            New
          </Button>
        </div>
      </div>

      {/* Subcategories */}
      <ScrollArea className="w-full whitespace-nowrap mb-6">
        <div className="flex space-x-4">
          {subcategories.map((subcategory) => (
            <Button
              key={subcategory.id}
              variant={
                activeSubcategory === subcategory.id ? "default" : "outline"
              }
              className="rounded-full"
              onClick={() => handleSubcategoryClick(subcategory.id)}
            >
              {subcategory.name}
            </Button>
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      {/* Templates Grid */}
      <div className="flex-grow overflow-y-hidden">
        {loading ? (
          <div className="grid grid-cols-2 gap-6 max-h-[500px]">
            {[1, 2, 3, 4].map((index) => (
              <MockTemplateCardSkeleton key={index} />
            ))}
          </div>
        ) : templates.length > 0 ? (
          <>
            <div className="grid grid-cols-2 gap-6 max-h-[500px]">
              {templates.map((template) => (
                <MockTemplateCard key={template._id} template={template} />
              ))}
            </div>

            {/* Pagination */}
            {/* {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-6">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="flex items-center gap-1">
                  <span className="text-sm">
                    Page {currentPage} of {totalPages}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )} */}
          </>
        ) : (
          <MockTemplateEmptyState category="Assignment Templates" />
        )}
      </div>
    </div>
  );
}

export function PresentationTemplatesMockup() {
  const [loading, setLoading] = useState(true);
  const [templates, setTemplates] = useState<MockTemplate[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [activeSubcategory, setActiveSubcategory] = useState<string | null>(
    null
  );

  // Subcategories for presentation templates
  const subcategories = [
    { id: "business", name: "Business" },
    { id: "academic", name: "Academic" },
    { id: "project", name: "Project" },
    { id: "marketing", name: "Marketing" },
    { id: "educational", name: "Educational" },
  ];

  // Simulate loading templates
  useEffect(() => {
    // Simulate API call
    const loadTemplates = async () => {
      setLoading(true);
      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setTemplates(mockPresentationTemplates);
      setTotalPages(2); // Mock total pages
      setLoading(false);
    };

    loadTemplates();
  }, []);

  // Handle subcategory selection
  const handleSubcategoryClick = (subcategoryId: string) => {
    setActiveSubcategory(subcategoryId);
    setLoading(true);

    // Simulate API call with delay
    setTimeout(() => {
      // Filter templates based on subcategory
      const filtered = mockPresentationTemplates.filter(
        (template) => template.subcategory === subcategoryId
      );
      setTemplates(filtered.length > 0 ? filtered : mockPresentationTemplates);
      setLoading(false);
    }, 800);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    setLoading(true);

    // Simulate API call with delay
    setTimeout(() => {
      setLoading(false);
      // We're just reusing the same templates for demo purposes
    }, 800);
  };

  // Custom presentation icon for template cards
  const renderPresentationIcon = () => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-12 w-12"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1}
        d="M8 13v-1m4 1v-3m4 3V8M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"
      />
    </svg>
  );

  return (
    <div className="bg-white p-6 h-full flex flex-col overflow-y-hidden">
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">
          Presentation Templates
        </h3>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm" className="text-xs">
            Filter
          </Button>
          <Button variant="default" size="sm" className="text-xs">
            New
          </Button>
        </div>
      </div>

      {/* Subcategories */}
      <ScrollArea className="w-full whitespace-nowrap mb-6">
        <div className="flex space-x-4">
          {subcategories.map((subcategory) => (
            <Button
              key={subcategory.id}
              variant={
                activeSubcategory === subcategory.id ? "default" : "outline"
              }
              className="rounded-full"
              onClick={() => handleSubcategoryClick(subcategory.id)}
            >
              {subcategory.name}
            </Button>
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      {/* Templates Grid */}
      <div className="flex-grow overflow-y-hidden">
        {loading ? (
          <div className="grid grid-cols-2 gap-6 max-h-[500px]">
            {[1, 2, 3, 4].map((index) => (
              <MockTemplateCardSkeleton key={index} />
            ))}
          </div>
        ) : templates.length > 0 ? (
          <>
            <div className="grid grid-cols-2 gap-6 max-h-[500px]">
              {templates.map((template) => (
                <div
                  key={template._id}
                  className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
                >
                  <div className="relative w-full overflow-hidden rounded-t-lg aspect-[16/9] bg-gray-200">
                    <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                      {renderPresentationIcon()}
                    </div>
                  </div>

                  {/* Tag Badge */}
                  {template.tag !== "none" && (
                    <div className="absolute top-2 right-2">
                      <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-500 text-white">
                        {template.tag.replace("-", " ")}
                      </span>
                    </div>
                  )}

                  {/* Content */}
                  <div className="p-4">
                    <div className="flex items-start justify-between gap-2">
                      <h3 className="font-semibold text-gray-900 line-clamp-1">
                        {template.title}
                      </h3>
                      <span className="text-xs text-gray-500">
                        {template.mediaType}
                      </span>
                    </div>

                    <p className="mt-1 text-sm text-gray-500 line-clamp-2">
                      {template.shortDescription}
                    </p>

                    {/* Price and Downloads */}
                    <div className="mt-4 flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {template.discountPrice ? (
                          <>
                            <span className="text-lg font-bold text-blue-600">
                              ${template.discountPrice}
                            </span>
                            <span className="text-sm text-gray-400 line-through">
                              ${template.price}
                            </span>
                          </>
                        ) : (
                          <span className="text-lg font-bold text-blue-600">
                            ${template.price}
                          </span>
                        )}
                      </div>

                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3v-13"
                          />
                        </svg>
                        <span>{template.downloadCount}</span>
                      </div>
                    </div>
                  </div>

                  {/* Buttons */}
                  <div className="mt-4 flex gap-2 p-4 pt-0">
                    <Button className="flex-1" variant="default">
                      Add to Cart
                    </Button>
                    <Button className="flex-1" variant="outline">
                      Preview
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {/* {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-6">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="flex items-center gap-1">
                  <span className="text-sm">
                    Page {currentPage} of {totalPages}
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )} */}
          </>
        ) : (
          <MockTemplateEmptyState category="Presentation Templates" />
        )}
      </div>
    </div>
  );
}
