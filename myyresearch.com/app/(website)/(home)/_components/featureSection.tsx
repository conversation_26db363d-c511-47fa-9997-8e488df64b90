import {
  FaCheckCircle,
  FaMobileAlt,
  FaGraduationCap,
  FaMoneyBillAlt,
} from "react-icons/fa";

const features = [
  {
    name: "Quality",
    description:
      "High standards in online courses, research assistance, and assignments.",
    icon: FaCheckCircle,
  },
  {
    name: "Convenience",
    description:
      "User-friendly platform with intuitive navigation and flexible learning options.",
    icon: FaMobileAlt,
  },
  {
    name: "Expertise",
    description:
      "Team of seasoned professionals and subject matter experts offering guidance and support.",
    icon: FaGraduationCap,
  },
  {
    name: "Affordability",
    description:
      "Competitive prices, flexible payment plans, and discounts to make education accessible.",
    icon: FaMoneyBillAlt,
  },
];

export default function FeatureSection() {
  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl lg:text-center">
          <h2 className="text-base font-semibold leading-7 text-indigo-600">
            Why Choose Us
          </h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Expert Learning, Affordable Success{" "}
          </p>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Join My Research for a quality, convenient, expert-supported, and
            affordable educational journey, ideal for students, educators, and
            lifelong learners.
          </p>
        </div>
        <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16">
            {features.map((feature) => (
              <div key={feature.name} className="relative pl-16">
                <dt className="text-base font-semibold leading-7 text-gray-900">
                  <div className="absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-indigo-600">
                    <feature.icon
                      className="h-6 w-6 text-white"
                      aria-hidden="true"
                    />
                  </div>
                  {feature.name}
                </dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  {feature.description}
                </dd>
              </div>
            ))}
          </dl>
        </div>
      </div>
    </div>
  );
}
