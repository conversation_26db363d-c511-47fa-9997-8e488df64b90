import { cn } from "@/lib/utils";
import {
  FaBookOpen,
  FaChartBar,
  FaFileAlt,
  FaGraduationCap,
} from "react-icons/fa";
import {
  FiTerminal,
  FiTrendingUp,
  FiDollarSign,
  FiCloud,
  FiGitBranch,
  FiHelpCircle,
  FiZap,
  FiHeart,
} from "react-icons/fi";

export function FeaturesSection() {
  const features = [
    {
      title: "Online Courses",
      description:
        "Welcome to our online courses platform where you can explore a diverse array of subjects designed to boost your expertise and proficiency in various fields.",
      href: "/assignment-help",
      icon: <FaGraduationCap />,
    },
    {
      title: "Research Help",
      description:
        "Explore the world of research with our complete guidance and resources designed to meet your academic goals.",
      href: "/assignment-help",
      icon: <FaBookOpen />,
    },
    {
      title: "Assignment Help",
      description:
        "My Research provides expert assistance to help you create high-quality assignments and improve your academic performance.",
      href: "/assignment-help",
      icon: <FaBookOpen />,
    },
    {
      title: "Assignment Templates",
      description:
        "Access a variety of professionally designed templates to create polished assignments",
      href: "/assignment-help",
      icon: <FaFileAlt />,
    },
    {
      title: "Presentation Templates",
      description:
        "Make your presentations stand out with our collection of eye-catching templates",
      href: "/assignment-help",
      icon: <FaChartBar />,
    },
  ];
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3  relative z-10 py-10 max-w-7xl mx-auto">
      {features.map((feature, index) => (
        <Feature key={feature.title} {...feature} index={index} />
      ))}
    </div>
  );
}

const Feature = ({
  title,
  description,
  icon,
  index,
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  index: number;
}) => {
  return (
    <div
      className={cn(
        "flex flex-col lg:border-r  py-10 relative group/feature dark:border-neutral-800",
        (index === 0 || index === 4) && "lg:border-l dark:border-neutral-800",
        index < 4 && "lg:border-b dark:border-neutral-800"
      )}
    >
      {index < 4 && (
        <div className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-t from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
      )}
      {index >= 4 && (
        <div className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-b from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
      )}
      <div className="mb-4 relative z-10 px-10 text-neutral-600 dark:text-neutral-400">
        {icon}
      </div>
      <div className="text-lg font-bold mb-2 relative z-10 px-10">
        <div className="absolute left-0 inset-y-0 h-6 group-hover/feature:h-8 w-1 rounded-tr-full rounded-br-full bg-neutral-300 dark:bg-neutral-700 group-hover/feature:bg-blue-500 transition-all duration-200 origin-center" />
        <span className="group-hover/feature:translate-x-2 transition duration-200 inline-block text-neutral-800 dark:text-neutral-100">
          {title}
        </span>
      </div>
      <p className="text-sm text-neutral-600 dark:text-neutral-300 max-w-xs relative z-10 px-10">
        {description}
      </p>
    </div>
  );
};
