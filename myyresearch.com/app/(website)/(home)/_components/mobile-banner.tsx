"use client";
import * as React from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import Image from "next/image";
import Link from "next/link";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import Autoplay from "embla-carousel-autoplay";
import { useEffect, useState } from "react";
// Move banner data to a separate file or use a data fetching method
import { mobileBanners } from "@/data";

const MobileBanner: React.FC = () => {
  const [api, setApi] = React.useState<CarouselApi>();
  const [current, setCurrent] = React.useState(0);
  const [count, setCount] = React.useState(0);

  useEffect(() => {
    if (!api) return;

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    const onSelect = () => setCurrent(api.selectedScrollSnap() + 1);
    api.on("select", onSelect);

    return () => {
      api.off("select", onSelect);
    };
  }, [api]);

  return (
    <Carousel
      plugins={[
        // @ts-ignore
        Autoplay({ delay: 4000 }),
      ]}
      setApi={setApi}
      className="w-full"
    >
      <CarouselContent>
        {mobileBanners.map((banner, index) => (
          <CarouselItem key={index}>
            <Link href={`/${banner.link}`}>
              <AspectRatio ratio={900 / 1000} className="bg-muted">
                <Image
                  src={banner.image}
                  alt="Banner image"
                  width={900}
                  height={1000}
                  className="object-cover"
                />
              </AspectRatio>
            </Link>
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  );
};

export default MobileBanner;
