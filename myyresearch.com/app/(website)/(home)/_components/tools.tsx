// components/BrandsGrid.tsx
import React from "react";
import Image from "next/image";
import Link from "next/link";

// Define the type for a brand item
type BrandItem = {
  name: string;
  logoPath: string; // Path to the logo image
  href: string; // Optional href
};

// Define the type for the BrandsGrid props
type BrandsGridProps = {
  brands: BrandItem[];
};

// The BrandsGrid component
const Tools = () => {
  //      MINITAB.png             eviews.png              excel.png               mitlab.png              nvivo.jpg     MINITAB LOGO 2.png           smart_pls.png           spss_logo.png

  const brands: BrandItem[] = [
    {
      name: "Minitab",
      logoPath: "/images/tools/minitab.png",
      href: "/brands/anker",
    },
    {
      name: "Minitab",
      logoPath: "/images/tools/eviews.png",
      href: "/brands/anker",
    },
  ];

  return (
    <div className="container  mx-auto px-4 py-8">
      <h2 className="text-2xl font-semibold text-center mb-8">Our Brands</h2>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4  lg:grid-cols-6 gap-4">
        {brands.map((brand) => (
          <div
            key={brand.name}
            className="flex justify-center h-[150px] rounded-[20px] cursor-pointer p-5 items-center bg-white shadow-md hover:shadow-lg transition-shadow duration-300"
          >
            <Link href={brand.href}>
              <Image
                width={200}
                height={200}
                src={brand.logoPath}
                alt={brand.name}
                className="object-contain hover:scale-105 transition-transform duration-300"
              />
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Tools;
