"use client";

import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { <PERSON>roll<PERSON><PERSON>, ScrollBar } from "@/components/ui/scroll-area";
import { templateCategories } from "@/data";
import { TemplateCard } from "@/components/template-card";
import { MinimalisticTemplate } from "@/types/template";
import { TemplateCardSkeleton } from "@/components/template-card-skeleton";
import { TemplateEmptyState } from "@/components/template-empty-state";
import { ApiStatus } from "@/types/common";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useTemplatesByCategory } from "@/lib/hooks/useTemplates";

const ITEMS_PER_PAGE = 8;

const deslugify = (slug: string) => {
  return slug.replace(/-/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());
};

export default function TemplateSelection() {
  const [activeTab, setActiveTab] = React.useState(templateCategories[0].id);
  const [activeSubcategory, setActiveSubcategory] = React.useState<
    string | null
  >(null);
  const [activeSubcategories, setActiveSubcategories] = React.useState(
    templateCategories[0].subcategories
  );
  const [currentPage, setCurrentPage] = React.useState(1);

  // Use TanStack Query for data fetching
  const {
    data: templateResponse,
    isLoading,
    isError,
  } = useTemplatesByCategory({
    category: activeTab || templateCategories[0].id,
    subcategory: activeSubcategory || undefined,
    page: currentPage,
    limit: ITEMS_PER_PAGE,
  });

  // Extract data from the query response
  const templates =
    templateResponse?.status === ApiStatus.SUCCESS
      ? templateResponse.data.data
      : [];
  const totalPages =
    templateResponse?.status === ApiStatus.SUCCESS
      ? templateResponse.data.pages
      : 1;
  const totalItems =
    templateResponse?.status === ApiStatus.SUCCESS
      ? templateResponse.data.total
      : 0;

  // Update subcategories when category changes
  React.useEffect(() => {
    const category = templateCategories.find((cat) => cat.id === activeTab);
    if (category) {
      setActiveSubcategories(category.subcategories);
      setActiveSubcategory(null); // Reset subcategory selection
      setCurrentPage(1); // Reset to first page
    }
  }, [activeTab]);

  // Handle subcategory click
  const handleSubcategoryClick = (subcategoryId: string) => {
    setActiveSubcategory(subcategoryId);
    setCurrentPage(1); // Reset to first page
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const renderSkeletons = () => (
    <>
      {[1, 2, 3, 4].map((index) => (
        <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/4">
          <div className="p-1">
            <TemplateCardSkeleton />
          </div>
        </CarouselItem>
      ))}
    </>
  );

  return (
    <div className="pt-16">
      {/* Desktop view */}
      <div className="hidden md:block">
        {/* Categories Navigation */}
        <div className="container mx-auto lg:px-8 px-4 border-b border-gray-200">
          <div className="text-left">
            <h2 className="text-lg font-semibold leading-8 tracking-tight text-indigo-600">
              Templates
            </h2>
            <p className="mt-2 font-heading text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Discover Your Perfect Template
            </p>
          </div>
          <nav className="flex space-x-8 mt-5">
            {templateCategories.map((category) => (
              <button
                key={category.id}
                className={`pb-4 px-1 ${
                  activeTab === category.id
                    ? "border-b-2 border-black text-black font-semibold"
                    : "text-gray-600 hover:text-gray-900"
                }`}
                onClick={() => setActiveTab(category.id)}
              >
                {category.name}
              </button>
            ))}
          </nav>
        </div>

        <div className="bg-[#F7F9FA] pb-10">
          <div className="container mx-auto lg:px-8 px-4">
            {/* First Carousel for subcategories */}
            <Carousel
              opts={{
                align: "start",
              }}
              className="w-full pt-10 mb-8"
            >
              <CarouselContent>
                {activeSubcategories.map((subcategory) => (
                  <CarouselItem
                    key={subcategory.id}
                    className="md:basis-1/4 lg:basis-1/6"
                  >
                    <button
                      onClick={() => handleSubcategoryClick(subcategory.id)}
                      className={`rounded-full px-5 h-16 w-full transition-colors font-semibold ${
                        activeSubcategory === subcategory.id
                          ? "bg-primary text-white"
                          : "bg-[#D1D7DC] hover:bg-gray-200"
                      }`}
                    >
                      {subcategory.name}
                    </button>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>

            {/* Second Carousel for templates with pagination */}
            <div className="relative">
              <Carousel
                opts={{
                  align: "start",
                }}
                className="w-full"
              >
                <CarouselContent>
                  {isLoading ? (
                    renderSkeletons()
                  ) : templates.length > 0 ? (
                    templates.map((template) => (
                      <CarouselItem
                        key={template._id}
                        className="md:basis-1/2 lg:basis-1/4"
                      >
                        <div className="p-1">
                          <TemplateCard template={template} />
                        </div>
                      </CarouselItem>
                    ))
                  ) : (
                    <CarouselItem className="w-full">
                      <TemplateEmptyState category={activeTab} />
                    </CarouselItem>
                  )}
                </CarouselContent>
                <CarouselPrevious />
                <CarouselNext />
              </Carousel>

              {/* Pagination below carousel */}
              {totalPages > 1 && (
                <div className="flex items-center justify-center gap-2 mt-6">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1 || isLoading}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <div className="flex items-center gap-1">
                    <span className="text-sm">
                      Page {currentPage} of {totalPages}
                    </span>
                    <span className="text-sm text-muted-foreground">
                      ({totalItems} items)
                    </span>
                  </div>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages || isLoading}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile view */}
      <div className="md:hidden lg:px-8 px-4 container mx-auto">
        <div className="text-left">
          <h2 className="text-lg font-semibold leading-8 tracking-tight text-indigo-600">
            Templates
          </h2>
          <p className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            A broad selection of Templates
          </p>
        </div>
        <Accordion type="single" collapsible className="w-full">
          {templateCategories.map((category) => (
            <AccordionItem key={category.id} value={category.id}>
              <AccordionTrigger className="text-lg font-semibold">
                {category.name}
              </AccordionTrigger>
              <AccordionContent>
                <Button variant="outline" className="mb-6">
                  Explore {category.name}
                </Button>
                <ScrollArea className="w-full whitespace-nowrap">
                  <div className="flex w-full pb-4">
                    {isLoading ? (
                      <div className="flex w-full gap-4">
                        {[1, 2, 3, 4].map((index) => (
                          <div key={index} className="w-[300px] shrink-0">
                            <TemplateCardSkeleton />
                          </div>
                        ))}
                      </div>
                    ) : templates.length > 0 ? (
                      templates.map((template) => (
                        <div key={template._id} className="w-[300px] shrink-0">
                          <TemplateCard template={template} />
                        </div>
                      ))
                    ) : (
                      <div className="w-full px-4">
                        <TemplateEmptyState category={category.name} />
                      </div>
                    )}
                  </div>
                  {templates.length > 0 && (
                    <ScrollBar orientation="horizontal" />
                  )}
                </ScrollArea>

                {/* Mobile Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-center gap-2 mt-6">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1 || isLoading}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <div className="flex items-center gap-1">
                      <span className="text-sm">
                        Page {currentPage} of {totalPages}
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages || isLoading}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  );
}
