import FaqSection from "./_components/faqSection";
import TestimonialSection from "./_components/testimonialSection";
import { PrimaryFeatures } from "./_components/primaryFeatures";
import ServicesSection from "./_components/servicesSection";
import Newsletter from "./_components/newsLetter";
import { DirectorMsgSection } from "./_components/directorMsgSection";
import DesktopBanner from "./_components/desktop-banner";
import MobileBanner from "./_components/mobile-banner";
import TemplateSelectionWrapper from "./_components/template-selection-wrapper";
import { Global } from "./_components/global";
import CourseSelectionWrapper from "./_components/course-selection-wrapper";
import { ViewMode } from "@/types/view-mode";

export default function Home() {
  return (
    <main>
      <div className="hidden md:block">
        <DesktopBanner />
      </div>
      <div className="block md:hidden">
        <MobileBanner />
      </div>

      <section className="mb-8 sm:mb-0">
        {/* <Hero /> */}

        <ServicesSection />
        <CourseSelectionWrapper viewMode={ViewMode.Carousel} />

        <TemplateSelectionWrapper viewMode={ViewMode.Carousel} />
      </section>
      <PrimaryFeatures />

      <section className="container mx-auto  lg:px-8">
        <TestimonialSection />
        <Global />

        <DirectorMsgSection />
        <FaqSection />
        <Newsletter />
      </section>
    </main>
  );
}

// Banners
// 5 Services (like fiverr)
// A broad selection of courses
// A broad selection of templates
// Do you want to do your research or Assignment ?
// Our services
// Testimonials
// Tools we use
// Director's quote
// FAQ
