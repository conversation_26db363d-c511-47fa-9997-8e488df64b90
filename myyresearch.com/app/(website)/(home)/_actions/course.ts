"use server";

import { DocumentStatus } from "@/types/common";
import CourseModel from "@/db/models/course";
import { Course, CourseStatus } from "@/types/course";
import dbConnect from "@/db/mongoose";

export async function getPublishedCourses(): Promise<Course[]> {
  try {
    await dbConnect();
    const courses = await CourseModel.find({
      status: CourseStatus.PUBLISHED,
      documentStatus: DocumentStatus.ACTIVE,
    }).sort({ createdAt: -1 });

    return JSON.parse(JSON.stringify(courses));
  } catch (error) {
    console.error("Error fetching courses:", error);
    return [];
  }
}

export async function getCoursesByCategory(
  category: string
): Promise<Course[]> {
  try {
    await dbConnect();
    const courses = await CourseModel.find({
      category,
      status: CourseStatus.PUBLISHED,
      documentStatus: DocumentStatus.ACTIVE,
    }).sort({ createdAt: -1 });

    return JSON.parse(JSON.stringify(courses));
  } catch (error) {
    console.error("Error fetching courses by category:", error);
    return [];
  }
}

export async function getCoursesBySubcategory(
  subcategory: string
): Promise<Course[]> {
  try {
    await dbConnect();
    const courses = await CourseModel.find({
      subcategory,
      status: CourseStatus.PUBLISHED,
      documentStatus: DocumentStatus.ACTIVE,
    }).sort({ createdAt: -1 });

    return JSON.parse(JSON.stringify(courses));
  } catch (error) {
    console.error("Error fetching courses by subcategory:", error);
    return [];
  }
}
