"use server";

import { DocumentStatus } from "@/types/common";
import TemplateModel from "@/db/models/template";
import { Template, TemplateStatus } from "@/types/template";
import dbConnect from "@/db/mongoose";

export async function getPublishedTemplates(): Promise<Template[]> {
  try {
    await dbConnect();
    const templates = await TemplateModel.find({
      status: TemplateStatus.PUBLISHED,
      documentStatus: DocumentStatus.ACTIVE,
    }).sort({ createdAt: -1 });

    return JSON.parse(JSON.stringify(templates));
  } catch (error) {
    console.error("Error fetching templates:", error);
    return [];
  }
}

export async function getTemplatesByCategory(
  category: string
): Promise<Template[]> {
  try {
    await dbConnect();
    const templates = await TemplateModel.find({
      category,
      status: TemplateStatus.PUBLISHED,
      documentStatus: DocumentStatus.ACTIVE,
    }).sort({ createdAt: -1 });

    return JSON.parse(JSON.stringify(templates));
  } catch (error) {
    console.error("Error fetching templates by category:", error);
    return [];
  }
}

export async function getTemplatesBySubcategory(
  subcategory: string
): Promise<Template[]> {
  try {
    await dbConnect();
    const templates = await TemplateModel.find({
      subcategory,
      status: TemplateStatus.PUBLISHED,
      documentStatus: DocumentStatus.ACTIVE,
    }).sort({ createdAt: -1 });

    return JSON.parse(JSON.stringify(templates));
  } catch (error) {
    console.error("Error fetching templates by subcategory:", error);
    return [];
  }
}
