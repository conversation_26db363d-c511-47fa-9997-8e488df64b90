"use client";

import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "@/contexts/auth-contexts";
import { useRouter } from "next/navigation";

export default function AuthLayout({
  children,
  ...props
}: {
  children: React.ReactNode;
}) {
  const auth = useContext(AuthContext);
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (auth?.user) {
      router.push("/");
    } else {
      setIsLoading(false);
    }
  }, [auth?.user, router]);

  if (isLoading) {
    return (
      <div className="flex min-h-svh items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col gap-6 max-w-[700px] mx-auto")} {...props}>
      {children}
    </div>
  );
}
