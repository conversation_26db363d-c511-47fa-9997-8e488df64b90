"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/auth-contexts";
import { useToast } from "@/hooks/use-toast";
import { createSession } from "@/lib/authenticaton";

export default function AuthCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const accessToken = searchParams.get("accessToken");
        const refreshToken = searchParams.get("refreshToken");
        const userDataStr = searchParams.get("userData");

        if (!accessToken || !refreshToken || !userDataStr) {
          throw new Error("Missing authentication data");
        }

        // Parse the user data
        const userData = JSON.parse(atob(userDataStr));

        // Create session with the tokens
        await createSession({
          accessToken,
          refreshToken,
        });

        // Update the auth context with user data
        await login(userData, { accessToken, refreshToken });

        toast({
          description: "Successfully logged in with social account.",
        });

        // Redirect to home page
        router.push("/");
      } catch (error) {
        console.error("Social auth callback error:", error);
        toast({
          variant: "destructive",
          description: "Failed to complete social authentication.",
        });
        router.push("/auth/login");
      }
    };

    handleCallback();
  }, [searchParams, login, router, toast]);

  return (
    <div className="flex min-h-[400px] items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
    </div>
  );
}
