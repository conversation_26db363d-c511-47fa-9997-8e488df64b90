"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import {
  Loader2,
  ChevronLeft,
  ChevronRight,
  User,
  GraduationCap,
  FileText,
} from "lucide-react";
import { register as registerUser } from "@/lib/authenticaton";
import { ApiStatus } from "@/types/common";
import { useToast } from "@/hooks/use-toast";
import Logo from "@/components/Logo";
import { useAuth } from "@/contexts/auth-contexts";
import { PasswordInput } from "@/components/ui/password-input";
import Link from "next/link";
import { SocialAuthButtons } from "../../../_components/social-auth-buttons";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  UserType,
  TeachingExperience,
  ExperienceLevel,
  AudienceReach,
} from "@/types/auth";
import { useState } from "react";

const registerSchema = z
  .object({
    username: z.string().min(3, "Username must be at least 3 characters"),
    email: z.string().email("Please enter a valid email address"),
    password: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z.string(),
    userType: z.enum([UserType.Student, UserType.Instructor]),
    // Instructor-specific fields
    teachingExperience: z
      .array(
        z.enum([
          TeachingExperience.InPersonInformal,
          TeachingExperience.InPersonProfessional,
          TeachingExperience.Online,
          TeachingExperience.Other,
        ])
      )
      .optional(),
    teachingExperienceOther: z.string().optional(),
    experienceLevel: z
      .enum([
        ExperienceLevel.Beginner,
        ExperienceLevel.SomeKnowledge,
        ExperienceLevel.Experienced,
      ])
      .optional(),
    audienceReach: z
      .enum([
        AudienceReach.NotAtMoment,
        AudienceReach.SmallFollowing,
        AudienceReach.SizeableFollowing,
      ])
      .optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  })
  .refine(
    (data) => {
      if (data.userType === UserType.Instructor) {
        return data.teachingExperience && data.teachingExperience.length > 0;
      }
      return true;
    },
    {
      message: "Please select at least one teaching experience",
      path: ["teachingExperience"],
    }
  )
  .refine(
    (data) => {
      if (data.userType === UserType.Instructor) {
        return data.experienceLevel !== undefined;
      }
      return true;
    },
    {
      message: "Please select your experience level",
      path: ["experienceLevel"],
    }
  )
  .refine(
    (data) => {
      if (data.userType === UserType.Instructor) {
        return data.audienceReach !== undefined;
      }
      return true;
    },
    {
      message: "Please select your audience reach",
      path: ["audienceReach"],
    }
  )
  .refine(
    (data) => {
      if (
        data.userType === UserType.Instructor &&
        data.teachingExperience?.includes(TeachingExperience.Other)
      ) {
        return (
          data.teachingExperienceOther &&
          data.teachingExperienceOther.trim().length > 0
        );
      }
      return true;
    },
    {
      message: "Please describe your other teaching experience",
      path: ["teachingExperienceOther"],
    }
  );

// Stepper component for registration flow
interface StepperProps {
  currentStep: number;
  totalSteps: number;
  userType?: UserType;
}

function RegistrationStepper({
  currentStep,
  totalSteps,
  userType,
}: StepperProps) {
  const steps = [
    { id: 1, name: "User Type", icon: User },
    { id: 2, name: "Questions", icon: GraduationCap, conditional: true },
    { id: 3, name: "Registration", icon: FileText },
  ];

  // Filter out conditional step for students
  const visibleSteps =
    userType === UserType.Student
      ? steps.filter((step) => !step.conditional)
      : steps;

  const progress = (currentStep / totalSteps) * 100;

  return (
    <div className="mb-8">
      {/* <div className="flex items-center justify-between mb-4">
        <div className="text-sm font-medium text-muted-foreground">
          Step {currentStep} of {totalSteps}
        </div>
        <div className="text-sm font-medium text-muted-foreground">
          {Math.round(progress)}% Complete
        </div>
      </div> */}
      {/* <Progress value={progress} className="h-2 mb-6" /> */}

      <div className="flex items-center justify-center space-x-4">
        {visibleSteps.map((step, index) => {
          const stepNumber =
            userType === UserType.Student && step.id > 2
              ? step.id - 1
              : step.id;
          const isActive = stepNumber === currentStep;
          const isCompleted = stepNumber < currentStep;
          const Icon = step.icon;

          return (
            <div key={step.id} className="flex items-center">
              <div
                className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  isActive
                    ? "border-primary bg-primary text-primary-foreground"
                    : isCompleted
                      ? "border-primary bg-primary text-primary-foreground"
                      : "border-muted-foreground text-muted-foreground"
                }`}
              >
                <Icon className="w-5 h-5" />
              </div>
              <div className="ml-2 text-sm font-medium">{step.name}</div>
              {index < visibleSteps.length - 1 && (
                <ChevronRight className="w-4 h-4 mx-4 text-muted-foreground" />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default function RegisterForm() {
  const { toast } = useToast();
  const { login: authLogin } = useAuth();
  const router = useRouter();

  // Stepper state
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedUserType, setSelectedUserType] = useState<UserType | null>(
    null
  );

  const [selectedTeachingExperience, setSelectedTeachingExperience] = useState<
    TeachingExperience[]
  >([]);

  const form = useForm<z.infer<typeof registerSchema>>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
      userType: UserType.Student,
      teachingExperience: [],
      teachingExperienceOther: "",
      experienceLevel: undefined,
      audienceReach: undefined,
    },
  });

  // Calculate total steps based on user type
  const getTotalSteps = () => {
    return selectedUserType === UserType.Instructor ? 3 : 2;
  };

  // Step navigation functions
  const nextStep = () => {
    const totalSteps = getTotalSteps();
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Validate instructor questions before proceeding
  const validateInstructorQuestions = () => {
    if (selectedUserType !== UserType.Instructor) return true;

    const teachingExperience = form.getValues("teachingExperience");
    const experienceLevel = form.getValues("experienceLevel");
    const audienceReach = form.getValues("audienceReach");
    const teachingExperienceOther = form.getValues("teachingExperienceOther");

    // Check if teaching experience is selected
    if (!teachingExperience || teachingExperience.length === 0) {
      form.setError("teachingExperience", {
        type: "manual",
        message: "Please select at least one teaching experience",
      });
      return false;
    }

    // Check if "Other" is selected but no description provided
    if (
      teachingExperience.includes(TeachingExperience.Other) &&
      (!teachingExperienceOther || teachingExperienceOther.trim().length === 0)
    ) {
      form.setError("teachingExperienceOther", {
        type: "manual",
        message: "Please describe your other teaching experience",
      });
      return false;
    }

    // Check if experience level is selected
    if (!experienceLevel) {
      form.setError("experienceLevel", {
        type: "manual",
        message: "Please select your experience level",
      });
      return false;
    }

    // Check if audience reach is selected
    if (!audienceReach) {
      form.setError("audienceReach", {
        type: "manual",
        message: "Please select your audience reach",
      });
      return false;
    }

    return true;
  };

  // Handle next step with validation
  const handleNextStep = () => {
    if (currentStep === 2 && selectedUserType === UserType.Instructor) {
      if (validateInstructorQuestions()) {
        nextStep();
      }
    } else {
      nextStep();
    }
  };

  // Handle user type selection
  const handleUserTypeSelection = (userType: UserType) => {
    setSelectedUserType(userType);
    form.setValue("userType", userType);

    // Reset instructor fields when switching to student
    if (userType === UserType.Student) {
      form.setValue("teachingExperience", []);
      form.setValue("teachingExperienceOther", "");
      form.setValue("experienceLevel", undefined);
      form.setValue("audienceReach", undefined);
      setSelectedTeachingExperience([]);
    }

    nextStep();
  };

  // Step 1: User Type Selection Component
  const UserTypeSelectionStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        {/* <h2 className="text-2xl font-semibold mb-2">
          Choose Your Account Type
        </h2> */}
        <p className="text-muted-foreground">
          Select how you plan to use My Research
        </p>
      </div>

      <div className="grid gap-4 max-w-md mx-auto">
        <Card
          className="cursor-pointer hover:border-primary transition-colors"
          onClick={() => handleUserTypeSelection(UserType.Student)}
        >
          <CardContent className="p-6 text-center">
            <User className="w-12 h-12 mx-auto mb-4 text-primary" />
            <h3 className="text-lg font-semibold mb-2">Student</h3>
            <p className="text-sm text-muted-foreground">
              I want to learn and access research content
            </p>
          </CardContent>
        </Card>

        <Card
          className="cursor-pointer hover:border-primary transition-colors"
          onClick={() => handleUserTypeSelection(UserType.Instructor)}
        >
          <CardContent className="p-6 text-center">
            <GraduationCap className="w-12 h-12 mx-auto mb-4 text-primary" />
            <h3 className="text-lg font-semibold mb-2">Instructor</h3>
            <p className="text-sm text-muted-foreground">
              I want to teach and share research content
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  // Step 2: Instructor Questions Component
  const InstructorQuestionsStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        {/* <h2 className="text-2xl font-semibold mb-2">
          Tell Us About Your Teaching
        </h2> */}
        <p className="text-muted-foreground">
          Help us understand your teaching background
        </p>
      </div>

      <div className="space-y-6 max-w-2xl mx-auto">
        {/* Teaching Experience */}
        <div className="grid gap-3">
          <Label>Teaching Experience (select all that apply):</Label>
          <div className="space-y-2">
            {[
              {
                value: TeachingExperience.InPersonInformal,
                label: "In-person, informally",
              },
              {
                value: TeachingExperience.InPersonProfessional,
                label: "In-person, professionally",
              },
              { value: TeachingExperience.Online, label: "Online" },
              { value: TeachingExperience.Other, label: "Other" },
            ].map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={option.value}
                  checked={selectedTeachingExperience.includes(option.value)}
                  onCheckedChange={(checked) => {
                    let newExperience = [...selectedTeachingExperience];
                    if (checked) {
                      newExperience.push(option.value);
                    } else {
                      newExperience = newExperience.filter(
                        (exp) => exp !== option.value
                      );
                    }
                    setSelectedTeachingExperience(newExperience);
                    form.setValue("teachingExperience", newExperience);
                  }}
                />
                <Label
                  htmlFor={option.value}
                  className="font-normal cursor-pointer"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
          {form.formState.errors.teachingExperience && (
            <p className="text-sm text-red-500">
              {form.formState.errors.teachingExperience.message}
            </p>
          )}
        </div>

        {/* Other Teaching Experience Description */}
        {selectedTeachingExperience.includes(TeachingExperience.Other) && (
          <div className="grid gap-2">
            <Label htmlFor="teachingExperienceOther">
              Please describe your other teaching experience:
            </Label>
            <Textarea
              id="teachingExperienceOther"
              placeholder="Describe your other teaching experience..."
              {...form.register("teachingExperienceOther")}
            />
            {form.formState.errors.teachingExperienceOther && (
              <p className="text-sm text-red-500">
                {form.formState.errors.teachingExperienceOther.message}
              </p>
            )}
          </div>
        )}

        {/* Experience Level */}
        <div className="grid gap-3">
          <Label>Experience Level:</Label>
          <RadioGroup
            value={form.watch("experienceLevel")}
            onValueChange={(value: string) => {
              form.setValue("experienceLevel", value as ExperienceLevel);
            }}
            className="flex flex-col space-y-2"
          >
            {[
              { value: ExperienceLevel.Beginner, label: "I'm a beginner" },
              {
                value: ExperienceLevel.SomeKnowledge,
                label: "I have some knowledge",
              },
              {
                value: ExperienceLevel.Experienced,
                label: "I'm experienced",
              },
            ].map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem
                  value={option.value}
                  id={`exp-${option.value}`}
                />
                <Label
                  htmlFor={`exp-${option.value}`}
                  className="font-normal cursor-pointer"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
          {form.formState.errors.experienceLevel && (
            <p className="text-sm text-red-500">
              {form.formState.errors.experienceLevel.message}
            </p>
          )}
        </div>

        {/* Audience Reach */}
        <div className="grid gap-3">
          <Label>Audience Reach:</Label>
          <RadioGroup
            value={form.watch("audienceReach")}
            onValueChange={(value: string) => {
              form.setValue("audienceReach", value as AudienceReach);
            }}
            className="flex flex-col space-y-2"
          >
            {[
              {
                value: AudienceReach.NotAtMoment,
                label: "Not at the moment",
              },
              {
                value: AudienceReach.SmallFollowing,
                label: "I have a small following",
              },
              {
                value: AudienceReach.SizeableFollowing,
                label: "I have a sizeable following",
              },
            ].map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem
                  value={option.value}
                  id={`aud-${option.value}`}
                />
                <Label
                  htmlFor={`aud-${option.value}`}
                  className="font-normal cursor-pointer"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
          {form.formState.errors.audienceReach && (
            <p className="text-sm text-red-500">
              {form.formState.errors.audienceReach.message}
            </p>
          )}
        </div>

        <div className="flex justify-between pt-4">
          <Button type="button" variant="outline" onClick={prevStep}>
            <ChevronLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <Button type="button" onClick={handleNextStep}>
            Continue
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );

  const onSubmit = async (values: z.infer<typeof registerSchema>) => {
    try {
      const registrationData: any = {
        username: values.username,
        email: values.email,
        password: values.password,
        userType: values.userType,
      };

      // Add instructor-specific data if user is registering as instructor
      if (values.userType === UserType.Instructor) {
        registrationData.instructorData = {
          teachingExperience: values.teachingExperience || [],
          teachingExperienceOther: values.teachingExperienceOther,
          experienceLevel: values.experienceLevel,
          audienceReach: values.audienceReach,
        };
      }

      const res = await registerUser(registrationData);

      if (res.status === ApiStatus.SUCCESS) {
        await authLogin(res.data.user, res.data.tokens);
        toast({
          description: "Registration successful! Welcome aboard.",
        });
        router.push("/");
      } else {
        toast({
          variant: "destructive",
          description: res.message || "An error occurred during registration.",
        });
      }
    } catch (error) {
      console.error("Registration error:", error);
      toast({
        variant: "destructive",
        description: "Failed to register. Please try again later.",
      });
    }
  };

  // Step 3: Registration Form Component
  const RegistrationFormStep = () => (
    <div className="space-y-3">
      <div className="text-center">
        <p className="text-muted-foreground">
          Enter your details to complete registration
        </p>
      </div>

      <div className="space-y-4 max-w-md mx-auto">
        {/* Social Auth - Only for Students */}
        {selectedUserType === UserType.Student && (
          <>
            <SocialAuthButtons />
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
            </div>
          </>
        )}

        {/* Username */}
        <div className="grid gap-2">
          <Label htmlFor="username">Username</Label>
          <Input
            id="username"
            type="text"
            placeholder="johndoe"
            {...form.register("username")}
          />
          {form.formState.errors.username && (
            <p className="text-sm text-red-500">
              {form.formState.errors.username.message}
            </p>
          )}
        </div>

        {/* Email */}
        <div className="grid gap-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            {...form.register("email")}
          />
          {form.formState.errors.email && (
            <p className="text-sm text-red-500">
              {form.formState.errors.email.message}
            </p>
          )}
        </div>

        {/* Password */}
        <div className="grid gap-2">
          <Label htmlFor="password">Password</Label>
          <PasswordInput id="password" {...form.register("password")} />
          {form.formState.errors.password && (
            <p className="text-sm text-red-500">
              {form.formState.errors.password.message}
            </p>
          )}
        </div>

        {/* Confirm Password */}
        <div className="grid gap-2">
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <PasswordInput
            id="confirmPassword"
            {...form.register("confirmPassword")}
          />
          {form.formState.errors.confirmPassword && (
            <p className="text-sm text-red-500">
              {form.formState.errors.confirmPassword.message}
            </p>
          )}
        </div>

        {/* Agress Terms and Conditions */}
        <div className="flex items-center gap-2">
          <label htmlFor="terms" className="text-sm">
            By registering, you agree to the{" "}
            <a
              href="/terms-conditions"
              className="text-primary underline-offset-2 hover:underline"
            >
              Terms and Conditions
            </a>
          </label>
        </div>

        <div className="flex justify-between pt-4">
          <Button type="button" variant="outline" onClick={prevStep}>
            <ChevronLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? (
              <Loader2 className="size-6 animate-spin" />
            ) : (
              "Register"
            )}
          </Button>
        </div>

        <div className="text-center text-sm">
          Already have an account?{" "}
          <Link
            href="/auth/sign-in"
            className="text-primary underline-offset-4 hover:underline"
          >
            Sign in here
          </Link>
        </div>
      </div>
    </div>
  );

  // Render current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <UserTypeSelectionStep />;
      case 2:
        if (selectedUserType === UserType.Instructor) {
          return <InstructorQuestionsStep />;
        } else {
          return <RegistrationFormStep />;
        }
      case 3:
        return <RegistrationFormStep />;
      default:
        return <UserTypeSelectionStep />;
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="p-6 md:p-8">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center text-center">
          <Logo />
          <p className="text-balance text-muted-foreground">
            Create your My Research account
          </p>
        </div>

        {/* Stepper - Hidden on mobile */}
        <div className="hidden md:block">
          <RegistrationStepper
            currentStep={currentStep}
            totalSteps={getTotalSteps()}
            userType={selectedUserType || undefined}
          />
        </div>

        {/* Step Content */}
        {renderStepContent()}
      </div>
    </form>
  );
}
