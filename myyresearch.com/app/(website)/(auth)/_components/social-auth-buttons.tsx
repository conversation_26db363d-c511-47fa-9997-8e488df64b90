"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Icons } from "@/components/icons";
import { googleAuth, facebookAuth, githubAuth } from "@/lib/social-auth";

export function SocialAuthButtons() {
  return (
    <div className="grid gap-2">
      <Button
        variant="outline"
        className="relative bg-white border-2 border-gray-200 text-gray-700 hover:bg-gray-50 hover:border-gray-300
        dark:bg-gray-800 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-700 dark:hover:border-gray-500
        flex items-center justify-start gap-3 p-4 h-auto min-h-[64px] shadow-sm
        hover:shadow-md transition-all duration-200 rounded-lg group"
        onClick={(e) => {
          e.preventDefault();
          googleAuth();
        }}
        disabled={false}
      >
        <div className="flex-shrink-0">
          <Icons.google className="size-5" />
        </div>
        <div className="flex flex-col items-start text-left flex-1">
          <span className="font-medium text-sm">Continue with Google</span>
          <span className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
            Use this only for students
          </span>
        </div>
        <div className="absolute top-2 right-2">
          <div className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 
                         text-xs px-2 py-1 rounded-full font-medium">
            Students Only
          </div>
        </div>
      </Button>

      {/* <Button
        variant="outline"
        className="bg-[#1877F2] text-white hover:bg-[#0C63D4] hover:text-white
        dark:bg-[#1877F2] dark:text-white dark:hover:bg-[#0C63D4] dark:hover:text-white"
        onClick={(e) => {
          e.preventDefault();
          facebookAuth();
        }}
        disabled={true}
      >
        <Icons.facebook className="mr-2 size-4" />
        Continue with Facebook
      </Button> */}

      {/* <Button
        variant="outline"
        className="bg-[#24292F] text-white hover:bg-[#1C2024] hover:text-white
        dark:bg-[#24292F] dark:text-white dark:hover:bg-[#1C2024] dark:hover:text-white"
        onClick={(e) => {
          e.preventDefault();
          githubAuth();
        }}
      >
        <Icons.github className="mr-2 size-4" />
        Continue with GitHub
      </Button> */}
    </div>
  );
}
