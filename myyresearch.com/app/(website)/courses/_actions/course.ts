"use server";

import CourseModel from "@/db/models/course";
import dbConnect from "@/db/mongoose";
import { Course } from "@/types/course";

interface GetCoursesParams {
  page?: number;
  pageSize?: number;
  categories?: string[];
  subcategories?: string[];
  levels?: string[];
}

interface GetCoursesResult {
  courses: Course[];
  totalCount: number;
}

export async function getCourses({
  page = 1,
  pageSize = 20,
  categories = [],
  subcategories = [],
  levels = [],
}: GetCoursesParams): Promise<GetCoursesResult> {
  try {
    await dbConnect();

    // Build filter conditions
    const filterConditions: any = {
      documentStatus: "active",
    };

    if (categories.length > 0) {
      filterConditions.category = { $in: categories };
    }

    if (subcategories.length > 0) {
      filterConditions.subcategory = { $in: subcategories };
    }

    if (levels.length > 0) {
      filterConditions.level = { $in: levels };
    }

    // Get total count
    const totalCount = await CourseModel.countDocuments(filterConditions);

    // Get paginated courses
    const courses = await CourseModel.find(filterConditions)
      .sort({ value: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .lean();

    return {
      courses: courses as unknown as Course[],
      totalCount,
    };
  } catch (error) {
    console.error("Error fetching courses:", error);
    throw new Error("Failed to fetch courses");
  }
}

export async function getCourseBySlug(slug: string): Promise<Course | null> {
  try {
    await dbConnect();

    const course = await CourseModel.findOne({
      slug,
      documentStatus: "active",
    }).lean();

    return course as Course | null;
  } catch (error) {
    console.error("Error fetching course:", error);
    throw new Error("Failed to fetch course");
  }
}
