"use client";

import { Suspense } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet";
import { PlusIcon } from "lucide-react";
import CourseFilters from "./_components/course-filters";
import { PaginationWithLinks } from "@/components/ui/pagination-with-links";
import CourseView from "./_components/course-view";
import CourseSkeleton from "./_components/course-skeleton";
import { ApiStatus } from "@/types/common";
import { useCoursesFromSearchParams } from "@/lib/hooks/useCourses";

// Component that uses useSearchParams and TanStack Query
function CourseContent() {
  // Use the custom hook that handles search params and data fetching
  const {
    data: response,
    isLoading,
    isError,
    page,
    limit,
  } = useCoursesFromSearchParams();

  // Extract data from the query response
  const courses =
    response?.status === ApiStatus.SUCCESS ? response.data.data : [];
  const totalCount =
    response?.status === ApiStatus.SUCCESS ? response.data.total : 0;

  return (
    <div>
      <Sheet>
        <div>
          <SheetContent side="left">
            <SheetHeader>
              <SheetTitle>Filters</SheetTitle>
            </SheetHeader>
            <CourseFilters />
          </SheetContent>

          <main className="mx-auto container px-4 py-1 sm:px-6 sm:py-4 lg:px-8">
            <div className="border-b border-gray-200 pb-10">
              <h1 className="text-4xl font-bold font-heading tracking-tight text-gray-900">
                Course Catalog
              </h1>
              <p className="mt-4 text-base text-gray-500">
                Explore our wide range of courses across various categories and
                skill levels.
              </p>
            </div>

            <div className="pt-12 lg:grid lg:grid-cols-3 lg:gap-x-8 xl:grid-cols-4">
              <aside>
                <h2 className="sr-only">Filters</h2>

                <SheetTrigger asChild>
                  <Button
                    variant="outline"
                    className="inline-flex items-center lg:hidden"
                  >
                    <span className="text-sm font-medium">Filters</span>
                    <PlusIcon className="ml-1 h-5 w-5 flex-shrink-0" />
                  </Button>
                </SheetTrigger>

                <div className="hidden lg:block">
                  <CourseFilters />
                </div>
              </aside>

              <div className="mt-6 lg:col-span-2 lg:mt-0 xl:col-span-3">
                <div className="space-y-6">
                  {isLoading ? (
                    <>
                      {Array.from({ length: 5 }).map((_, i) => (
                        <CourseSkeleton key={i} />
                      ))}
                    </>
                  ) : isError ? (
                    <div className="text-center py-10">
                      <p className="text-red-500">
                        Error loading courses. Please try again later.
                      </p>
                    </div>
                  ) : courses.length === 0 ? (
                    <div className="text-center py-10">
                      <p className="text-gray-500">
                        No courses found matching your criteria.
                      </p>
                    </div>
                  ) : (
                    courses.map((course) => (
                      <CourseView key={course.publicId} course={course} />
                    ))
                  )}
                </div>

                <div className="mt-8">
                  <PaginationWithLinks
                    page={page}
                    pageSize={limit}
                    totalCount={totalCount}
                  />
                </div>
              </div>
            </div>
          </main>
        </div>
      </Sheet>
    </div>
  );
}

// Main component that wraps the content in a Suspense boundary
export default function CourseCatalogPage() {
  return (
    <Suspense fallback={<CourseLoadingSkeleton />}>
      <CourseContent />
    </Suspense>
  );
}

// Loading skeleton component
function CourseLoadingSkeleton() {
  return (
    <div>
      <main className="mx-auto container px-4 py-1 sm:px-6 sm:py-4 lg:px-8">
        <div className="border-b border-gray-200 pb-10">
          <div className="h-8 bg-gray-200 rounded w-1/3 animate-pulse mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
        </div>

        <div className="pt-12 lg:grid lg:grid-cols-3 lg:gap-x-8 xl:grid-cols-4">
          <aside>
            <div className="hidden lg:block">
              <div className="space-y-4">
                <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div
                      key={i}
                      className="h-4 bg-gray-200 rounded animate-pulse"
                    ></div>
                  ))}
                </div>
              </div>
            </div>
          </aside>

          <div className="mt-6 lg:col-span-2 lg:mt-0 xl:col-span-3">
            <div className="space-y-6">
              {Array.from({ length: 5 }).map((_, i) => (
                <CourseSkeleton key={i} />
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
