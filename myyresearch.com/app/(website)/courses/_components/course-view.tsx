"use client";

import { Course, PublicMinimalCourse } from "@/types/course";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useAddToCartHelper, useCartData } from "@/contexts/cart-context";
import { Loader2, ShoppingCart, Check } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useState } from "react";

interface CourseViewProps {
  course: Course | PublicMinimalCourse;
}

export default function CourseView({ course }: CourseViewProps) {
  const router = useRouter();
  const { toast } = useToast();

  // Local loading state for this specific component
  const [isLocallyAddingToCart, setIsLocallyAddingToCart] = useState(false);

  // Cart context hooks
  const { cart } = useCartData();
  const { addCourseToCartAsync, addToCartError } = useAddToCartHelper();

  // Get course ID safely
  const courseId = course._id || "";

  // Check if course is in cart by comparing the course ID with cart item's itemId
  const isInCart =
    cart?.items.some((item) => {
      return item.itemId === courseId;
    }) || false;

  // Helper function to get course URL
  const getCourseUrl = (course: Course | PublicMinimalCourse) => {
    if ("udemyUrl" in course) {
      return course.udemyUrl;
    }
    return `/courses/${course.publicId}`;
  };

  const handleAddToCart = async () => {
    if (!courseId) {
      toast({
        title: "Error",
        description: "Course ID is missing.",
        variant: "destructive",
      });
      return;
    }

    setIsLocallyAddingToCart(true);
    try {
      await addCourseToCartAsync(courseId, course.publicId, 1);
      toast({
        title: "Added to cart",
        description: `${course.title} has been added to your cart.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLocallyAddingToCart(false);
    }
  };

  const handleBuyNow = async () => {
    if (!courseId) {
      toast({
        title: "Error",
        description: "Course ID is missing.",
        variant: "destructive",
      });
      return;
    }

    try {
      // If course is NOT in cart, redirect directly to checkout without adding to cart
      if (!isInCart) {
        router.push("/checkout");
      } else {
        // If course IS in cart, redirect to checkout
        router.push("/checkout");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to navigate to checkout. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex flex-col sm:flex-row border rounded-lg overflow-hidden hover:shadow-md transition-shadow mb-5">
      <div className="w-full sm:w-1/3 sm:min-w-[300px] h-48 sm:h-auto relative">
        <Link href={getCourseUrl(course)}>
          <div className="absolute inset-0">
            <Image
              src={`${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${course.thumbnail}`}
              alt={course.title}
              fill
              className="object-cover"
            />
          </div>
        </Link>
      </div>
      <div className="flex-1 p-4">
        <Link href={getCourseUrl(course)}>
          <h3 className="text-lg font-semibold hover:text-primary-600 transition-colors">
            {course.title}
          </h3>
        </Link>
        <p className="text-sm text-gray-600 mt-1">{course.shortDescription}</p>
        <div className="mt-2 text-sm text-gray-500">
          <p>
            {course.category} • {course.subcategory}
          </p>
        </div>
        <div className="mt-2 flex items-center">
          <span className="text-yellow-400">
            {"★".repeat(
              Math.round(
                "averageReview" in course ? course.averageReview : course.rating
              )
            )}
          </span>
          <span className="ml-1 text-sm text-gray-600">
            {("averageReview" in course
              ? course.averageReview
              : course.rating
            ).toFixed(1)}{" "}
            ({"reviewCount" in course ? course.reviewCount : course.ratingCount}{" "}
            reviews)
          </span>
        </div>
        <div className="mt-4 sm:hidden">
          {course.discountPrice ? (
            <div className="flex items-center">
              <p className="text-lg font-bold">
                ${course.discountPrice.toFixed(2)}
              </p>
              <p className="ml-2 text-sm text-gray-500 line-through">
                ${course.price.toFixed(2)}
              </p>
            </div>
          ) : (
            <p className="text-lg font-bold">${course.price.toFixed(2)}</p>
          )}
        </div>
      </div>
      <div className="w-full sm:w-1/4 sm:min-w-[200px] p-4 flex flex-col justify-between items-start sm:items-end">
        <div className="hidden sm:block mb-4">
          {course.discountPrice ? (
            <>
              <p className="text-lg font-bold">
                ${course.discountPrice.toFixed(2)}
              </p>
              <p className="text-sm text-gray-500 line-through">
                ${course.price.toFixed(2)}
              </p>
            </>
          ) : (
            <p className="text-lg font-bold">${course.price.toFixed(2)}</p>
          )}
        </div>

        {/* Cart Buttons */}
        <div className="w-full flex flex-col gap-2">
          <Button
            className="w-full"
            variant="default"
            onClick={handleAddToCart}
            disabled={isInCart || isLocallyAddingToCart}
          >
            {isLocallyAddingToCart ? (
              <div className="flex items-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Adding...</span>
              </div>
            ) : isInCart ? (
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4" />
                <span>In Cart</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <ShoppingCart className="h-4 w-4" />
                <span>Add to Cart</span>
              </div>
            )}
          </Button>
          <Button
            className="w-full"
            variant="outline"
            onClick={handleBuyNow}
            disabled={isLocallyAddingToCart}
          >
            {isLocallyAddingToCart ? (
              <div className="flex items-center space-x-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Adding...</span>
              </div>
            ) : (
              "Buy now"
            )}
          </Button>

          {/* View Course Details Link */}
          <Link
            href={getCourseUrl(course)}
            className="text-sm text-primary-600 hover:text-primary-700 text-center mt-2"
          >
            View Course Details →
          </Link>
        </div>
      </div>

      {/* Error display */}
      {addToCartError && (
        <div className="px-4 pb-4">
          <p className="text-sm text-red-600">{addToCartError.message}</p>
        </div>
      )}
    </div>
  );
}
