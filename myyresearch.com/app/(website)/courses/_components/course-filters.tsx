"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { courseCategories, coursesLevels } from "@/data";

export default function CourseFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [selectedCategories, setSelectedCategories] = useState<string[]>(
    searchParams.get("categories")?.split(",").filter(Boolean) || []
  );
  const [selectedSubcategories, setSelectedSubcategories] = useState<string[]>(
    searchParams.get("subcategories")?.split(",").filter(Boolean) || []
  );
  const [selectedLevels, setSelectedLevels] = useState<string[]>(
    searchParams.get("levels")?.split(",").filter(Boolean) || []
  );
  const [openAccordions, setOpenAccordions] = useState<string[]>([
    "categories",
  ]);

  useEffect(() => {
    // Update URL query parameters
    const params = new URLSearchParams(searchParams);
    if (selectedCategories.length > 0) {
      params.set("categories", selectedCategories.join(","));
    } else {
      params.delete("categories");
    }
    if (selectedSubcategories.length > 0) {
      params.set("subcategories", selectedSubcategories.join(","));
    } else {
      params.delete("subcategories");
    }
    if (selectedLevels.length > 0) {
      params.set("levels", selectedLevels.join(","));
    } else {
      params.delete("levels");
    }
    router.push(`?${params.toString()}`);
  }, [
    selectedCategories,
    selectedSubcategories,
    selectedLevels,
    router,
    searchParams,
  ]);

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((c) => c !== categoryId)
        : [...prev, categoryId]
    );
    setSelectedSubcategories([]); // Reset subcategories when category changes
  };

  const handleSubcategoryChange = (subcategoryId: string) => {
    setSelectedSubcategories((prev) =>
      prev.includes(subcategoryId)
        ? prev.filter((sc) => sc !== subcategoryId)
        : [...prev, subcategoryId]
    );
  };

  const handleLevelChange = (level: string) => {
    setSelectedLevels((prev) =>
      prev.includes(level) ? prev.filter((l) => l !== level) : [...prev, level]
    );
  };

  const handleAccordionChange = (value: string) => {
    setOpenAccordions((prev) =>
      prev.includes(value) ? prev : [...prev, value]
    );
  };

  const FilterContent = () => (
    <form className="space-y-10 divide-y divide-gray-200">
      <Accordion
        type="multiple"
        className="w-full"
        value={openAccordions}
        onValueChange={setOpenAccordions}
        defaultValue={["categories"]}
      >
        <AccordionItem value="categories">
          <AccordionTrigger>Categories</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-3 pt-2">
              {courseCategories.map((category) => (
                <div key={category.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={category.id}
                    checked={selectedCategories.includes(category.id)}
                    onCheckedChange={(checked) => {
                      handleCategoryChange(category.id);
                      checked && handleAccordionChange("categories");
                    }}
                  />
                  <Label htmlFor={category.id}>{category.name}</Label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="subcategories">
          <AccordionTrigger>Subcategories</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-3 pt-2">
              {selectedCategories.length === 0 ? (
                <p className="text-sm text-gray-500">
                  Please select a category to view subcategories.
                </p>
              ) : (
                courseCategories
                  .filter((c) => selectedCategories.includes(c.id))
                  .flatMap((c) => c.subcategories)
                  .map((subcategory) => (
                    <div
                      key={subcategory.id}
                      className="flex items-center space-x-2"
                    >
                      <Checkbox
                        id={subcategory.id}
                        checked={selectedSubcategories.includes(subcategory.id)}
                        onCheckedChange={() =>
                          handleSubcategoryChange(subcategory.id)
                        }
                      />
                      <Label htmlFor={subcategory.id}>{subcategory.name}</Label>
                    </div>
                  ))
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="levels">
          <AccordionTrigger>Levels</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-3 pt-2">
              {coursesLevels.map((level) => (
                <div key={level} className="flex items-center space-x-2">
                  <Checkbox
                    id={level}
                    checked={selectedLevels.includes(level)}
                    onCheckedChange={() => handleLevelChange(level)}
                  />
                  <Label htmlFor={level} className="capitalize">
                    {level}
                  </Label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </form>
  );

  return <FilterContent />;
}
