"use client";

import { useCourseDetails } from "@/lib/hooks/useCourses";
import { ApiStatus } from "@/types/common";
import { useParams } from "next/navigation";
import {
  Clock,
  Users,
  BarChart2,
  Globe,
  Award,
  ChevronRight,
  Loader2,
} from "lucide-react";
import { VideoPlayer } from "@/components/video-player";
import Link from "next/link";
import { EnrollButton } from "@/components/course/enroll-button";
import { Toaster } from "@/components/ui/toaster";
import Viewer from "@/components/editor/viewer";
import { PurchasedCourseLayout } from "@/components/course/purchased-course-layout";

// Helper function to convert slug to display text
function deslugify(slug: string): string {
  return slug
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

export default function CoursePage() {
  const params = useParams();
  const courseId = params.course as string;

  const {
    data: response,
    isLoading,
    isError,
    error,
  } = useCourseDetails(courseId);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Loading course details...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (
    isError ||
    !response ||
    response.status !== ApiStatus.SUCCESS ||
    !response.data
  ) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Course Not Found
          </h1>
          <p className="text-gray-600 mb-4">
            {error?.message || "The requested course could not be found."}
          </p>
          <Link
            href="/courses"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Browse Courses
          </Link>
        </div>
      </div>
    );
  }

  const course = response.data;

  // If user has purchased the course or it's a free course, show the enhanced purchased course layout
  if (course.purchased || course.free || course.price === 0) {
    return (
      <>
        <PurchasedCourseLayout course={course} />
        <Toaster />
      </>
    );
  }

  // Otherwise, show the regular course preview/purchase page
  return (
    <div className="min-h-screen">
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-3">
          {/* Desktop Breadcrumb */}
          <div className="hidden md:flex items-center text-sm text-gray-600">
            <Link href="/" className="hover:text-blue-600">
              Home
            </Link>
            <ChevronRight className="w-4 h-4 mx-2" />
            <Link href="/courses" className="hover:text-blue-600">
              Courses
            </Link>
            <ChevronRight className="w-4 h-4 mx-2" />
            <Link
              href={`/courses?category=${course.category}`}
              className="hover:text-blue-600"
            >
              {deslugify(course.category)}
            </Link>
            <ChevronRight className="w-4 h-4 mx-2" />
            <Link
              href={`/courses?category=${course.category}&subcategory=${course.subcategory}`}
              className="hover:text-blue-600"
            >
              {deslugify(course.subcategory)}
            </Link>
            <ChevronRight className="w-4 h-4 mx-2" />
            <span className="text-gray-900 font-medium truncate max-w-[200px]">
              {course.title}
            </span>
          </div>

          {/* Mobile Breadcrumb */}
          <div className="flex md:hidden items-center text-sm text-gray-600 overflow-hidden">
            <Link href="/courses" className="hover:text-blue-600 flex-shrink-0">
              Courses
            </Link>
            <ChevronRight className="w-4 h-4 mx-2 flex-shrink-0" />
            <Link
              href={`/courses?category=${course.category}`}
              className="hover:text-blue-600 hidden sm:block flex-shrink-0"
            >
              {deslugify(course.category)}
            </Link>
            <ChevronRight className="w-4 h-4 mx-2 hidden sm:block flex-shrink-0" />
            <span className="text-gray-900 font-medium truncate">
              {course.title}
            </span>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gray-900 text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <h1 className="text-4xl font-bold mb-4">{course.title}</h1>

              {/* Purchase Status Indicator */}
              {course.purchased && (
                <div className="mb-4 p-3 bg-green-900/20 border border-green-500/30 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Award className="w-5 h-5 text-green-400" />
                    <span className="text-green-300 font-medium">
                      ✓ You have purchased this course
                    </span>
                  </div>
                  <p className="text-green-200 text-sm mt-1">
                    You now have access to all course materials and can download
                    content.
                  </p>
                </div>
              )}

              <p className="text-lg mb-4">
                {course.shortDescription ||
                  course.description.substring(0, 160)}
                ...
              </p>

              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center">
                  <div className="text-yellow-400 mr-2">
                    {"★".repeat(Math.round(course.averageReview))}
                    {"☆".repeat(5 - Math.round(course.averageReview))}
                  </div>
                  <span>({course.reviewCount} reviews)</span>
                </div>
                <span>•</span>
                <span>{course.enrollmentCount || 0} students</span>
              </div>

              <div className="flex flex-wrap gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>Last updated {course.lastUpdated || "Recently"}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Globe className="w-4 h-4" />
                  <span>English</span>
                </div>
                <div className="flex items-center gap-1">
                  <BarChart2 className="w-4 h-4" />
                  <span>{course.level || "All Levels"}</span>
                </div>
              </div>
            </div>

            {/* Video Player Section */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-xl text-gray-900 p-6 sticky top-4">
                <div className="relative aspect-video mb-4">
                  {course.preview ? (
                    <VideoPlayer
                      sources={[
                        {
                          label: "Original",
                          src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${course.preview.key}`,
                        },
                        {
                          label: "720p",
                          src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${course.preview.key}?quality=720`,
                        },
                        {
                          label: "480p",
                          src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${course.preview.key}?quality=480`,
                        },
                        {
                          label: "360p",
                          src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${course.preview.key}?quality=360`,
                        },
                      ]}
                    />
                  ) : (
                    course.thumbnail && (
                      <img
                        src={`${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${course.thumbnail}`}
                        alt={course.title}
                        className="w-full h-full object-cover rounded-lg"
                      />
                    )
                  )}
                </div>

                <div className="mb-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-3xl font-bold">
                        $
                        {course.discountPrice?.toFixed(2) ||
                          course.price.toFixed(2)}
                      </p>
                      {course.discountPrice && (
                        <p className="text-gray-500 line-through">
                          ${course.price.toFixed(2)}
                        </p>
                      )}
                    </div>
                    {course.discountEnds && (
                      <div className="text-red-600">
                        <p className="text-sm">Offer ends in</p>
                        <p className="font-medium">{course.discountEnds}</p>
                      </div>
                    )}
                  </div>

                  <EnrollButton
                    courseId={course._id}
                    publicId={course.publicId}
                    courseTitle={course.title}
                    price={course.price}
                    discountPrice={course.discountPrice}
                    isFree={course.free}
                    thumbnail={course.thumbnail}
                  />

                  <div className="space-y-4 mt-4">
                    <h3 className="font-medium">This course includes:</h3>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        <span>
                          {course.duration || "X hours"} of video content
                        </span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Award className="w-4 h-4" />
                        <span>Certificate of completion</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <Users className="w-4 h-4" />
                        <span>Access on mobile and TV</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {/* What you'll learn */}
            {/* <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
              <h2 className="text-2xl font-bold mb-4">What you'll learn</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {course.whatYouWillLearn?.map(
                  (objective: string, index: number) => (
                    <div key={index} className="flex items-start">
                      <svg
                        className="w-5 h-5 text-green-500 mr-2 flex-shrink-0"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      <span>{objective}</span>
                    </div>
                  )
                )}
              </div>
            </div> */}

            {/* Requirements */}
            {/* <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
              <h2 className="text-2xl font-bold mb-4">Requirements</h2>
              <ul className="list-disc list-inside space-y-2">
                {course.requirements?.map(
                  (requirement: string, index: number) => (
                    <li key={index}>{requirement}</li>
                  )
                ) || <li>No specific requirements for this course</li>}
              </ul>
            </div> */}

            {/* Course Content */}
            {course.sections && course.sections.length > 0 && (
              <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 className="text-2xl font-bold mb-4">Course Content</h2>
                <div className="mb-4">
                  <span className="text-sm text-gray-600">
                    {course.sections.length} sections •{" "}
                    {course.sections.reduce(
                      (acc, section) => acc + section.lessons.length,
                      0
                    )}{" "}
                    lectures • {course.duration || "X"} total length
                  </span>
                </div>
                <div className="space-y-4">
                  {course.sections.map((section, index) => (
                    <div key={section._id} className="border rounded-lg">
                      <div className="p-4 bg-gray-50 border-b">
                        <h3 className="font-medium">
                          Section {index + 1}: {section.title}
                        </h3>
                      </div>
                      <ul className="divide-y">
                        {section.lessons.map((lesson) => (
                          <li key={lesson._id} className="p-4 hover:bg-gray-50">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <span className="mr-2">📹</span>
                                <div className="flex flex-col">
                                  <span>{lesson.title}</span>
                                  {/* Show enhanced content for purchased courses or free lessons */}
                                  {(course.purchased || lesson.isFree) &&
                                    lesson.content && (
                                      <div className="mt-2 text-sm text-gray-600">
                                        <Viewer content={lesson.content} />
                                      </div>
                                    )}
                                  {/* Show video player for purchased courses or free lessons */}
                                  {(course.purchased || lesson.isFree) &&
                                    lesson.video && (
                                      <div className="mt-2">
                                        <VideoPlayer
                                          sources={[
                                            {
                                              label: "HD",
                                              src: lesson.video.url,
                                            },
                                          ]}
                                        />
                                      </div>
                                    )}
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                {lesson.isFree && (
                                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                    Free
                                  </span>
                                )}
                                {course.purchased && !lesson.isFree && (
                                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                    Purchased
                                  </span>
                                )}
                                <span className="text-sm text-gray-500">
                                  {lesson.duration || "00:00"}
                                </span>
                              </div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Description */}
            <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
              <h2 className="text-2xl font-bold mb-4">Description</h2>
              <div className="prose max-w-none">
                {/* <p className="whitespace-pre-line">{course.description}</p> */}
                <Viewer content={course.description} />
              </div>
            </div>
          </div>
        </div>
      </div>
      <Toaster />
    </div>
  );
}
