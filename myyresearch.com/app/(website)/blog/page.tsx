import { Metadata } from "next";
import Link from "next/link";

// metadata
export const metadata: Metadata = {
  title: "Blog - MyResearch",
  description:
    "Stay updated with the latest research trends, educational insights, and academic resources.",
};

const BlogPage = () => {
  return (
    <div className="bg-white py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Our Blog
          </h2>
          <p className="mt-2 text-lg leading-8 text-gray-600">
            Stay updated with the latest research trends, educational insights,
            and academic resources.
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-2xl rounded-xl border border-gray-200 bg-white p-8 shadow-sm">
          <div className="flex flex-col items-center text-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-16 h-16 text-indigo-600 mb-4"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z"
              />
            </svg>

            <h3 className="text-xl font-semibold text-gray-900">
              Coming Soon!
            </h3>
            <p className="mt-4 text-gray-600 max-w-md">
              We're working on creating valuable content for our blog. Check
              back soon for articles on research methodologies, academic writing
              tips, and educational resources.
            </p>

            <div className="mt-8">
              <Link
                href="/"
                className="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              >
                Back to Home
              </Link>
            </div>
          </div>
        </div>

        <div className="mx-auto mt-10 max-w-2xl text-center">
          <p className="text-sm text-gray-500">
            Want to be notified when we publish new content?
            <Link
              href="/contact-us"
              className="ml-1 font-semibold text-indigo-600 hover:text-indigo-500"
            >
              Contact us
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default BlogPage;
