// "use client";

// import { useEffect, useState } from "react";
// import { useSearchParams } from "next/navigation";
// import { CheckCircle, ShoppingCart } from "lucide-react";
// import Link from "next/link";
// import { useCart } from "@/state/cart";

// interface VerificationResult {
//   success: boolean;
//   items?: Array<{
//     id: string;
//     title: string;
//     price: number;
//   }>;
//   error?: string;
// }

// export default function CartSuccessPage() {
//   const searchParams = useSearchParams();
//   const sessionId = searchParams.get("session_id");
//   const [isVerifying, setIsVerifying] = useState(true);
//   const [verificationResult, setVerificationResult] =
//     useState<VerificationResult | null>(null);
//   const { clearCart } = useCart();

//   useEffect(() => {
//     const verifyPayment = async () => {
//       try {
//         const response = await fetch("/api/cart/verify-payment", {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//           },
//           body: JSON.stringify({
//             sessionId,
//           }),
//         });

//         const result = await response.json();

//         if (!response.ok) {
//           throw new Error(result.error || "Payment verification failed");
//         }

//         setVerificationResult(result);
//         // Clear the cart after successful verification
//         clearCart();
//       } catch (err) {
//         setVerificationResult({
//           success: false,
//           error:
//             err instanceof Error
//               ? err.message
//               : "Failed to verify payment. Please contact support.",
//         });
//       } finally {
//         setIsVerifying(false);
//       }
//     };

//     if (sessionId) {
//       verifyPayment();
//     } else {
//       setVerificationResult({
//         success: false,
//         error: "Invalid session",
//       });
//       setIsVerifying(false);
//     }
//   }, [sessionId, clearCart]);

//   if (isVerifying) {
//     return (
//       <div className="min-h-screen bg-gray-50 flex items-center justify-center">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
//           <p className="mt-4 text-lg">Verifying your payment...</p>
//         </div>
//       </div>
//     );
//   }

//   if (!verificationResult?.success) {
//     return (
//       <div className="min-h-screen bg-gray-50 flex items-center justify-center">
//         <div className="max-w-md w-full mx-auto text-center px-4">
//           <div className="mb-4 text-red-500">
//             <svg
//               className="h-12 w-12 mx-auto"
//               fill="none"
//               stroke="currentColor"
//               viewBox="0 0 24 24"
//             >
//               <path
//                 strokeLinecap="round"
//                 strokeLinejoin="round"
//                 strokeWidth="2"
//                 d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
//               />
//             </svg>
//           </div>
//           <h1 className="text-2xl font-bold text-gray-900 mb-4">
//             Payment Verification Failed
//           </h1>
//           <p className="text-gray-600 mb-8">{verificationResult?.error}</p>
//           <div className="space-y-4">
//             <Link
//               href="/checkout"
//               className="block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium"
//             >
//               Return to Checkout
//             </Link>
//             <Link
//               href="/templates"
//               className="block w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors font-medium"
//             >
//               Continue Shopping
//             </Link>
//           </div>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="min-h-screen bg-gray-50 flex items-center justify-center">
//       <div className="max-w-2xl w-full mx-auto text-center px-4">
//         <div className="mb-4 text-green-500">
//           <CheckCircle className="h-16 w-16 mx-auto" />
//         </div>
//         <h1 className="text-3xl font-bold text-gray-900 mb-2">
//           Thank You for Your Purchase!
//         </h1>
//         <p className="text-gray-600 mb-8">
//           Your payment was successful and your templates are now available.
//         </p>

//         {verificationResult.items && verificationResult.items.length > 0 && (
//           <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
//             <h2 className="text-xl font-semibold mb-4">Purchased Items</h2>
//             <ul className="divide-y divide-gray-200">
//               {verificationResult.items.map((item) => (
//                 <li key={item.id} className="py-4 flex justify-between">
//                   <span className="font-medium">{item.title}</span>
//                   <span className="text-gray-600">
//                     ${item.price.toFixed(2)}
//                   </span>
//                 </li>
//               ))}
//             </ul>
//           </div>
//         )}

//         <div className="space-y-4">
//           <Link
//             href="/dashboard/templates"
//             className="block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium"
//           >
//             View My Templates
//           </Link>
//           <Link
//             href="/templates"
//             className="block w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-200 transition-colors font-medium"
//           >
//             Browse More Templates
//           </Link>
//         </div>
//       </div>
//     </div>
//   );
// }

export default function CartSuccessPage() {
  return <div>Cart Success</div>;
}
