import {
  FaCloudUploadAlt,
  FaLock,
  FaServer,
  FaUserGraduate,
  FaClipboard<PERSON>heck,
  FaBookReader,
} from "react-icons/fa";
import { Main, Section, Container } from "@/components/craft";
import Image from "next/image";
import { Metadata } from "next";
import { CallToAction } from "@/components/call-to-action";
import { Cta11 } from "@/components/blocks/shadcnblocks-com-cta11";

export const metadata: Metadata = {
  title: "Expert Research Help & Consultation Services",
  description:
    "Get professional research help and consultation services from MyResearch. Our expert team provides comprehensive support for academic research, methodology, data analysis, and more.",
  keywords:
    "research help, research consultation, academic research, research methodology, data analysis, research support",
  openGraph: {
    title: "Expert Research Help & Consultation Services",
    description:
      "Get professional research help and consultation services from MyResearch. Our expert team provides comprehensive support for academic research and analysis.",
    type: "website",
  },
};

const Page = () => {
  return (
    <div className="relative isolate overflow-hidden bg-white py-8 sm:py-16">
      <div
        className="absolute -top-80 left-[max(6rem,33%)] -z-10 transform-gpu blur-3xl sm:left-1/2 md:top-20 lg:ml-20 xl:top-3 xl:ml-56"
        aria-hidden="true"
      >
        <div
          className="aspect-[801/1036] w-[50.0625rem] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30"
          style={{
            clipPath:
              "polygon(63.1% 29.6%, 100% 17.2%, 76.7% 3.1%, 48.4% 0.1%, 44.6% 4.8%, 54.5% 25.4%, 59.8% 49.1%, 55.3% 57.9%, 44.5% 57.3%, 27.8% 48%, 35.1% 81.6%, 0% 97.8%, 39.3% 100%, 35.3% 81.5%, 97.2% 52.8%, 63.1% 29.6%)",
          }}
        />
      </div>
      <div className="mx-auto container px-6 lg:px-8">
        <div className="mx-auto max-w-3xl lg:mx-0">
          <p className="text-lg font-semibold leading-8 tracking-tight text-indigo-600">
            Research Help
          </p>
          <h1 className="mt-2 font-heading text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Expert Guidance for Your Research
          </h1>
          <p className="mt-6 text-xl leading-8 text-gray-700">
            Welcome to MyResearch, your go-to platform for comprehensive
            research assistance. Whether you're working on academic papers,
            dissertations, or complex research projects, we offer expert
            guidance to help you at every stage. Our dedicated team ensures you
            have the resources and support needed to succeed in your research
            endeavors.
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-4xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:mt-10 lg:max-w-none lg:grid-cols-12">
          <div className="relative lg:order-last lg:col-span-5">
            <svg
              className="absolute -top-[40rem] left-1 -z-10 h-[64rem] w-[175.5rem] -translate-x-1/2 stroke-gray-900/10 [mask-image:radial-gradient(64rem_64rem_at_111.5rem_0%,white,transparent)]"
              aria-hidden="true"
            >
              <defs>
                <pattern
                  id="e87443c8-56e4-4c20-9111-55b82fa704e3"
                  width={200}
                  height={200}
                  patternUnits="userSpaceOnUse"
                >
                  <path d="M0.5 0V200M200 0.5L0 0.499983" />
                </pattern>
              </defs>
              <rect
                width="100%"
                height="100%"
                strokeWidth={0}
                fill="url(#e87443c8-56e4-4c20-9111-55b82fa704e3)"
              />
            </svg>
            <figure className="border-l border-indigo-600 pl-8">
              <blockquote className="text-xl font-semibold leading-8 tracking-tight text-gray-900">
                <p>
                  "Unlock your research potential with the help of experienced
                  professionals, from the early stages to final conclusions,
                  with personalized support that focuses on real-world
                  application and no reliance on AI."
                </p>
              </blockquote>
              <figcaption className="mt-8 flex gap-x-4">
                <Image
                  src="/images/profile/avatar.png"
                  alt=""
                  className="mt-1  flex-none rounded-full bg-gray-50"
                  width={50}
                  height={50}
                />
                <div className="text-sm leading-6">
                  <div className="font-semibold text-gray-900">
                    Nipuni Tharushika
                  </div>
                  <div className="text-gray-600">@nipuni</div>
                </div>
              </figcaption>
            </figure>
          </div>
          <div className="max-w-xl text-base leading-7 text-gray-700 lg:col-span-7">
            <p>
              At MyResearch, we prioritize your research journey by offering
              personalized assistance led by professionals with hands-on
              research experience. Whether you're in the initial stages of topic
              selection or finalizing your conclusions, our experts will help
              you navigate the process with ease. We focus on practical
              solutions and advanced methodologies to ensure high-quality
              research outputs.
            </p>
            <h2 className="mt-16 text-2xl font-bold tracking-tight text-gray-900">
              Why Choose MyResearch?
            </h2>
            <ul role="list" className="mt-8 max-w-xl space-y-8 text-gray-600">
              <li className="flex gap-x-3">
                <FaUserGraduate
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Expert-Led Support:
                  </strong>{" "}
                  Receive guidance from seasoned researchers and academic
                  professionals with real-world experience.
                </span>
              </li>
              <li className="flex gap-x-3">
                <FaClipboardCheck
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Tailored Assistance:
                  </strong>{" "}
                  Get personalized help on everything from research design to
                  data analysis and writing.
                </span>
              </li>
              <li className="flex gap-x-3">
                <FaBookReader
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Resourceful Research:
                  </strong>{" "}
                  Access a wealth of research materials, expert insights, and
                  practical tools to strengthen your work.
                </span>
              </li>
            </ul>
            <p className="mt-8">
              Our service provides more than just research support - we guide
              you towards academic excellence. With a focus on practical
              research strategies and real-world application, MyResearch ensures
              you develop the skills and knowledge to conduct impactful,
              high-quality research.
            </p>
            <p className="mt-6">
              Unlock the full potential of your research with MyResearch. Start
              your journey towards academic success now!
            </p>
          </div>
        </div>
      </div>
      <Cta11
        heading="Need Research Assistance?"
        description="Get expert guidance for your research project from our experienced researchers. We help you achieve excellence in your research work."
        buttons={{
          primary: {
            text: "Chat on WhatsApp",
            url: "https://wa.me/+94777372227",
          },
        }}
      />
    </div>
  );
};

export default Page;
