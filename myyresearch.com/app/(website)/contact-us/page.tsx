// External imports
import { Metadata } from "next";
// Internal imports
import siteConfig from "@/site.config";
import ExtendedForm from "@/components/extended-from";

export const metadata: Metadata = {
  title: `Contact Us`,
  description: `Get in touch with ${siteConfig.name}. We're here to help with your research needs and answer any questions about our courses and templates.`,
  keywords: "contact us, support, help, research assistance, customer service",
  openGraph: {
    title: `Contact Us`,
    description: `Get in touch with ${siteConfig.name}. We're here to help with your research needs and answer any questions about our courses and templates.`,
    type: "website",
  },
};

const ContactUsPage = () => {
  return <ExtendedForm />;
};

export default ContactUsPage;
