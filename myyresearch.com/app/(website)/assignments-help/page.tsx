import {
  FaCloudUploadAlt,
  FaLock,
  FaServer,
  FaUserGraduate,
  FaClipboardCheck,
  FaClock,
} from "react-icons/fa";
import { Main, Section, Container } from "@/components/craft";
import { Metadata } from "next";
import Image from "next/image";
import { CallToAction } from "@/components/call-to-action";
import { Cta11 } from "@/components/blocks/shadcnblocks-com-cta11";

export const metadata: Metadata = {
  title: "Professional Assignment Help Services",
  description:
    "Get expert assignment help and academic support from MyResearch. Our professional team provides personalized assistance for research papers, essays, and more to help you excel in your studies.",
  keywords:
    "assignment help, academic support, research papers, essay writing, academic assistance, professional assignment help",
  openGraph: {
    title: "Professional Assignment Help Services",
    description:
      "Get expert assignment help and academic support from MyResearch. Our professional team provides personalized assistance for research papers, essays, and more.",
    type: "website",
  },
};

const Page = () => {
  return (
    <div className="relative isolate overflow-hidden bg-white py-8 sm:py-16">
      <div
        className="absolute -top-80 left-[max(6rem,33%)] -z-10 transform-gpu blur-3xl sm:left-1/2 md:top-20 lg:ml-20 xl:top-3 xl:ml-56"
        aria-hidden="true"
      >
        <div
          className="aspect-[801/1036] w-[50.0625rem] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30"
          style={{
            clipPath:
              "polygon(63.1% 29.6%, 100% 17.2%, 76.7% 3.1%, 48.4% 0.1%, 44.6% 4.8%, 54.5% 25.4%, 59.8% 49.1%, 55.3% 57.9%, 44.5% 57.3%, 27.8% 48%, 35.1% 81.6%, 0% 97.8%, 39.3% 100%, 35.3% 81.5%, 97.2% 52.8%, 63.1% 29.6%)",
          }}
        />
      </div>
      <div className="mx-auto container px-6 lg:px-8">
        <div className="mx-auto max-w-3xl lg:mx-0">
          <p className="text-lg font-semibold leading-8 tracking-tight text-indigo-600">
            Assignments Help
          </p>
          <h1 className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Professional Assistance for Top Assignments
          </h1>
          <p className="mt-6 text-xl leading-8 text-gray-700">
            Boost Your Academic Success with MyResearch. Welcome to MyResearch,
            your trusted platform for assignment help and academic support.
            Whether you're struggling with complex research papers or looking
            for guidance on essay writing, we offer tailored services to meet
            your academic needs. Our expert team is here to help you excel in
            your studies and achieve your academic goals.
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-4xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:mt-10 lg:max-w-none lg:grid-cols-12">
          <div className="relative lg:order-last lg:col-span-5">
            <svg
              className="absolute -top-[40rem] left-1 -z-10 h-[64rem] w-[175.5rem] -translate-x-1/2 stroke-gray-900/10 [mask-image:radial-gradient(64rem_64rem_at_111.5rem_0%,white,transparent)]"
              aria-hidden="true"
            >
              <defs>
                <pattern
                  id="e87443c8-56e4-4c20-9111-55b82fa704e3"
                  width={200}
                  height={200}
                  patternUnits="userSpaceOnUse"
                >
                  <path d="M0.5 0V200M200 0.5L0 0.499983" />
                </pattern>
              </defs>
              <rect
                width="100%"
                height="100%"
                strokeWidth={0}
                fill="url(#e87443c8-56e4-4c20-9111-55b82fa704e3)"
              />
            </svg>
            <figure className="border-l border-indigo-600 pl-8">
              <blockquote className="text-xl font-semibold leading-8 tracking-tight text-gray-900">
                <p>
                  "Get expert guidance for all your academic needs with
                  personalized support for each step of your assignments,
                  ensuring timely delivery and top quality without the use of
                  AI-generated content."
                </p>
              </blockquote>
              <figcaption className="mt-8 flex gap-x-4">
                <Image
                  src="/images/profile/avatar.png"
                  alt=""
                  className="mt-1  flex-none rounded-full bg-gray-50"
                  width={50}
                  height={50}
                />
                <div className="text-sm leading-6">
                  <div className="font-semibold text-gray-900">
                    Nipuni Tharushika
                  </div>
                  <div className="text-gray-600">@nipuni</div>
                </div>
              </figcaption>
            </figure>
          </div>
          <div className="max-w-xl text-base leading-7 text-gray-700 lg:col-span-7">
            <p>
              At MyResearch, we are committed to helping you succeed
              academically. Our experts provide one-on-one support, offering
              feedback and guidance to enhance your writing and research skills.
              With tailored assistance, you can confidently submit assignments
              that meet high academic standards.
            </p>
            <ul role="list" className="mt-8 max-w-xl space-y-8 text-gray-600">
              <li className="flex gap-x-3">
                <FaUserGraduate
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Expert Guidance.
                  </strong>{" "}
                  Get assistance from experienced academic professionals and
                  subject-matter experts.
                </span>
              </li>
              <li className="flex gap-x-3">
                <FaClipboardCheck
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Tailored Support.
                  </strong>{" "}
                  Receive personalized help with any aspect of your assignments,
                  from brainstorming to final proofreading.
                </span>
              </li>
              <li className="flex gap-x-3">
                <FaClock
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Timely Delivery.
                  </strong>{" "}
                  Our team ensures that your assignments are delivered on time,
                  without compromising quality.
                </span>
              </li>
            </ul>
            <p className="mt-8">
              MyResearch offers more than just assignment help - we provide a
              comprehensive service that helps you grow academically. Our focus
              on personalized feedback and practical solutions ensures that you
              not only complete your assignments but also learn and improve
              throughout the process.
            </p>
            <h2 className="mt-16 text-2xl font-bold tracking-tight text-gray-900">
              Get Assignment Help Today
            </h2>
            <p className="mt-6">
              Excel in your studies with MyResearch. Your path to academic
              success starts here.
            </p>
          </div>
        </div>
      </div>
      <Cta11
        heading="Need Help with Your Assignment?"
        description="Get expert assistance with your assignments from our qualified professionals. We ensure quality, originality, and timely delivery."
        buttons={{
          primary: {
            text: "Chat on WhatsApp",
            url: "https://wa.me/+94777372227",
          },
        }}
      />
    </div>
  );
};

export default Page;
