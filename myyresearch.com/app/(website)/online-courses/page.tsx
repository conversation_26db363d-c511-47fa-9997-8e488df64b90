import { FaCloudUpload<PERSON>lt, FaLock, FaServer } from "react-icons/fa";
import { Main, Section, Container } from "@/components/craft";
import { Metadata } from "next";
import Image from "next/image";
import { CallToAction } from "@/components/call-to-action";
import { Cta11 } from "@/components/blocks/shadcnblocks-com-cta11";

export const metadata: Metadata = {
  title: "Online Academic Courses & Training",
  description:
    "Enhance your skills with MyResearch's online courses. Access high-quality academic and professional training in research, statistics, data analysis, and more.",
  keywords:
    "online courses, academic training, research courses, statistics courses, data analysis courses, professional development, e-learning",
  openGraph: {
    title: "Online Academic Courses & Training",
    description:
      "Enhance your skills with MyResearch's online courses. Access high-quality academic and professional training in research and data analysis.",
    type: "website",
  },
};

const Page = () => {
  return (
    <div>
      <div className="relative isolate overflow-hidden bg-white py-8 sm:py-16">
        <div
          className="absolute -top-80 left-[max(6rem,33%)] -z-10 transform-gpu blur-3xl sm:left-1/2 md:top-20 lg:ml-20 xl:top-3 xl:ml-56"
          aria-hidden="true"
        >
          <div
            className="aspect-[801/1036] w-[50.0625rem] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30"
            style={{
              clipPath:
                "polygon(63.1% 29.6%, 100% 17.2%, 76.7% 3.1%, 48.4% 0.1%, 44.6% 4.8%, 54.5% 25.4%, 59.8% 49.1%, 55.3% 57.9%, 44.5% 57.3%, 27.8% 48%, 35.1% 81.6%, 0% 97.8%, 39.3% 100%, 35.3% 81.5%, 97.2% 52.8%, 63.1% 29.6%)",
            }}
          />
        </div>
        <div className="mx-auto container px-6 lg:px-8">
          <div className="mx-auto max-w-3xl lg:mx-0">
            <p className="text-lg font-semibold leading-8 tracking-tight text-indigo-600">
              Online Courses
            </p>
            <h1 className="mt-2 font-heading text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Enhance Your Knowledge and Skills
            </h1>
            <p className="mt-6 text-xl leading-8 text-gray-700">
              Expand Your Expertise with MyResearch. Welcome to MyResearch, your
              premier platform for online learning. Here, you can explore a vast
              range of courses designed to enhance your knowledge and skills
              across multiple disciplines. Whether you're aiming to advance your
              career or dive deeper into academic research, our expertly crafted
              courses cater to all learning needs.
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-4xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:mt-10 lg:max-w-none lg:grid-cols-12">
            <div className="relative lg:order-last lg:col-span-5">
              <svg
                className="absolute -top-[40rem] left-1 -z-10 h-[64rem] w-[175.5rem] -translate-x-1/2 stroke-gray-900/10 [mask-image:radial-gradient(64rem_64rem_at_111.5rem_0%,white,transparent)]"
                aria-hidden="true"
              >
                <defs>
                  <pattern
                    id="e87443c8-56e4-4c20-9111-55b82fa704e3"
                    width={200}
                    height={200}
                    patternUnits="userSpaceOnUse"
                  >
                    <path d="M0.5 0V200M200 0.5L0 0.499983" />
                  </pattern>
                </defs>
                <rect
                  width="100%"
                  height="100%"
                  strokeWidth={0}
                  fill="url(#e87443c8-56e4-4c20-9111-55b82fa704e3)"
                />
              </svg>
              <figure className="border-l border-indigo-600 pl-8">
                <blockquote className="text-xl font-semibold leading-8 tracking-tight text-gray-900">
                  <p>
                    "Our platform helps you grow by giving you the freedom to
                    learn at your own speed, with expert guidance to support you
                    every step of the way."
                  </p>
                </blockquote>
                <figcaption className="mt-8 flex gap-x-4">
                  <Image
                    src="/images/profile/avatar.png"
                    alt=""
                    className="mt-1  flex-none rounded-full bg-gray-50"
                    width={50}
                    height={50}
                  />
                  <div className="text-sm leading-6">
                    <div className="font-semibold text-gray-900">
                      Nipuni Tharushika
                    </div>
                    <div className="text-gray-600">@nipuni</div>
                  </div>
                </figcaption>
              </figure>
            </div>
            <div className="max-w-xl text-base leading-7 text-gray-700 lg:col-span-7">
              <p>
                At MyResearch, we prioritize your learning experience by
                offering courses led by industry experts and academic
                professionals with hands-on experience. Our flexible, self-paced
                learning model allows you to study on your schedule. With
                interactive elements like quizzes, discussions, and case
                studies, you can apply what you learn in real-world scenarios.
              </p>
              <ul role="list" className="mt-8 max-w-xl space-y-8 text-gray-600">
                <li className="flex gap-x-3">
                  <FaCloudUploadAlt
                    className="mt-1 h-5 w-5 flex-none text-indigo-600"
                    aria-hidden="true"
                  />
                  <span>
                    <strong className="font-semibold text-gray-900">
                      Expert-Led Courses:
                    </strong>{" "}
                    Learn from industry professionals and academic experts with
                    real-world experience.
                  </span>
                </li>
                <li className="flex gap-x-3">
                  <FaLock
                    className="mt-1 h-5 w-5 flex-none text-indigo-600"
                    aria-hidden="true"
                  />
                  <span>
                    <strong className="font-semibold text-gray-900">
                      Flexible Learning:
                    </strong>{" "}
                    Access courses anytime, anywhere, at your own pace.
                  </span>
                </li>
                <li className="flex gap-x-3">
                  <FaServer
                    className="mt-1 h-5 w-5 flex-none text-indigo-600"
                    aria-hidden="true"
                  />
                  <span>
                    <strong className="font-semibold text-gray-900">
                      Interactive Learning:
                    </strong>{" "}
                    Engage with quizzes, discussions, and real-world case
                    studies to enhance learning and practical application.
                  </span>
                </li>
              </ul>
              <h2 className="mt-16 text-2xl font-bold tracking-tight text-gray-900">
                Why MyResearch Stands Out?
              </h2>
              <p className="mt-6">
                At MyResearch, we turn business research into your competitive
                advantage. Our expert-led services deliver clear, actionable
                insights aligned with your business goals. Whether you need
                market analysis, competitor benchmarking, or decision support,
                we equip you with the knowledge to grow. Our research doesn’t
                just present data—it empowers results.
              </p>
              <p className="mt-8">
                Discover how meaningful, data-driven insights from MyResearch
                can give your business the edge it needs to succeed.
              </p>
            </div>
          </div>
        </div>
      </div>
      <Cta11
        heading="Ready to Start Learning?"
        description="Join thousands of students already benefiting from our expert-led online courses. Start your learning journey today."
        buttons={{
          primary: {
            text: "Chat on WhatsApp",
            url: "https://wa.me/***********",
          },
        }}
      />
    </div>
  );
};

export default Page;
