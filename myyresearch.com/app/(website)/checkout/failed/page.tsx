"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { useOrder } from "@/lib/hooks/useOrders";
import { useCartData, useCartOperations } from "@/contexts/cart-context";
import {
  XCircle,
  AlertTriangle,
  RefreshCw,
  ShoppingCart,
  ArrowRight,
  Phone,
  CreditCard,
} from "lucide-react";
import { Order, PaymentStatus } from "@/types/orders";

function CheckoutFailedContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();

  const orderId = searchParams.get("orderId");
  const cartId = searchParams.get("cartId");
  const [isLoading, setIsLoading] = useState(true);

  // Cart context hooks
  const { cart, isEmpty: isCartEmpty } = useCartData();
  const { refreshCart } = useCartOperations();

  // Fetch order details if orderId is provided
  const {
    data: orderResponse,
    isLoading: orderLoading,
    isError: orderError,
    error,
  } = useOrder(orderId || "");

  useEffect(() => {
    // Set loading to false after a short delay
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    // Refresh cart data to ensure we have latest state
    if (cartId) {
      refreshCart();
    }

    return () => clearTimeout(timer);
  }, [cartId, refreshCart]);

  // Show loading state
  if (isLoading || orderLoading) {
    return <CheckoutFailedSkeleton />;
  }

  // Determine the failure scenario
  const hasOrder = orderResponse?.success && orderResponse.order;
  const order = hasOrder ? orderResponse.order : null;
  const isPaymentFailed = order?.paymentStatus === PaymentStatus.FAILED;

  return (
    <div className="bg-white">
      <div className="mx-auto max-w-2xl px-4 py-16 sm:px-6 sm:py-24 lg:px-0">
        <div className="text-center">
          {/* Error Icon */}
          <XCircle className="mx-auto h-16 w-16 text-red-500 mb-4" />

          {/* Error Message */}
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-4">
            Payment Failed
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            {hasOrder
              ? "We encountered an issue processing your payment. Your order has been created but payment was not completed."
              : "We encountered an issue processing your payment. Please try again or contact support if the problem persists."}
          </p>

          {/* Order Details - Show if we have order information */}
          {hasOrder && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8 text-left">
              <div className="flex items-center mb-4">
                <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
                <h2 className="text-xl font-semibold text-gray-900">
                  Order Details
                </h2>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Order Number:</span>
                  <span className="font-medium">{order!.orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Amount:</span>
                  <span className="font-medium">
                    ${order!.total.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Status:</span>
                  <span className="font-medium text-red-600 capitalize">
                    {order!.paymentStatus}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Order Status:</span>
                  <span className="font-medium text-orange-600 capitalize">
                    {order!.status}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Order Items - Show if we have order information */}
          {hasOrder && order!.items.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-6 mb-8 text-left">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Items in Order ({order!.items.length})
              </h3>
              <div className="space-y-4">
                {order!.items.map((item) => (
                  <div key={item._id} className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      {item.thumbnail ? (
                        <img
                          src={`${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${item.thumbnail}`}
                          alt={item.title}
                          className="h-16 w-16 rounded-md object-cover"
                        />
                      ) : (
                        <div className="h-16 w-16 rounded-md bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-400 text-xs">
                            No Image
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {item.title}
                      </h4>
                      <p className="text-sm text-gray-500 capitalize">
                        {item.itemType} • Quantity: {item.quantity}
                      </p>
                    </div>
                    <div className="text-sm font-medium text-gray-900">
                      ${item.totalPrice.toFixed(2)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {/* Retry Payment - Show if we have an order */}
              {hasOrder && (
                <Link href={`/checkout?orderId=${order!._id}`}>
                  <Button className="w-full sm:w-auto bg-red-600 hover:bg-red-700">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry Payment
                  </Button>
                </Link>
              )}

              {/* Return to Cart - Show if cart has items */}
              {!isCartEmpty && (
                <Link href="/checkout">
                  <Button variant="outline" className="w-full sm:w-auto">
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Return to Cart
                  </Button>
                </Link>
              )}

              {/* Contact Support */}
              <Link href="/contact-us">
                <Button variant="outline" className="w-full sm:w-auto">
                  <Phone className="h-4 w-4 mr-2" />
                  Contact Support
                </Button>
              </Link>
            </div>

            <div className="pt-4">
              <Link href="/templates">
                <Button
                  variant="ghost"
                  className="text-indigo-600 hover:text-indigo-500"
                >
                  Continue Shopping
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </Link>
            </div>
          </div>

          {/* Help Text */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-sm font-medium text-blue-900 mb-2">
              What happens next?
            </h3>
            <div className="text-sm text-blue-700 space-y-1">
              {hasOrder ? (
                <>
                  <p>
                    • Your order has been saved and you can retry payment
                    anytime
                  </p>
                  <p>• No charges have been made to your payment method</p>
                  <p>• Contact support if you continue experiencing issues</p>
                </>
              ) : (
                <>
                  <p>
                    • Your cart items are still saved and ready for checkout
                  </p>
                  <p>• No charges have been made to your payment method</p>
                  <p>
                    • Try using a different payment method if the issue persists
                  </p>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function CheckoutFailedSkeleton() {
  return (
    <div className="bg-white">
      <div className="mx-auto max-w-2xl px-4 py-16 sm:px-6 sm:py-24 lg:px-0">
        <div className="text-center">
          <Skeleton className="h-16 w-16 rounded-full mx-auto mb-4" />
          <Skeleton className="h-10 w-3/4 mx-auto mb-4" />
          <Skeleton className="h-6 w-2/3 mx-auto mb-8" />

          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <Skeleton className="h-6 w-1/3 mb-4" />
            <div className="space-y-3">
              {[...Array(4)].map((_, index) => (
                <div key={index} className="flex justify-between">
                  <Skeleton className="h-4 w-1/3" />
                  <Skeleton className="h-4 w-1/4" />
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Skeleton className="h-10 w-full sm:w-32" />
              <Skeleton className="h-10 w-full sm:w-32" />
              <Skeleton className="h-10 w-full sm:w-32" />
            </div>
            <Skeleton className="h-8 w-48 mx-auto" />
          </div>
        </div>
      </div>
    </div>
  );
}

export default function CheckoutFailedPage() {
  return (
    <Suspense fallback={<CheckoutFailedSkeleton />}>
      <CheckoutFailedContent />
    </Suspense>
  );
}
