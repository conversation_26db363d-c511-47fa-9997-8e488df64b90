"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { useOrder } from "@/lib/hooks/useOrders";
import { CheckCircle, XCircle, ArrowRight, Download, Eye } from "lucide-react";
import { Order } from "@/types/orders";

function CheckoutSuccessContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();

  const orderId = searchParams.get("orderId");
  const [isLoading, setIsLoading] = useState(true);

  // Fetch order details
  const {
    data: orderResponse,
    isLoading: orderLoading,
    isError: orderError,
    error,
  } = useOrder(orderId || "");

  useEffect(() => {
    if (!orderId) {
      toast({
        title: "Invalid order",
        description: "No order ID provided. Redirecting to home page.",
        variant: "destructive",
      });
      router.push("/");
      return;
    }

    // Set loading to false after a short delay to show success animation
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [orderId, router, toast]);

  // Show loading state
  if (isLoading || orderLoading) {
    return <CheckoutSuccessSkeleton />;
  }

  // Show error state
  if (orderError || !orderResponse?.success) {
    return (
      <div className="bg-white">
        <div className="mx-auto max-w-2xl px-4 py-16 sm:px-6 sm:py-24 lg:px-0">
          <div className="text-center">
            <XCircle className="mx-auto h-16 w-16 text-red-500 mb-4" />
            <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-4">
              Order Not Found
            </h1>
            <p className="text-gray-600 mb-8">
              We couldn't find your order. Please check your email for order
              confirmation or contact support if you need assistance.
            </p>
            <div className="space-x-4">
              <Link href="/">
                <Button variant="outline">Go to Home</Button>
              </Link>
              <Link href="/contact-us">
                <Button>Contact Support</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const order = orderResponse.order;

  return (
    <div className="bg-white">
      <div className="mx-auto max-w-2xl px-4 py-16 sm:px-6 sm:py-24 lg:px-0">
        <div className="text-center">
          {/* Success Icon */}
          <CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-4" />

          {/* Success Message */}
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-4">
            Payment Successful!
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Thank you for your purchase. Your order has been processed
            successfully.
          </p>

          {/* Order Details */}
          <div className="bg-gray-50 rounded-lg p-6 mb-8 text-left">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Order Details
            </h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Order Number:</span>
                <span className="font-medium">{order.orderNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Amount:</span>
                <span className="font-medium">${order.total.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Status:</span>
                <span className="font-medium text-green-600 capitalize">
                  {order.paymentStatus}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Order Date:</span>
                <span className="font-medium">
                  {new Date(order.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {/* Order Items */}
          <div className="bg-gray-50 rounded-lg p-6 mb-8 text-left">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Items Purchased ({order.items.length})
            </h3>
            <div className="space-y-4">
              {order.items.map((item) => (
                <div key={item._id} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {item.thumbnail ? (
                      <img
                        src={`${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${item.thumbnail}`}
                        alt={item.title}
                        className="h-16 w-16 rounded-md object-cover"
                      />
                    ) : (
                      <div className="h-16 w-16 rounded-md bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-400 text-xs">No Image</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {item.title}
                    </h4>
                    <p className="text-sm text-gray-500 capitalize">
                      {item.itemType} • Quantity: {item.quantity}
                    </p>
                  </div>
                  <div className="text-sm font-medium text-gray-900">
                    ${item.totalPrice.toFixed(2)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/account/orders">
                <Button variant="outline" className="w-full sm:w-auto">
                  <Eye className="h-4 w-4 mr-2" />
                  View All Orders
                </Button>
              </Link>
              <Link href="/account/resources">
                <Button className="w-full sm:w-auto">
                  <Download className="h-4 w-4 mr-2" />
                  Access Resources
                </Button>
              </Link>
            </div>

            <div className="pt-4">
              <Link href="/templates">
                <Button
                  variant="ghost"
                  className="text-indigo-600 hover:text-indigo-500"
                >
                  Continue Shopping
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </Link>
            </div>
          </div>

          {/* Additional Information */}
          {/* <div className="mt-8 text-sm text-gray-500">
            <p>
              A confirmation email has been sent to your email address with your
              order details and download links (if applicable).
            </p>
          </div> */}
        </div>
      </div>
    </div>
  );
}

function CheckoutSuccessSkeleton() {
  return (
    <div className="bg-white">
      <div className="mx-auto max-w-2xl px-4 py-16 sm:px-6 sm:py-24 lg:px-0">
        <div className="text-center">
          <Skeleton className="h-16 w-16 rounded-full mx-auto mb-4" />
          <Skeleton className="h-10 w-3/4 mx-auto mb-4" />
          <Skeleton className="h-6 w-2/3 mx-auto mb-8" />

          <div className="bg-gray-50 rounded-lg p-6 mb-8">
            <Skeleton className="h-6 w-1/3 mb-4" />
            <div className="space-y-3">
              {[...Array(4)].map((_, index) => (
                <div key={index} className="flex justify-between">
                  <Skeleton className="h-4 w-1/3" />
                  <Skeleton className="h-4 w-1/4" />
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex gap-4 justify-center">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
            </div>
            <Skeleton className="h-8 w-40 mx-auto" />
          </div>
        </div>
      </div>
    </div>
  );
}

export default function CheckoutSuccessPage() {
  return (
    <Suspense fallback={<CheckoutSuccessSkeleton />}>
      <CheckoutSuccessContent />
    </Suspense>
  );
}
