"use client";

import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { loadStripe } from "@stripe/stripe-js";
import { useAuth } from "@/contexts/auth-contexts";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { useCartData, useCartOperations } from "@/contexts/cart-context";
import { Loader2, Minus, Plus, Trash2, CreditCard } from "lucide-react";
import { useCreateOrder } from "@/lib/hooks/useOrders";
import {
  useCreatePaymentIntent,
  useConfirmPayment,
} from "@/lib/hooks/usePayments";
import {
  Elements,
  CardElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { useRouter, useSearchParams } from "next/navigation";

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY!);

// Stripe card element options
const cardElementOptions = {
  style: {
    base: {
      fontSize: "16px",
      color: "#424770",
      "::placeholder": {
        color: "#aab7c4",
      },
    },
    invalid: {
      color: "#9e2146",
    },
  },
};

// Checkout form component with Stripe Elements
function CheckoutForm() {
  const stripe = useStripe();
  const elements = useElements();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const { user } = useAuth();

  // Check if this is a retry payment scenario
  const retryOrderId = searchParams.get("orderId");

  // Cart context hooks
  const { cartId, totalAmount, isEmpty } = useCartData();
  const { clearCartAsync } = useCartOperations();

  // API hooks
  const createOrderMutation = useCreateOrder();
  const createPaymentIntentMutation = useCreatePaymentIntent();
  const confirmPaymentMutation = useConfirmPayment();

  const [isProcessing, setIsProcessing] = useState(false);

  const handleCheckout = async () => {
    if (!stripe || !elements) {
      toast({
        title: "Error",
        description: "Unable to process payment. Please try again.",
        variant: "destructive",
      });
      return;
    }

    // For retry payments, we don't need cart validation
    if (!retryOrderId && (isEmpty || !cartId)) {
      toast({
        title: "Error",
        description: "Unable to process payment. Please try again.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      let orderResult;

      if (retryOrderId) {
        // Step 1a: For retry payments, use existing order
        toast({
          title: "Preparing retry payment...",
          description: "Setting up payment for existing order.",
        });

        // We'll use the existing order ID for payment intent creation
        orderResult = {
          order: { _id: retryOrderId, orderNumber: `Retry-${retryOrderId}` },
        };
      } else {
        // Step 1b: Create new order from cart
        toast({
          title: "Creating order...",
          description: "Please wait while we prepare your order.",
        });

        orderResult = await createOrderMutation.mutateAsync({
          cartId,
          metadata: {
            source: "checkout_page",
            timestamp: new Date().toISOString(),
          },
        });

        // if (!orderResult.success || !orderResult.order) {
        //   throw new Error("Failed to create order");
        // }
      }

      // Step 2: Create payment intent
      toast({
        title: "Initializing payment...",
        description: "Setting up secure payment processing.",
      });

      const paymentIntentResult = await createPaymentIntentMutation.mutateAsync(
        {
          orderId: orderResult.order._id,
          cartId: cartId || "retry-payment", // Use current cartId or placeholder for retry payments
          metadata: {
            orderNumber: orderResult.order.orderNumber,
            isRetry: !!retryOrderId,
          },
        }
      );

      if (!paymentIntentResult.success || !paymentIntentResult.clientSecret) {
        throw new Error("Failed to initialize payment");
      }

      // Step 3: Confirm payment with Stripe
      toast({
        title: "Processing payment...",
        description: "Please wait while we process your payment.",
      });

      const cardElement = elements.getElement(CardElement);
      if (!cardElement) {
        throw new Error("Card element not found");
      }

      const { error, paymentIntent } = await stripe.confirmCardPayment(
        paymentIntentResult.clientSecret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: user?.username || "Guest User",
              email: user?.email || "",
            },
          },
        }
      );

      if (error) {
        throw new Error(error.message || "Payment failed");
      }

      if (paymentIntent?.status !== "succeeded") {
        throw new Error("Payment was not successful");
      }

      // Step 4: Confirm payment on backend
      const confirmResult = await confirmPaymentMutation.mutateAsync({
        paymentIntentId: paymentIntent.id,
        metadata: {
          orderNumber: orderResult.order.orderNumber,
        },
      });

      if (!confirmResult.success) {
        throw new Error("Failed to confirm payment");
      }

      // Success! Clear cart and redirect
      toast({
        title: "Payment successful!",
        description: "Your order has been processed successfully.",
        variant: "default",
      });

      // Clear cart state only for new orders, not retry payments
      if (!retryOrderId) {
        await clearCartAsync();
      }

      // Redirect to success page
      router.push(`/checkout/success?orderId=${orderResult.order._id}`);
    } catch (error: any) {
      console.log("Checkout error:", error);

      // If we have an order ID from the error response, redirect to failed page with order info
      const orderId = error?.response?.data?.orderId || error?.orderId;
      if (orderId) {
        router.push(`/checkout/failed?orderId=${orderId}&cartId=${cartId}`);
      } else {
        // If no order was created, redirect to failed page with cart info only
        router.push(`/checkout/failed?cartId=${cartId}`);
      }

      toast({
        title: "Payment failed",
        description: error.message || "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Payment Method Section */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Payment Information
        </h3>
        <div className="bg-white p-4 rounded border">
          <CardElement options={cardElementOptions} />
        </div>
      </div>

      {/* Checkout Button */}
      <Button
        onClick={handleCheckout}
        disabled={
          isProcessing || (!retryOrderId && isEmpty) || !stripe || !elements
        }
        className="w-full rounded-md border border-transparent bg-indigo-600 px-4 py-3 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-50"
      >
        {isProcessing ? (
          <div className="flex items-center justify-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Processing...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center space-x-2">
            <CreditCard className="h-4 w-4" />
            <span>
              {retryOrderId ? "Retry Payment" : `Pay ${totalAmount.toFixed(2)}`}
            </span>
          </div>
        )}
      </Button>
    </div>
  );
}

export default function CheckoutPage() {
  const { user, loading: authLoading } = useAuth();

  // Cart context hooks
  const {
    cart,
    totalAmount,
    totalItems,
    isEmpty,
    isLoading: cartLoading,
    isError: cartError,
  } = useCartData();
  const {
    removeFromCart,
    updateCartItem,
    isRemovingFromCart,
    isUpdatingCartItem,
    removeFromCartError,
    updateCartItemError,
  } = useCartOperations();

  useEffect(() => {
    console.log("Cart data in checkout:", cart);
    console.log("Cart loading state:", cartLoading);
    console.log("Cart error:", cartError);
  }, [cart, cartLoading, cartError]);

  const subtotal = totalAmount;

  // Show loading state for auth or cart
  if (authLoading || cartLoading) {
    return <CheckoutSkeleton />;
  }

  // Show error state for cart
  if (cartError) {
    return (
      <div className="bg-white">
        <div className="mx-auto max-w-2xl px-4 py-16 sm:px-6 sm:py-24 lg:px-0">
          <h1 className="text-center text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Shopping Cart
          </h1>
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">
              Error loading cart. Please try again.
            </p>
            <Button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
            >
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white">
      <div className="mx-auto max-w-2xl px-4 py-16 sm:px-6 sm:py-24 lg:px-0">
        <h1 className="text-center text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
          Shopping Cart
        </h1>

        <form className="mt-12">
          <section aria-labelledby="cart-heading">
            <h2 id="cart-heading" className="sr-only">
              Items in your shopping cart
            </h2>

            {isEmpty ? (
              <div className="text-center py-12 border-b border-t border-gray-200">
                <p className="text-gray-500 mb-4">Your cart is empty</p>
                <Link
                  href="/templates"
                  className="inline-block px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
                >
                  Browse Templates
                </Link>
              </div>
            ) : (
              <ul
                role="list"
                className="divide-y divide-gray-200 border-b border-t border-gray-200"
              >
                {cart?.items.map((cartItem) => (
                  <li key={cartItem._id} className="flex py-6">
                    <div className="flex-shrink-0">
                      <Image
                        alt={cartItem.title}
                        src={
                          cartItem.thumbnail
                            ? `${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${cartItem.thumbnail}`
                            : "/placeholder-image.jpg"
                        }
                        className="rounded-md object-cover object-center "
                        width={160}
                        height={90}
                      />
                    </div>

                    <div className="ml-4 flex flex-1 flex-col sm:ml-6">
                      <div>
                        <div className="flex justify-between">
                          <h4 className="text-sm">
                            <p className="font-medium text-gray-700 hover:text-gray-800">
                              {cartItem.title}
                            </p>
                          </h4>
                          <div className="ml-4 text-sm font-medium text-gray-900">
                            <p>${cartItem.discountPrice || cartItem.price}</p>
                            {cartItem.discountPrice &&
                              cartItem.discountPrice < cartItem.price && (
                                <p className="text-xs text-gray-500 line-through">
                                  ${cartItem.price}
                                </p>
                              )}
                          </div>
                        </div>
                        {/* <p className="mt-1 text-sm text-gray-500">
                        {cartItem.item.shortDescription}
                      </p> */}
                      </div>

                      <div className="mt-4 flex flex-1 items-end justify-between">
                        <div className="flex items-center space-x-4">
                          <p className="text-sm text-gray-700">
                            <span>{cartItem.itemType}</span>
                          </p>
                          {/* Quantity Controls */}
                          <div className="flex items-center space-x-2">
                            <button
                              type="button"
                              onClick={() =>
                                updateCartItem({
                                  cartItemId: cartItem._id,
                                  quantity: Math.max(1, cartItem.quantity - 1),
                                })
                              }
                              disabled={
                                isUpdatingCartItem || cartItem.quantity <= 1
                              }
                              className="p-1 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <Minus className="h-3 w-3" />
                            </button>
                            <span className="text-sm font-medium w-8 text-center">
                              {cartItem.quantity}
                            </span>
                            <button
                              type="button"
                              onClick={() =>
                                updateCartItem({
                                  cartItemId: cartItem._id,
                                  quantity: cartItem.quantity + 1,
                                })
                              }
                              disabled={isUpdatingCartItem}
                              className="p-1 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <Plus className="h-3 w-3" />
                            </button>
                          </div>
                        </div>
                        <div className="ml-4 flex items-center space-x-2">
                          {isUpdatingCartItem && (
                            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                          )}
                          <button
                            type="button"
                            onClick={() => removeFromCart(cartItem._id)}
                            disabled={isRemovingFromCart}
                            className="text-sm font-medium text-red-600 hover:text-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                          >
                            {isRemovingFromCart ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                            <span>Remove</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </section>

          {/* Error Messages */}
          {(removeFromCartError || updateCartItemError) && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">
                {removeFromCartError?.message || updateCartItemError?.message}
              </p>
            </div>
          )}

          {/* Order summary */}
          <section aria-labelledby="summary-heading" className="mt-10">
            <h2 id="summary-heading" className="sr-only">
              Order summary
            </h2>

            <div>
              <dl className="space-y-4">
                <div className="flex items-center justify-between">
                  <dt className="text-base font-medium text-gray-900">
                    Subtotal ({totalItems} items)
                  </dt>
                  <dd className="ml-4 text-base font-medium text-gray-900">
                    ${subtotal.toFixed(2)}
                  </dd>
                </div>
              </dl>
              <p className="mt-1 text-sm text-gray-500">
                Taxes will be calculated at checkout.
              </p>
            </div>

            <div className="mt-10">
              {user ? (
                <Elements stripe={stripePromise}>
                  <CheckoutForm />
                </Elements>
              ) : (
                !authLoading && (
                  <Link href="/auth/sign-in?redirect=/checkout">
                    <Button
                      variant="default"
                      className="w-full rounded-md border border-transparent bg-indigo-600 px-4 py-3 text-base font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-50"
                    >
                      Sign in to checkout
                    </Button>
                  </Link>
                )
              )}
            </div>

            <div className="mt-6 text-center text-sm">
              <p>
                or{" "}
                <Link
                  href="/templates"
                  className="font-medium text-indigo-600 hover:text-indigo-500"
                >
                  Continue Shopping
                  <span aria-hidden="true"> &rarr;</span>
                </Link>
              </p>
            </div>
          </section>
        </form>
      </div>
    </div>
  );
}

function CheckoutSkeleton() {
  return (
    <div className="bg-white">
      <div className="mx-auto max-w-2xl px-4 py-16 sm:px-6 sm:py-24 lg:px-0">
        <Skeleton className="h-10 w-3/4 mx-auto mb-12" />

        <div className="mt-12">
          <div className="border-b border-t border-gray-200">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="flex py-6">
                <Skeleton className="h-24 w-24 rounded-md sm:h-32 sm:w-32" />
                <div className="ml-4 flex flex-1 flex-col sm:ml-6">
                  <div className="flex justify-between mb-4">
                    <Skeleton className="h-4 w-1/3" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                  <Skeleton className="h-4 w-2/3 mb-4" />
                  <div className="mt-4 flex justify-between">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-10">
            <Skeleton className="h-6 w-1/4 mb-2" />
            <Skeleton className="h-6 w-1/3 mb-6" />
            <Skeleton className="h-12 w-full rounded-md" />
          </div>

          <div className="mt-6 text-center">
            <Skeleton className="h-4 w-40 mx-auto" />
          </div>
        </div>
      </div>
    </div>
  );
}
