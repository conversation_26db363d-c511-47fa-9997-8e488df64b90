import { FaCloudUpload<PERSON><PERSON>, <PERSON>aLock, FaServer } from "react-icons/fa";
import { Main, Section, Container } from "@/components/craft";
import { Metadata } from "next";
import Image from "next/image";
import { CallToAction } from "@/components/call-to-action";
import { Cta11 } from "@/components/blocks/shadcnblocks-com-cta11";

export const metadata: Metadata = {
  title: "Professional Assignment Templates",
  description:
    "Access high-quality assignment templates from MyResearch. Our professionally designed templates help you structure your academic work effectively across various subjects and formats.",
  keywords:
    "assignment templates, academic templates, essay templates, research paper templates, academic writing templates, assignment structure",
  openGraph: {
    title: "Professional Assignment Templates",
    description:
      "Access high-quality assignment templates from MyResearch. Our professionally designed templates help you structure your academic work effectively.",
    type: "website",
  },
};

const Page = () => {
  return (
    <div className="relative isolate overflow-hidden bg-white py-8 sm:py-16">
      <div
        className="absolute -top-80 left-[max(6rem,33%)] -z-10 transform-gpu blur-3xl sm:left-1/2 md:top-20 lg:ml-20 xl:top-3 xl:ml-56"
        aria-hidden="true"
      >
        <div
          className="aspect-[801/1036] w-[50.0625rem] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30"
          style={{
            clipPath:
              "polygon(63.1% 29.6%, 100% 17.2%, 76.7% 3.1%, 48.4% 0.1%, 44.6% 4.8%, 54.5% 25.4%, 59.8% 49.1%, 55.3% 57.9%, 44.5% 57.3%, 27.8% 48%, 35.1% 81.6%, 0% 97.8%, 39.3% 100%, 35.3% 81.5%, 97.2% 52.8%, 63.1% 29.6%)",
          }}
        />
      </div>
      <div className="mx-auto container px-6 lg:px-8">
        <div className="mx-auto max-w-3xl lg:mx-0">
          <p className="text-lg font-semibold leading-8 tracking-tight text-indigo-600">
            Assignments Templates
          </p>
          <h1 className="mt-2 font-heading text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Professional Templates for Polished Assignments
          </h1>
          <p className="mt-6 text-xl leading-8 text-gray-700">
            Welcome to MyResearch, your ultimate platform for expertly designed
            assignment templates. Our wide range of templates is created to help
            you structure your work efficiently and professionally. Whether
            you're writing essays, reports, or research papers, MyResearch
            offers templates that meet the highest academic standards, ensuring
            your assignments stand out.
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-4xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:mt-10 lg:max-w-none lg:grid-cols-12">
          <div className="relative lg:order-last lg:col-span-5">
            <svg
              className="absolute -top-[40rem] left-1 -z-10 h-[64rem] w-[175.5rem] -translate-x-1/2 stroke-gray-900/10 [mask-image:radial-gradient(64rem_64rem_at_111.5rem_0%,white,transparent)]"
              aria-hidden="true"
            >
              <defs>
                <pattern
                  id="e87443c8-56e4-4c20-9111-55b82fa704e3"
                  width={200}
                  height={200}
                  patternUnits="userSpaceOnUse"
                >
                  <path d="M0.5 0V200M200 0.5L0 0.499983" />
                </pattern>
              </defs>
              <rect
                width="100%"
                height="100%"
                strokeWidth={0}
                fill="url(#e87443c8-56e4-4c20-9111-55b82fa704e3)"
              />
            </svg>
            <figure className="border-l border-indigo-600 pl-8">
              <blockquote className="text-xl font-semibold leading-8 tracking-tight text-gray-900">
                <p>
                  "Save time and improve the quality of your assignments with
                  expert-designed templates that meet academic standards,
                  allowing you to focus on your content while we handle the
                  structure."
                </p>
              </blockquote>
              <figcaption className="mt-8 flex gap-x-4">
                <Image
                  src="/images/profile/avatar.png"
                  alt=""
                  className="mt-1  flex-none rounded-full bg-gray-50"
                  width={50}
                  height={50}
                />
                <div className="text-sm leading-6">
                  <div className="font-semibold text-gray-900">
                    Nipuni Tharushika
                  </div>
                  <div className="text-gray-600">@nipuni</div>
                </div>
              </figcaption>
            </figure>
          </div>
          <div className="max-w-xl text-base leading-7 text-gray-700 lg:col-span-7">
            <p>
              At MyResearch, we understand the importance of presenting
              well-structured and professional assignments. Our templates are
              designed by experts with years of academic experience to help you
              focus more on content while we take care of the format. With
              templates for various academic levels and subjects, MyResearch
              ensures that you stay organized and produce high-quality work.
            </p>
            <ul role="list" className="mt-8 max-w-xl space-y-8 text-gray-600">
              <li className="flex gap-x-3">
                <FaCloudUploadAlt
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Expert-Designed Templates:
                  </strong>{" "}
                  Our templates are crafted by academic professionals to ensure
                  adherence to academic standards.
                </span>
              </li>
              <li className="flex gap-x-3">
                <FaLock
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Zero Artificial Intelligence:
                  </strong>{" "}
                  Receive genuine, personalized support from real experts – no
                  AI-generated content.
                </span>
              </li>
              <li className="flex gap-x-3">
                <FaServer
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Time-Saving Solution:
                  </strong>{" "}
                  Simplify your writing process by using ready-made structures
                  that guide you through each section of your assignment.
                </span>
              </li>
            </ul>
            <p className="mt-8">
              Our platform offers more than just templates – we provide a
              streamlined path to academic excellence. By using our templates,
              you'll enhance the presentation and professionalism of your work,
              giving you a competitive edge in your academic pursuits.
            </p>
            <h2 className="mt-16 text-2xl font-bold tracking-tight text-gray-900">
              Start organizing your assignments with ease
            </h2>
            <p className="mt-6">
              Explore our collection of templates and elevate the quality of
              your academic work! Save time and improve the quality of your
              assignments with expert-designed templates that meet academic
              standards, allowing you to focus on your content while we handle
              the structure.
            </p>
          </div>
        </div>
      </div>
      <Cta11
        heading="Get Professional Assignment Templates"
        description="Access our collection of professionally designed assignment templates to streamline your academic work and improve your presentation."
        buttons={{
          primary: {
            text: "Chat on WhatsApp",
            url: "https://wa.me/+94777372227",
          },
        }}
      />
    </div>
  );
};

export default Page;
