import {
  FaFileAlt,
  FaRegCopy,
  FaCheck,
  FaRegFileWord,
  FaRegFilePdf,
  FaRegFileExcel,
} from "react-icons/fa";
import { Main, Section, Container } from "@/components/craft";
import { Metadata } from "next";
import Image from "next/image";
import { CallToAction } from "@/components/call-to-action";
import { Cta11 } from "@/components/blocks/shadcnblocks-com-cta11";

export const metadata: Metadata = {
  title: "Professional Business Document Templates",
  description:
    "Access professional-grade business document templates from MyResearch. Our comprehensive collection includes templates for business plans, proposals, reports, presentations, and more to streamline your business documentation process.",
  keywords:
    "business templates, document templates, business plans, proposal templates, report templates, professional documents, business documentation",
  openGraph: {
    title: "Professional Business Document Templates",
    description:
      "Access professional-grade business document templates from MyResearch. Our comprehensive collection includes templates for business plans, proposals, reports, presentations, and more.",
    type: "website",
  },
};

const Page = () => {
  return (
    <div className="relative isolate overflow-hidden bg-white py-8 sm:py-16">
      <div
        className="absolute -top-80 left-[max(6rem,33%)] -z-10 transform-gpu blur-3xl sm:left-1/2 md:top-20 lg:ml-20 xl:top-3 xl:ml-56"
        aria-hidden="true"
      >
        <div
          className="aspect-[801/1036] w-[50.0625rem] bg-gradient-to-tr from-[#c5eaff] to-[#a989fc] opacity-30"
          style={{
            clipPath:
              "polygon(63.1% 29.6%, 100% 17.2%, 76.7% 3.1%, 48.4% 0.1%, 44.6% 4.8%, 54.5% 25.4%, 59.8% 49.1%, 55.3% 57.9%, 44.5% 57.3%, 27.8% 48%, 35.1% 81.6%, 0% 97.8%, 39.3% 100%, 35.3% 81.5%, 97.2% 52.8%, 63.1% 29.6%)",
          }}
        />
      </div>
      <div className="mx-auto container px-6 lg:px-8">
        <div className="mx-auto max-w-3xl lg:mx-0">
          <p className="text-lg font-semibold leading-8 tracking-tight text-indigo-600">
            Business Services
          </p>
          <h1 className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Business Document Templates
          </h1>
          <p className="mt-6 text-xl leading-8 text-gray-700">
            Save time and maintain professionalism with our premium business
            document templates. At MyResearch, we offer a comprehensive
            collection of professionally designed, customizable templates for
            all your business documentation needs. From business plans and
            proposals to reports and presentations, our templates help you
            create polished, consistent documents with ease.
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-4xl grid-cols-1 gap-x-8 gap-y-16 lg:mx-0 lg:mt-10 lg:max-w-none lg:grid-cols-12">
          <div className="relative lg:order-last lg:col-span-5">
            <svg
              className="absolute -top-[40rem] left-1 -z-10 h-[64rem] w-[175.5rem] -translate-x-1/2 stroke-gray-900/10 [mask-image:radial-gradient(64rem_64rem_at_111.5rem_0%,white,transparent)]"
              aria-hidden="true"
            >
              <defs>
                <pattern
                  id="e87443c8-56e4-4c20-9111-55b82fa704e3"
                  width={200}
                  height={200}
                  patternUnits="userSpaceOnUse"
                >
                  <path d="M0.5 0V200M200 0.5L0 0.499983" />
                </pattern>
              </defs>
              <rect
                width="100%"
                height="100%"
                strokeWidth={0}
                fill="url(#e87443c8-56e4-4c20-9111-55b82fa704e3)"
              />
            </svg>
            <figure className="border-l border-indigo-600 pl-8">
              <blockquote className="text-xl font-semibold leading-8 tracking-tight text-gray-900">
                <p>
                  "The business templates from MyResearch have revolutionized
                  our documentation process. Their professional designs and easy
                  customization options have saved us countless hours while
                  maintaining a consistent brand image across all our business
                  documents."
                </p>
              </blockquote>
              <figcaption className="mt-8 flex gap-x-4">
                <Image
                  src="/images/profile/avatar.png"
                  alt=""
                  className="mt-1  flex-none rounded-full bg-gray-50"
                  width={50}
                  height={50}
                />
                <div className="text-sm leading-6">
                  <div className="font-semibold text-gray-900">
                    Nipuni Tharushika
                  </div>
                  <div className="text-gray-600">@nipuni</div>
                </div>
              </figcaption>
            </figure>
          </div>
          <div className="max-w-xl text-base leading-7 text-gray-700 lg:col-span-7">
            <p>
              Our business document templates are designed by professionals with
              extensive experience in business documentation. Each template is
              crafted to meet industry standards while being fully customizable
              to suit your specific business needs and brand identity.
            </p>
            <ul role="list" className="mt-8 max-w-xl space-y-8 text-gray-600">
              <li className="flex gap-x-3">
                <FaRegFileWord
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Business Plans & Proposals.
                  </strong>{" "}
                  Professional templates for business plans, project proposals,
                  client pitches, and investment proposals.
                </span>
              </li>
              <li className="flex gap-x-3">
                <FaRegFilePdf
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Reports & Documentation.
                  </strong>{" "}
                  Comprehensive templates for business reports, financial
                  statements, project documentation, and case studies.
                </span>
              </li>
              <li className="flex gap-x-3">
                <FaRegFileExcel
                  className="mt-1 h-5 w-5 flex-none text-indigo-600"
                  aria-hidden="true"
                />
                <span>
                  <strong className="font-semibold text-gray-900">
                    Forms & Spreadsheets.
                  </strong>{" "}
                  Practical templates for invoices, expense reports, budget
                  planning, project tracking, and various business forms.
                </span>
              </li>
            </ul>
            <p className="mt-8">
              All our templates are meticulously designed with attention to
              detail and are compatible with popular software like Microsoft
              Office and Google Workspace. They come with clear instructions and
              are easily adaptable to maintain your brand consistency. We
              regularly update our collection to ensure you have access to the
              latest document formats and business practices.
            </p>
            <h2 className="mt-16 text-2xl font-bold tracking-tight text-gray-900">
              Streamline Your Business Documentation
            </h2>
            <p className="mt-6">
              Access our premium business document templates to save time,
              maintain professionalism, and ensure consistency across all your
              business communications.
            </p>
          </div>
        </div>
      </div>
      <Cta11
        heading="Need Professional Business Templates?"
        description="Access our comprehensive collection of professional business document templates to streamline your documentation process."
        buttons={{
          primary: {
            text: "Browse Templates",
            url: "/business-services/document-templates/browse",
          },
        }}
      />
    </div>
  );
};

export default Page;
