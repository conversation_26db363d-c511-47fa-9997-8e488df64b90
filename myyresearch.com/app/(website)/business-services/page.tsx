import { Metada<PERSON> } from "next";
import Link from "next/link";
import { BarChart3, Database, FileText, FileEdit } from "lucide-react";

export const metadata: Metadata = {
  title: "Business Services | MyResearch",
  description:
    "Explore our comprehensive business services including research & analysis, tech & data services, document templates, and content writing & marketing solutions to support your business growth.",
  keywords:
    "business services, research, data services, business templates, content writing, marketing services, professional business support",
  openGraph: {
    title: "Business Services | MyResearch",
    description:
      "Explore our comprehensive business services including research & analysis, tech & data services, document templates, and content writing & marketing solutions.",
    type: "website",
  },
};

const businessServices = [
  {
    title: "Business Research & Analysis",
    description: "Comprehensive research and analysis for your business",
    icon: <BarChart3 className="h-10 w-10 text-indigo-600" />,
    href: "/business-services/research-analysis",
  },
  {
    title: "Tech & Data Services",
    description: "Technical and data solutions for your business",
    icon: <Database className="h-10 w-10 text-indigo-600" />,
    href: "/business-services/tech-data",
  },
  {
    title: "Business Document Templates",
    description: "Professional templates for business documents",
    icon: <FileText className="h-10 w-10 text-indigo-600" />,
    href: "/business-services/document-templates",
  },
  {
    title: "Content Writing & Marketing",
    description: "Professional content creation and marketing services",
    icon: <FileEdit className="h-10 w-10 text-indigo-600" />,
    href: "/business-services/content-marketing",
  },
];

export default function BusinessServices() {
  return (
    <div className="bg-white py-12 sm:py-24">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
            Business Services
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Comprehensive professional services to support your business growth
            and success. Our team of experts provides tailored solutions to meet
            your specific business needs.
          </p>
        </div>

        <div className="mx-auto mt-16 max-w-5xl">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
            {businessServices.map((service, index) => (
              <Link key={index} href={service.href} className="group">
                <div className="relative flex flex-col items-start p-8 rounded-2xl border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg">
                  <div className="bg-indigo-50 rounded-xl p-3 mb-4">
                    {service.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors duration-300">
                    {service.title}
                  </h3>
                  <p className="mt-3 text-base text-gray-600">
                    {service.description}
                  </p>
                  <div className="mt-6 flex items-center text-indigo-600 font-medium">
                    Learn more
                    <svg
                      className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        <div className="mt-20 py-12 bg-gray-50 rounded-3xl">
          <div className="px-6 md:px-12 lg:px-20">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900">
              Why Choose Our Business Services?
            </h2>
            <p className="mt-6 text-lg text-gray-600">
              At MyResearch, we provide professional business services designed
              to help your business thrive in today's competitive landscape. Our
              team combines expertise, innovation, and commitment to deliver
              high-quality solutions tailored to your specific business needs.
            </p>

            <div className="mt-10 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <div className="flex flex-col">
                <div className="text-indigo-600 text-lg font-semibold">
                  Expertise
                </div>
                <p className="mt-2 text-gray-600">
                  Our team consists of industry experts with years of experience
                  in their respective fields.
                </p>
              </div>
              <div className="flex flex-col">
                <div className="text-indigo-600 text-lg font-semibold">
                  Customization
                </div>
                <p className="mt-2 text-gray-600">
                  We tailor our services to meet your specific business
                  requirements and objectives.
                </p>
              </div>
              <div className="flex flex-col">
                <div className="text-indigo-600 text-lg font-semibold">
                  Quality
                </div>
                <p className="mt-2 text-gray-600">
                  We maintain high standards in all our deliverables to ensure
                  exceptional quality.
                </p>
              </div>
              <div className="flex flex-col">
                <div className="text-indigo-600 text-lg font-semibold">
                  Innovation
                </div>
                <p className="mt-2 text-gray-600">
                  We stay ahead of industry trends to provide innovative
                  solutions that drive results.
                </p>
              </div>
              <div className="flex flex-col">
                <div className="text-indigo-600 text-lg font-semibold">
                  Support
                </div>
                <p className="mt-2 text-gray-600">
                  We offer ongoing support to ensure the success of our
                  solutions for your business.
                </p>
              </div>
              <div className="flex flex-col">
                <div className="text-indigo-600 text-lg font-semibold">
                  Value
                </div>
                <p className="mt-2 text-gray-600">
                  Our services are designed to provide excellent return on
                  investment for your business.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-20 text-center">
          <h2 className="text-3xl font-bold tracking-tight text-gray-900">
            Ready to Get Started?
          </h2>
          <p className="mt-6 text-lg text-gray-600 max-w-2xl mx-auto">
            Contact us today to discuss how our business services can support
            your growth and help you achieve your business objectives.
          </p>
          <div className="mt-10">
            <Link
              href="/contact-us"
              className="rounded-md bg-indigo-600 px-6 py-3 text-base font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
