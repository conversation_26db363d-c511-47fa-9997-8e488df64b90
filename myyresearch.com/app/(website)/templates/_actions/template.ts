"use server";

import { connectToDatabase } from "@/lib/db";
import Template from "@/db/models/template";
import { Template as TemplateType } from "@/types/template";

export async function getTemplates({
  page = 1,
  pageSize = 20,
  categories = [],
  subcategories = [],
  mediaTypes = [],
}: {
  page?: number;
  pageSize?: number;
  categories?: string[];
  subcategories?: string[];
  mediaTypes?: string[];
}) {
  try {
    await connectToDatabase();

    // Construct filter conditions
    const filterConditions: any = {};

    if (categories.length > 0) {
      filterConditions.category = { $in: categories };
    }
    if (subcategories.length > 0) {
      filterConditions.subcategory = { $in: subcategories };
    }
    if (mediaTypes.length > 0) {
      filterConditions.mediaType = { $in: mediaTypes };
    }

    // Add status condition to only fetch published templates
    filterConditions.status = "published";
    filterConditions.documentStatus = "active";

    // Calculate skip value for pagination
    const skip = (page - 1) * pageSize;

    // Fetch templates with pagination
    const templates = await Template.find(filterConditions)
      .sort({ value: -1 })
      .skip(skip)
      .limit(pageSize)
      .lean();

    // Get total count for pagination
    const totalCount = await Template.countDocuments(filterConditions);

    return {
      templates,
      totalCount,
    };
  } catch (error) {
    console.error("Error fetching templates:", error);
    throw new Error("Failed to fetch templates");
  }
}

export async function getTemplateBySlug(
  slug: string
): Promise<TemplateType | null> {
  try {
    await connectToDatabase();

    const template = await Template.findOne({
      slug,
      status: "published",
      documentStatus: "active",
    }).lean();

    return template as TemplateType | null;
  } catch (error) {
    console.error("Error fetching template:", error);
    throw new Error("Failed to fetch template");
  }
}
