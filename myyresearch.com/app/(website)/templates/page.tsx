"use client";

import { Suspense } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet";
import { PlusIcon } from "lucide-react";
import TemplateFilters from "./_components/template-filters";
import { PaginationWithLinks } from "@/components/ui/pagination-with-links";
import { TemplateCard } from "@/components/template-card";
import { ApiStatus } from "@/types/common";
import { useTemplatesFromSearchParams } from "@/lib/hooks/useTemplates";

// Component that uses useSearchParams and TanStack Query
function TemplateContent() {
  // Use the custom hook that handles search params and data fetching
  const {
    data: response,
    isLoading,
    isError,
    page,
    limit,
  } = useTemplatesFromSearchParams();

  // Extract data from the query response
  const templates =
    response?.status === ApiStatus.SUCCESS ? response.data.data : [];
  const totalCount =
    response?.status === ApiStatus.SUCCESS ? response.data.total : 0;

  return (
    <div className="bg-white">
      <Sheet>
        <div>
          <SheetContent side="left">
            <SheetHeader>
              <SheetTitle>Filters</SheetTitle>
            </SheetHeader>
            <TemplateFilters />
          </SheetContent>

          <main className="mx-auto container px-4 py-1 sm:px-6 sm:py-4 lg:px-8">
            <div className="border-b border-gray-200 pb-10">
              <h1 className="text-4xl font-bold font-heading tracking-tight text-gray-900">
                Template Catalog
              </h1>
              <p className="mt-4 text-base text-gray-500">
                Explore our wide range of templates across various categories
                and skill levels.
              </p>
            </div>

            <div className="pt-12 lg:grid lg:grid-cols-3 lg:gap-x-8 xl:grid-cols-4">
              <aside>
                <h2 className="sr-only">Filters</h2>

                <SheetTrigger asChild>
                  <Button
                    variant="outline"
                    className="inline-flex items-center lg:hidden"
                  >
                    <span className="text-sm font-medium">Filters</span>
                    <PlusIcon className="ml-1 h-5 w-5 flex-shrink-0" />
                  </Button>
                </SheetTrigger>

                <div className="hidden lg:block">
                  <TemplateFilters />
                </div>
              </aside>

              <div className="mt-6 lg:col-span-2 lg:mt-0 xl:col-span-3">
                {isLoading ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Array.from({ length: 6 }).map((_, i) => (
                      <div
                        key={i}
                        className="flex flex-col border rounded-lg overflow-hidden"
                      >
                        <div className="h-48 bg-gray-200 animate-pulse"></div>
                        <div className="p-4 space-y-2">
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : isError ? (
                  <div className="text-center py-10">
                    <p className="text-red-500">
                      Error loading templates. Please try again later.
                    </p>
                  </div>
                ) : templates.length === 0 ? (
                  <div className="text-center py-10">
                    <p className="text-gray-500">
                      No templates found matching your criteria.
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {templates.map((template) => (
                      <div
                        key={template._id}
                        className="flex flex-col border rounded-lg overflow-hidden"
                      >
                        <TemplateCard template={template} />
                      </div>
                    ))}
                  </div>
                )}

                <div className="mt-8">
                  <PaginationWithLinks
                    page={page}
                    pageSize={limit}
                    totalCount={totalCount}
                  />
                </div>
              </div>
            </div>
          </main>
        </div>
      </Sheet>
    </div>
  );
}

// Main component that wraps the content in a Suspense boundary
export default function TemplateCatalogPage() {
  return (
    <Suspense fallback={<TemplateLoadingSkeleton />}>
      <TemplateContent />
    </Suspense>
  );
}

// Loading skeleton component
function TemplateLoadingSkeleton() {
  return (
    <div className="bg-white">
      <main className="mx-auto container px-4 py-1 sm:px-6 sm:py-4 lg:px-8">
        <div className="border-b border-gray-200 pb-10">
          <div className="h-8 bg-gray-200 rounded w-1/3 animate-pulse mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
        </div>

        <div className="pt-12 lg:grid lg:grid-cols-3 lg:gap-x-8 xl:grid-cols-4">
          <aside>
            <div className="hidden lg:block">
              <div className="space-y-4">
                <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div
                      key={i}
                      className="h-4 bg-gray-200 rounded animate-pulse"
                    ></div>
                  ))}
                </div>
              </div>
            </div>
          </aside>

          <div className="mt-6 lg:col-span-2 lg:mt-0 xl:col-span-3">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div
                  key={i}
                  className="flex flex-col border rounded-lg overflow-hidden"
                >
                  <div className="h-48 bg-gray-200 animate-pulse"></div>
                  <div className="p-4 space-y-2">
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
