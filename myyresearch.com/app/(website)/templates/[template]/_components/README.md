# Add to Cart Component

A comprehensive React component for adding templates to the shopping cart with full cart context integration, loading states, error handling, and user feedback.

## Features

- ✅ **Cart Context Integration**: Uses the new cart context hooks for state management
- ✅ **Loading States**: Visual feedback during cart operations
- ✅ **Error Handling**: Comprehensive error handling with user-friendly messages
- ✅ **Toast Notifications**: Success and error notifications
- ✅ **Cart State Detection**: Shows different states based on cart contents
- ✅ **Async Operations**: Proper async/await handling for cart operations
- ✅ **TypeScript Support**: Full type safety throughout
- ✅ **Accessibility**: Proper ARIA labels and keyboard navigation
- ✅ **Responsive Design**: Works on all screen sizes

## Usage

### Basic Implementation

```tsx
import AddToCart from "./_components/add-to-cart";
import { PublicTemplateResponse } from "@/types/template";

function TemplatePage({ template }: { template: PublicTemplateResponse }) {
  return (
    <div>
      <h1>{template.title}</h1>
      <p>{template.shortDescription}</p>
      
      {/* Add to Cart Component */}
      <AddToCart template={template} />
    </div>
  );
}
```

### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `template` | `PublicTemplateResponse` | Yes | The template object containing all template data |

### Template Object Requirements

The template object must include:
- `_id`: Unique template identifier
- `title`: Template title for notifications
- Other template properties as defined in `PublicTemplateResponse`

## Component States

### 1. Default State
- Shows "Add to Cart" button with shopping cart icon
- Shows "Buy Now" button with credit card icon
- Both buttons are enabled and ready for interaction

### 2. Loading State
- Shows "Adding..." text with spinning loader icon
- Both buttons are disabled during the operation
- Prevents multiple simultaneous requests

### 3. In Cart State
- "Add to Cart" button shows "In Cart" with check icon
- Button is disabled to prevent duplicate additions
- Shows success message with link to checkout
- "Buy Now" button remains enabled for direct purchase

### 4. Error State
- Displays error message below buttons
- Shows specific error details
- Buttons remain functional for retry attempts

## Cart Operations

### Add to Cart
```tsx
const handleAddToCart = async () => {
  try {
    await addTemplateToCartAsync(templateId, 1);
    // Success toast notification
  } catch (error) {
    // Error toast notification
  }
};
```

### Buy Now
```tsx
const handleBuyNow = async () => {
  try {
    await addTemplateToCartAsync(templateId, 1);
    router.push("/checkout"); // Redirect to checkout
  } catch (error) {
    // Error toast notification
  }
};
```

## Visual States

### Button States

#### Add to Cart Button
- **Default**: Blue background, shopping cart icon, "Add to Cart" text
- **Loading**: Gray background, spinning loader, "Adding..." text
- **In Cart**: Gray background, check icon, "In Cart" text
- **Disabled**: Reduced opacity, not clickable

#### Buy Now Button
- **Default**: Light blue background, credit card icon, "Buy Now" text
- **Loading**: Reduced opacity, spinning loader, "Adding..." text
- **Disabled**: Reduced opacity, not clickable

### Feedback Messages

#### Success State
```tsx
<div className="bg-green-50 border border-green-200">
  <Check className="text-green-600" />
  <p className="text-green-700">
    Success! This template is already in your cart.
    <button onClick={() => router.push("/checkout")}>
      Go to checkout
    </button>
  </p>
</div>
```

#### Error State
```tsx
<div className="bg-red-50 border border-red-200">
  <p className="text-red-600">
    <strong>Error:</strong> {error.message}
  </p>
</div>
```

## Toast Notifications

### Success Toast
```tsx
toast({
  title: "Added to cart",
  description: `${template.title} has been added to your cart.`,
});
```

### Error Toast
```tsx
toast({
  title: "Error",
  description: "Failed to add template to cart. Please try again.",
  variant: "destructive",
});
```

## Error Handling

The component handles several error scenarios:

1. **Missing Template ID**: Shows error if template._id is undefined
2. **Network Errors**: Handles API failures gracefully
3. **Cart Operation Failures**: Shows user-friendly error messages
4. **Validation Errors**: Prevents invalid operations

## Accessibility Features

- **Keyboard Navigation**: All buttons are keyboard accessible
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Focus Management**: Clear focus indicators
- **Color Contrast**: Meets WCAG guidelines
- **Loading States**: Screen readers announce state changes

## Styling

The component uses Tailwind CSS classes for styling:

- **Responsive Grid**: `grid-cols-1 sm:grid-cols-2`
- **Button Styling**: Consistent with design system
- **State Colors**: Green for success, red for errors, blue for actions
- **Transitions**: Smooth color transitions on state changes

## Dependencies

- `@/contexts/cart-context`: Cart state management
- `@/hooks/use-toast`: Toast notifications
- `lucide-react`: Icons
- `next/navigation`: Router for navigation
- `@/types/template`: TypeScript types

## Testing

Use the demo component for testing:

```tsx
import AddToCartDemo from "./_components/add-to-cart-demo";

function TestPage() {
  return <AddToCartDemo />;
}
```

The demo component provides:
- Mock template data
- Real-time cart status display
- Usage instructions
- Interactive testing environment

## Best Practices

1. **Always handle errors**: Wrap cart operations in try-catch blocks
2. **Provide user feedback**: Use toast notifications for all operations
3. **Show loading states**: Keep users informed during async operations
4. **Validate inputs**: Check for required data before operations
5. **Maintain accessibility**: Ensure keyboard and screen reader support

## Integration Notes

This component is designed to work seamlessly with:
- Cart context provider
- Template detail pages
- Checkout flow
- Navigation cart badge
- Toast notification system

The component automatically syncs with the global cart state and updates all related UI components in real-time.
