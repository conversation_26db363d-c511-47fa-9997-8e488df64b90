"use client";

import { useRouter } from "next/navigation";
import { PublicTemplateResponse } from "@/types/template";
import { useCartData, useAddToCartHelper } from "@/contexts/cart-context";
import { useToast } from "@/hooks/use-toast";
import { Loader2, ShoppingCart, Check, CreditCard } from "lucide-react";

export default function AddToCart({
  template,
}: {
  template: PublicTemplateResponse;
}) {
  const router = useRouter();
  const { toast } = useToast();

  // Cart context hooks
  const { cart } = useCartData();
  const { addTemplateToCartAsync, isAddingToCart, addToCartError } =
    useAddToCartHelper();

  // Get template ID safely
  const templateId = template._id || "";

  // Check if template is in cart by comparing the template ID with cart item's itemId
  const isInCart =
    cart?.items.some((item) => {
      return item.itemId === templateId;
    }) || false;

  const handleAddToCart = async () => {
    if (!templateId) {
      toast({
        title: "Error",
        description: "Template ID is missing.",
        variant: "destructive",
      });
      return;
    }

    try {
      await addTemplateToCartAsync(templateId, 1);
      toast({
        title: "Added to cart",
        description: `${template.title} has been added to your cart.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add template to cart. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleBuyNow = async () => {
    if (!templateId) {
      toast({
        title: "Error",
        description: "Template ID is missing.",
        variant: "destructive",
      });
      return;
    }

    try {
      await addTemplateToCartAsync(templateId, 1);
      router.push("/checkout");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add template to cart. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <div className="mt-10 grid grid-cols-1 gap-x-6 gap-y-4 sm:grid-cols-2">
        {/* Add to Cart Button */}
        <button
          type="button"
          onClick={handleAddToCart}
          disabled={isInCart || isAddingToCart}
          className="flex w-full items-center justify-center gap-2 rounded-md border border-transparent bg-indigo-600 px-8 py-3 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-50 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200"
        >
          {isAddingToCart ? (
            <>
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Adding...</span>
            </>
          ) : isInCart ? (
            <>
              <Check className="h-5 w-5" />
              <span>In Cart</span>
            </>
          ) : (
            <>
              <ShoppingCart className="h-5 w-5" />
              <span>Add to Cart</span>
            </>
          )}
        </button>

        {/* Buy Now Button */}
        <button
          type="button"
          onClick={handleBuyNow}
          disabled={isAddingToCart}
          className="flex w-full items-center justify-center gap-2 rounded-md border border-transparent bg-indigo-50 px-8 py-3 text-base font-medium text-indigo-700 hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          {isAddingToCart ? (
            <>
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Adding...</span>
            </>
          ) : (
            <>
              <CreditCard className="h-5 w-5" />
              <span>Buy Now</span>
            </>
          )}
        </button>
      </div>

      {/* Error Display */}
      {addToCartError && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">
            <strong>Error:</strong> {addToCartError.message}
          </p>
        </div>
      )}

      {/* Success State Information */}
      {isInCart && (
        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center gap-2">
            <Check className="h-5 w-5 text-green-600" />
            <p className="text-sm text-green-700">
              <strong>Success!</strong> This template is already in your cart.{" "}
              <button
                onClick={() => router.push("/checkout")}
                className="underline hover:no-underline font-medium"
              >
                Go to checkout
              </button>
            </p>
          </div>
        </div>
      )}
    </>
  );
}
