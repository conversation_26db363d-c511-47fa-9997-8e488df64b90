"use client";

import { useCartData } from "@/contexts/cart-context";
import AddToCart from "./add-to-cart";
import { PublicTemplateResponse } from "@/types/template";

/**
 * Demo component to showcase the AddToCart functionality
 * This can be used for testing and demonstration purposes
 */
export function AddToCartDemo() {
  const { cart, totalItems, totalAmount, isLoading } = useCartData();

  // Mock template data for demonstration
  const mockTemplate: PublicTemplateResponse = {
    _id: "demo-template-123",
    title: "Demo Research Template",
    shortDescription: "A sample template for demonstration",
    longDescription: "This is a detailed description of the demo template",
    price: 29.99,
    discountPrice: 19.99,
    thumbnail: "/placeholder-template.jpg",
    category: "research",
    subcategory: "academic",
    tags: ["research", "academic", "demo"],
    downloadCount: 150,
    rating: 4.5,
    reviewCount: 25,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-8">
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Add to Cart Component Demo
        </h1>
        
        {/* Template Info */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">{mockTemplate.title}</h2>
          <p className="text-gray-600 mb-2">{mockTemplate.shortDescription}</p>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span>Price: ${mockTemplate.price}</span>
            {mockTemplate.discountPrice && (
              <span className="text-green-600">
                Sale: ${mockTemplate.discountPrice}
              </span>
            )}
            <span>Rating: {mockTemplate.rating}/5</span>
          </div>
        </div>

        {/* Add to Cart Component */}
        <AddToCart template={mockTemplate} />
      </div>

      {/* Cart Status */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-blue-900 mb-4">
          Current Cart Status
        </h2>
        
        {isLoading ? (
          <p className="text-blue-700">Loading cart...</p>
        ) : (
          <div className="space-y-2 text-blue-800">
            <p><strong>Total Items:</strong> {totalItems}</p>
            <p><strong>Total Amount:</strong> ${totalAmount.toFixed(2)}</p>
            
            {cart?.items && cart.items.length > 0 && (
              <div className="mt-4">
                <h3 className="font-medium mb-2">Items in Cart:</h3>
                <ul className="space-y-1">
                  {cart.items.map((item) => (
                    <li key={item._id} className="text-sm">
                      • {item.title} - Qty: {item.quantity} - $
                      {(item.discountPrice || item.price).toFixed(2)}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Usage Instructions */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h2 className="text-lg font-semibold text-yellow-900 mb-4">
          How to Use
        </h2>
        <div className="text-yellow-800 space-y-2 text-sm">
          <p>1. Click "Add to Cart" to add the template to your cart</p>
          <p>2. The button will show loading state while processing</p>
          <p>3. Once added, the button will show "In Cart" state</p>
          <p>4. Click "Buy Now" to add to cart and go directly to checkout</p>
          <p>5. Toast notifications will show success/error messages</p>
          <p>6. Cart status updates in real-time above</p>
        </div>
      </div>
    </div>
  );
}

export default AddToCartDemo;
