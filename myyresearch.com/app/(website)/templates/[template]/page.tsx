"use client";

import { Fragment } from "react";
import { AiFillStar } from "react-icons/ai";
import { Tab, TabGroup, Tab<PERSON>ist, TabPanel, TabP<PERSON><PERSON> } from "@headlessui/react";
import Image from "next/image";
import AddToCart from "./_components/add-to-cart";
import Viewer from "@/components/editor/viewer";

import { HiDownload, HiEye } from "react-icons/hi";
import { useTemplateDetails } from "@/lib/hooks/useTemplates";
import { ApiStatus } from "@/types/common";
import { useParams } from "next/navigation";
import { Loader2, Award } from "lucide-react";
import Link from "next/link";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

// Types for FAQ and License content
interface FAQ {
  question: string;
  answer: string;
}

interface License {
  href: string;
  summary: string;
  content: string;
}

interface TemplateContent {
  faqs: FAQ[];
  license: License;
}

// Dynamic FAQ and License content based on template category
const getTemplateContent = (category: string): TemplateContent => {
  if (category === "assignment-templates") {
    return {
      faqs: [
        {
          question: "What format are the templates in?",
          answer:
            "Our templates come in Microsoft Word (.docx), PowerPoint (.pptx), and PDF formats. They are easy to edit and ready to use.",
        },
        {
          question: "Can I change the content or design of the templates?",
          answer:
            "Yes! You are free to edit the templates to fit your needs, including changing colors, text, or layout.",
        },
        {
          question:
            "Can I use these templates for my school or professional work?",
          answer:
            "Absolutely. You can use them for assignments, presentations, reports, or any educational or business purpose.",
        },
        {
          question:
            "Can I share these templates with my friends or colleagues?",
          answer:
            "No, sorry. These templates are only for your personal or professional use. Please ask your friends to download their own copy from our website.",
        },
      ],
      license: {
        href: "#",
        summary:
          "For personal and professional use. You cannot resell or redistribute these templates in their original or modified state.",
        content: `
          <h4>Usage Rights</h4>
          <ul role="list">
            <li>You can use the templates for both personal and professional work.</li>
            <li>You may use them in unlimited projects.</li>
          </ul>
          
          <h4>What You Can Do</h4>
          <ul role="list">
            <li>Edit the templates to match your brand or project</li>
            <li>Use them in reports, presentations, or assignments</li>
            <li>Keep them for your future projects</li>
          </ul>
          
          <h4>What You Cannot Do</h4>
          <ul role="list">
            <li>Do not sell, resell, or share the original or edited templates</li>
            <li>Do not upload them to other websites or marketplaces</li>
            <li>Do not use the templates for projects that support illegal or harmful activities</li>
          </ul>
          
          <p><strong>Note:</strong> These templates are created to support learning and research. Please use them ethically and responsibly.</p>
        `,
      },
    };
  } else if (category === "presentation-templates") {
    return {
      faqs: [
        {
          question: "What format are these presentation templates?",
          answer:
            "The templates are available in Microsoft PowerPoint (.pptx) format. You can open and edit them in PowerPoint or similar software like Google Slides.",
        },
        {
          question: "Can I change the design and content?",
          answer:
            "Yes, you can fully customize the slides. You can edit text, change colors, add your logo, and insert new images to suit your project.",
        },
        {
          question: "Who can use these templates?",
          answer:
            "These templates are made for students, teachers, and professionals. You can use them for school projects, business presentations, or research work.",
        },
        {
          question: "Can I use them more than once?",
          answer:
            "Yes! Once you download the template, you can use it for as many projects as you like.",
        },
        {
          question: "Can I share or sell these templates?",
          answer:
            "No. You cannot share, resell, or upload the templates (original or edited versions) to other websites or platforms.",
        },
      ],
      license: {
        href: "#",
        summary:
          "For personal, academic, or professional use. You cannot resell or redistribute these templates in their original or modified state.",
        content: `
          <h4>Allowed Use</h4>
          <ul role="list">
            <li>You can use these presentation templates for personal, academic, or professional purposes.</li>
            <li>You may use them in unlimited projects after purchase.</li>
          </ul>
          
          <h4>You Can</h4>
          <ul role="list">
            <li>Edit the slides to match your topic, style, or brand</li>
            <li>Use the templates for your presentations, meetings, or academic submissions</li>
            <li>Keep and reuse them for future work</li>
          </ul>
          
          <h4>You Cannot</h4>
          <ul role="list">
            <li>Sell or share the templates in their original or modified form</li>
            <li>Upload the templates to other marketplaces or websites</li>
            <li>Use the templates in projects that involve harmful, illegal, or unethical content</li>
          </ul>
          
          <p><strong>Note:</strong> These templates are designed to support creative learning and professional work. Please use them responsibly and follow the license rules.</p>
        `,
      },
    };
  }

  // Default fallback content
  return {
    faqs: [
      {
        question: "What format are these templates?",
        answer:
          "The templates are available in various formats depending on the type. Please check the description for specific format details.",
      },
      {
        question: "Can I use the templates at different sizes?",
        answer:
          "Yes. The templates can be scaled to different sizes as needed while maintaining quality and visual balance.",
      },
    ],
    license: {
      href: "#",
      summary:
        "For personal and professional use. You cannot resell or redistribute these templates in their original or modified state.",
      content: `
        <h4>Overview</h4>
        <p>For personal and professional use. You cannot resell or redistribute these templates in their original or modified state.</p>
        
        <ul role="list">
        <li>You're allowed to use the templates in unlimited projects.</li>
        <li>Attribution is not required to use the templates.</li>
        </ul>
        
        <h4>What you can do with it</h4>
        <ul role="list">
        <li>Use them freely in your personal and professional work.</li>
        <li>Make them your own. Change the colors to suit your project or brand.</li>
        </ul>
        
        <h4>What you can't do with it</h4>
        <ul role="list">
        <li>Don't be greedy. Selling or distributing these templates in their original or modified state is prohibited.</li>
        <li>Don't be evil. These templates cannot be used on websites or applications that promote illegal or immoral beliefs or activities.</li>
        </ul>
      `,
    },
  };
};

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export default function TemplatePage() {
  const params = useParams();
  const templateId = params.template as string;

  const {
    data: response,
    isLoading,
    isError,
    error,
  } = useTemplateDetails(templateId);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="w-6 h-6 animate-spin" />
          <span>Loading template details...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (
    isError ||
    !response ||
    response.status !== ApiStatus.SUCCESS ||
    !response.data
  ) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Template Not Found
          </h1>
          <p className="text-gray-600 mb-4">
            {error?.message || "The requested template could not be found."}
          </p>
          <Link
            href="/templates"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Browse Templates
          </Link>
        </div>
      </div>
    );
  }

  const template = response.data;

  // Get dynamic content based on template category
  const templateContent = getTemplateContent(template.category);
  const { faqs, license } = templateContent;

  return (
    <div>
      <div className="container mx-auto px-4 py-4 sm:px-6 sm:py-12 lg:px-8">
        {/* Add Breadcrumb */}
        <Breadcrumb className="mb-8">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/templates">Templates</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{template.title}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Product */}
        <div className="lg:grid lg:grid-cols-7 lg:grid-rows-1 lg:gap-x-8 lg:gap-y-10 xl:gap-x-16">
          {/* Product image */}
          <div className="lg:col-span-4 lg:row-end-1">
            <div className="space-y-4">
              {/* Thumbnail */}
              <div className="aspect-h-3 aspect-w-4 overflow-hidden rounded-lg bg-gray-100 relative">
                <Image
                  alt={template.title}
                  src={`${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${template.thumbnail.key}`}
                  className="object-cover object-center"
                  width={1280}
                  height={720}
                />
                {/* Download button - show different content based on purchase status */}
                {template.downloadMedia && (
                  <div className="absolute top-4 right-4">
                    <a
                      href={`${process.env.NEXT_PUBLIC_AWS_FILES_CLOUDFRONT_URL}/${template.downloadMedia.key}`}
                      target="_blank"
                      download
                      className={`inline-flex items-center justify-center gap-2 rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 ${
                        template.purchased
                          ? "bg-green-600 hover:bg-green-500 focus-visible:outline-green-600"
                          : "bg-indigo-600 hover:bg-indigo-500 focus-visible:outline-indigo-600"
                      }`}
                    >
                      <HiDownload className="h-4 w-4" />
                      {template.purchased
                        ? "Download Full Template"
                        : "Download Preview"}
                    </a>
                  </div>
                )}

                {/* Preview button for non-purchased templates */}
                {!template.purchased && template.previewMedia && (
                  <div className="absolute top-4 left-4">
                    <a
                      href={`${process.env.NEXT_PUBLIC_AWS_FILES_CLOUDFRONT_URL}/${template.previewMedia.key}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center justify-center gap-2 rounded-md bg-gray-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600"
                    >
                      <HiEye className="h-4 w-4" />
                      Preview
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Product details */}
          <div className="mx-auto mt-14 max-w-2xl sm:mt-16 lg:col-span-3 lg:row-span-2 lg:row-end-2 lg:mt-0 lg:max-w-none">
            <div className="flex flex-col-reverse">
              <div className="mt-4">
                <h1 className="text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl">
                  {template.title}
                </h1>

                {/* Purchase Status Indicator */}
                {template.purchased && (
                  <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Award className="w-5 h-5 text-green-600" />
                      <span className="text-green-800 font-medium">
                        ✓ You have purchased this template
                      </span>
                    </div>
                    <p className="text-green-700 text-sm mt-1">
                      You now have access to download the full template files.
                    </p>
                  </div>
                )}

                <h2 id="information-heading" className="sr-only">
                  Product information
                </h2>
                <p className="mt-2 text-sm text-gray-500">
                  Updated {new Date(template.updatedAt).toLocaleDateString()}
                </p>
              </div>

              {/* Reviews summary */}
              <div>
                <h3 className="sr-only">Reviews</h3>
                <div className="flex items-center">
                  {/* {[0, 1, 2, 3, 4].map((rating) => (
                    <AiFillStar
                      key={rating}
                      aria-hidden="true"
                      className={classNames(
                        template.averageReview > rating
                          ? "text-yellow-400"
                          : "text-gray-300",
                        "h-5 w-5 flex-shrink-0"
                      )}
                    />
                  ))} */}
                  {/* <p className="ml-2 text-sm text-gray-500">
                    ({template.reviewCount} reviews)
                  </p> */}
                </div>
                <p className="sr-only">
                  {template.averageReview} out of 5 stars
                </p>
              </div>
            </div>

            {/* Add pricing details */}
            <div className="mt-6">
              <div className="flex items-center">
                {template.discountPrice ? (
                  <>
                    <p className="text-3xl font-bold text-gray-900">
                      ${template.discountPrice}
                    </p>
                    <p className="ml-3 text-lg text-gray-500 line-through">
                      ${template.price}
                    </p>
                    <p className="ml-3 text-sm text-green-600">
                      {Math.round(
                        ((template.price - template.discountPrice) /
                          template.price) *
                          100
                      )}
                      % off
                    </p>
                  </>
                ) : template.free ? (
                  <p className="text-3xl font-bold text-gray-900">Free</p>
                ) : (
                  <p className="text-3xl font-bold text-gray-900">
                    ${template.price}
                  </p>
                )}
              </div>
            </div>

            <p className="mt-6 text-gray-500">{template.shortDescription}</p>

            {/* Enhanced download section for purchased templates */}
            {/* {template.purchased && template.downloadMedia && (
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="text-lg font-medium text-green-800 mb-3">
                  Your Template Downloads
                </h3>
                <div className="space-y-2">
                  <a
                    href={template.downloadMedia.url}
                    download
                    className="inline-flex items-center justify-center gap-2 w-full rounded-md bg-green-600 px-4 py-3 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600"
                  >
                    <HiDownload className="h-5 w-5" />
                    Download Full Template (
                    {template.downloadMedia.size
                      ? `${Math.round(template.downloadMedia.size / 1024 / 1024)}MB`
                      : "File"}
                    )
                  </a>
                  {template.previewMedia && (
                    <a
                      href={template.previewMedia.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center justify-center gap-2 w-full rounded-md bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-500"
                    >
                      <HiEye className="h-4 w-4" />
                      View Preview
                    </a>
                  )}
                </div>
              </div>
            )} */}

            {/* Conditional rendering based on template status */}
            {template.purchased || template.free || template.price === 0 ? (
              /* Download Now Button */
              <div className="mt-10">
                {template.downloadMedia ? (
                  <a
                    href={`${process.env.NEXT_PUBLIC_AWS_FILES_CLOUDFRONT_URL}/${template.downloadMedia.key}`}
                    download
                    target="_blank"
                    className="flex w-full items-center justify-center gap-2 rounded-md border border-transparent bg-green-600 px-8 py-3 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-gray-50 transition-colors duration-200"
                  >
                    <HiDownload className="h-5 w-5" />
                    <span>Download Now</span>
                  </a>
                ) : (
                  <div className="flex w-full items-center justify-center gap-2 rounded-md border border-gray-300 px-8 py-3 text-base font-medium text-gray-500 bg-gray-100 cursor-not-allowed">
                    <HiDownload className="h-5 w-5" />
                    <span>Download Not Available</span>
                  </div>
                )}
              </div>
            ) : (
              /* Add to Cart and Buy Now Buttons */
              <AddToCart template={template} />
            )}

            {/* Template Description */}
            <div className="pt-6">
              <Viewer content={template.description} />
            </div>

            {/* Reviews Section */}
            {template.reviews.length > 0 && (
              <div className="mt-10 border-t border-gray-200 pt-10">
                <h3 className="text-lg font-medium text-gray-900">Reviews</h3>
                <div className="mt-6 space-y-6">
                  {template.reviews.map((review) => (
                    <div key={review._id} className="relative">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center">
                          {[0, 1, 2, 3, 4].map((rating) => (
                            <AiFillStar
                              key={rating}
                              aria-hidden="true"
                              className={classNames(
                                review.rating > rating
                                  ? "text-yellow-400"
                                  : "text-gray-300",
                                "h-4 w-4 flex-shrink-0"
                              )}
                            />
                          ))}
                        </div>
                        <p className="font-medium text-gray-900">
                          {review.user.firstName} {review.user.lastName}
                        </p>
                        <time
                          dateTime={review.createdAt.toString()}
                          className="text-sm text-gray-500"
                        >
                          {new Date(review.createdAt).toLocaleDateString()}
                        </time>
                      </div>
                      <p className="mt-4 text-gray-500">{review.comment}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="mt-10 border-t border-gray-200 pt-10">
              <h3 className="text-sm font-medium text-gray-900">License</h3>
              <p className="mt-4 text-sm text-gray-500">
                {license.summary}{" "}
                {/* <a
                  href={license.href}
                  className="font-medium text-indigo-600 hover:text-indigo-500"
                >
                  Read full license
                </a> */}
              </p>
            </div>

            <div className="mt-10 border-t border-gray-200 pt-10">
              <h3 className="text-sm font-medium text-gray-900">Share</h3>
              <ul role="list" className="mt-4 flex items-center space-x-6">
                <li>
                  <a
                    href="#"
                    className="flex h-6 w-6 items-center justify-center text-gray-400 hover:text-gray-500"
                  >
                    <span className="sr-only">Share on Facebook</span>
                    <svg
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      aria-hidden="true"
                      className="h-5 w-5"
                    >
                      <path
                        d="M20 10c0-5.523-4.477-10-10-10S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z"
                        clipRule="evenodd"
                        fillRule="evenodd"
                      />
                    </svg>
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="flex h-6 w-6 items-center justify-center text-gray-400 hover:text-gray-500"
                  >
                    <span className="sr-only">Share on Instagram</span>
                    <svg
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                      className="h-6 w-6"
                    >
                      <path
                        d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                        clipRule="evenodd"
                        fillRule="evenodd"
                      />
                    </svg>
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="flex h-6 w-6 items-center justify-center text-gray-400 hover:text-gray-500"
                  >
                    <span className="sr-only">Share on X</span>
                    <svg
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      aria-hidden="true"
                      className="h-5 w-5"
                    >
                      <path d="M11.4678 8.77491L17.2961 2H15.915L10.8543 7.88256L6.81232 2H2.15039L8.26263 10.8955L2.15039 18H3.53159L8.87581 11.7878L13.1444 18H17.8063L11.4675 8.77491H11.4678ZM9.57608 10.9738L8.95678 10.0881L4.02925 3.03974H6.15068L10.1273 8.72795L10.7466 9.61374L15.9156 17.0075H13.7942L9.57608 10.9742V10.9738Z" />
                    </svg>
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="mx-auto mt-16 w-full max-w-2xl lg:col-span-4 lg:mt-0 lg:max-w-none">
            <TabGroup>
              <div className="border-b border-gray-200">
                <TabList className="-mb-px flex space-x-8">
                  {/* <Tab className="whitespace-nowrap border-b-2 border-transparent py-6 text-sm font-medium text-gray-700 hover:border-gray-300 hover:text-gray-800 data-[selected]:border-indigo-600 data-[selected]:text-indigo-600">
                    Customer Reviews
                  </Tab> */}
                  <Tab className="whitespace-nowrap border-b-2 border-transparent py-6 text-sm font-medium text-gray-700 hover:border-gray-300 hover:text-gray-800 data-[selected]:border-indigo-600 data-[selected]:text-indigo-600">
                    FAQ
                  </Tab>
                  <Tab className="whitespace-nowrap border-b-2 border-transparent py-6 text-sm font-medium text-gray-700 hover:border-gray-300 hover:text-gray-800 data-[selected]:border-indigo-600 data-[selected]:text-indigo-600">
                    License
                  </Tab>
                </TabList>
              </div>
              <TabPanels as={Fragment}>
                {/* <TabPanel className="-mb-10">
                  <h3 className="sr-only">Customer Reviews</h3>

                  {reviews.featured.map((review, reviewIdx) => (
                    <div
                      key={review.id}
                      className="flex space-x-4 text-sm text-gray-500"
                    >
                      <div className="flex-none py-10">
                        <img
                          alt=""
                          src={review.avatarSrc}
                          className="h-10 w-10 rounded-full bg-gray-100"
                        />
                      </div>
                      <div
                        className={classNames(
                          reviewIdx === 0 ? "" : "border-t border-gray-200",
                          "py-10"
                        )}
                      >
                        <h3 className="font-medium text-gray-900">
                          {review.author}
                        </h3>
                        <p>
                          <time dateTime={review.datetime}>{review.date}</time>
                        </p>

                        <div className="mt-4 flex items-center">
                          {[0, 1, 2, 3, 4].map((rating) => (
                            <AiFillStar
                              key={rating}
                              aria-hidden="true"
                              className={classNames(
                                review.rating > rating
                                  ? "text-yellow-400"
                                  : "text-gray-300",
                                "h-5 w-5 flex-shrink-0"
                              )}
                            />
                          ))}
                        </div>
                        <p className="sr-only">
                          {review.rating} out of 5 stars
                        </p>

                        <div
                          dangerouslySetInnerHTML={{ __html: review.content }}
                          className="prose prose-sm mt-4 max-w-none text-gray-500"
                        />
                      </div>
                    </div>
                  ))}
                </TabPanel> */}

                <TabPanel className="text-sm text-gray-500">
                  <h3 className="sr-only">Frequently Asked Questions</h3>

                  <div className="space-y-6 mt-10">
                    {faqs.map((faq, index) => (
                      <div
                        key={faq.question}
                        className="bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors duration-200 overflow-hidden"
                      >
                        <div className="p-6">
                          <dt className="flex items-start gap-3">
                            <div className="flex-shrink-0 mt-1">
                              <div className="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full">
                                <span className="text-sm font-semibold text-blue-600">
                                  {index + 1}
                                </span>
                              </div>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 leading-6">
                              {faq.question}
                            </h3>
                          </dt>
                          <dd className="mt-4 ml-9">
                            <div className="prose prose-sm max-w-none text-gray-600 leading-relaxed">
                              <p>{faq.answer}</p>
                            </div>
                          </dd>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-8 p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-gray-200">
                    <div className="flex items-center gap-3">
                      <svg
                        className="w-6 h-6 text-blue-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      <div>
                        <h4 className="font-semibold text-gray-900">
                          Still have questions?
                        </h4>
                        <p className="text-sm text-gray-600 mt-1">
                          Can't find what you're looking for?
                          <a
                            href="/contact-us"
                            className="text-blue-600 hover:text-blue-800 font-medium ml-1 underline"
                          >
                            Get in touch with our team
                          </a>
                        </p>
                      </div>
                    </div>
                  </div>
                </TabPanel>

                <TabPanel className="pt-10">
                  <h3 className="sr-only">License</h3>

                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-8 border border-blue-100">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="flex-shrink-0">
                        <svg
                          className="w-8 h-8 text-blue-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                          />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-gray-900">
                          License Agreement
                        </h4>
                        <p className="text-sm text-gray-600">
                          Terms and conditions for template usage
                        </p>
                      </div>
                    </div>

                    <div
                      dangerouslySetInnerHTML={{ __html: license.content }}
                      className="prose prose-sm max-w-none text-gray-700 [&>h4]:text-lg [&>h4]:font-semibold [&>h4]:text-gray-900 [&>h4]:mb-3 [&>h4]:mt-6 [&>h4:first-child]:mt-0 [&>ul]:space-y-2 [&>li]:flex [&>li]:items-start [&>li]:gap-2 [&>li:before]:content-['✓'] [&>li:before]:text-green-600 [&>li:before]:font-bold [&>li:before]:mt-0.5 [&>li:before]:flex-shrink-0 [&>p]:text-gray-700 [&>p>strong]:text-gray-900 [&>p>strong]:font-semibold"
                    />

                    <div className="mt-8 pt-6 border-t border-blue-200">
                      <div className="flex items-center gap-2 text-sm text-blue-700">
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        <span className="font-medium">
                          Questions about licensing?
                        </span>
                        <a
                          href="/contact-us"
                          className="text-blue-600 hover:text-blue-800 underline"
                        >
                          Contact our support team
                        </a>
                      </div>
                    </div>
                  </div>
                </TabPanel>
              </TabPanels>
            </TabGroup>
          </div>
        </div>
      </div>
    </div>
  );
}
