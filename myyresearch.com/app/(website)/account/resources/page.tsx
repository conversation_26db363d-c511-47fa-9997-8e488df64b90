"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { useUserPurchases } from "@/lib/hooks/useOrders";
import { PurchasedItemsGrid } from "@/components/account/purchased-items-grid";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { OrderItemDetails, OrderItemType } from "@/types/orders";
import { Star, AlertCircle } from "lucide-react";
import Link from "next/link";

export default function ResourcesPage() {
  const router = useRouter();

  const {
    data: purchasesResponse,
    isLoading,
    error,
    refetch,
  } = useUserPurchases({
    page: 1,
    limit: 50, // Get more items for better user experience
    sortBy: "orderDate",
    sortOrder: "desc",
  });

  const handleItemAccess = (item: OrderItemDetails) => {
    // Navigate to the appropriate page based on item type
    if (item.itemType === OrderItemType.COURSE) {
      router.push(`/courses/${item.publicId}`);
    } else if (item.itemType === OrderItemType.TEMPLATE) {
      router.push(`/templates/${item.publicId}`);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 w-48 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 w-64 bg-gray-200 rounded animate-pulse mt-2" />
          </div>
        </div>

        {/* Loading grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="h-64">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <Skeleton className="h-16 w-16 rounded-lg" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                  <div className="flex justify-between">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-8 w-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Resources</h1>
            <p className="text-gray-600 mt-1">
              Access your purchased courses and templates
            </p>
          </div>
        </div>

        <Card>
          <CardContent className="p-12">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Error Loading Resources
              </h3>
              <p className="text-gray-600 mb-4">
                We couldn't load your purchased items. Please try again.
              </p>
              <Button onClick={() => refetch()}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const purchases = purchasesResponse?.data?.data || [];
  const total = purchasesResponse?.data?.total || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Resources</h1>
          <p className="text-gray-600 mt-1">
            Access your purchased courses and templates ({total} items)
          </p>
        </div>
      </div>

      {/* Content */}
      {purchases.length > 0 ? (
        <PurchasedItemsGrid
          items={purchases}
          title="Your Purchased Items"
          emptyMessage="No purchased items found matching your filters."
          onItemAccess={handleItemAccess}
        />
      ) : (
        <Card>
          <CardContent className="p-12">
            <div className="text-center">
              <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Resources Yet
              </h3>
              <p className="text-gray-600 mb-4">
                You haven't purchased any courses or templates yet. Start
                exploring our collection to build your resource library.
              </p>
              <div className="flex gap-3 justify-center">
                <Button asChild>
                  <Link href="/courses">Browse Courses</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/templates">Browse Templates</Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
