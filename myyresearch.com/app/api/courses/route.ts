import { NextResponse } from "next/server";
import {
  getCoursesByCategory,
  getCoursesBySubcategory,
  getPublishedCourses,
} from "@/app/(website)/(home)/_actions/course";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get("category");
    const subcategory = searchParams.get("subcategory");

    let courses;
    if (category) {
      courses = await getCoursesByCategory(category);
    } else if (subcategory) {
      courses = await getCoursesBySubcategory(subcategory);
    } else {
      courses = await getPublishedCourses();
    }

    return NextResponse.json(courses);
  } catch (error) {
    console.error("Error in courses API:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
