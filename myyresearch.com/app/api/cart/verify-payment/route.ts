import { NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-02-24.acacia",
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { sessionId } = body;

    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: "Session ID is required" },
        { status: 400 }
      );
    }

    // Retrieve the session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    // Verify the payment was successful
    if (session.payment_status !== "paid") {
      return NextResponse.json(
        { success: false, error: "Payment not completed" },
        { status: 400 }
      );
    }

    // Get the purchased items from metadata
    const purchasedItems = session.metadata?.items
      ? JSON.parse(session.metadata.items)
      : [];

    // TODO: Update your database to grant access to the templates
    // This could include:
    // 1. Creating purchase records for each template
    // 2. Updating user's purchased templates
    // 3. Recording the transaction
    // 4. Sending confirmation email
    // Example:
    // await Promise.all(purchasedItems.map(async (item) => {
    //   await db.templatePurchases.create({
    //     userId: session.metadata.userId,
    //     templateId: item.id,
    //     transactionId: session.payment_intent as string,
    //     amount: item.price,
    //   });
    // }));

    return NextResponse.json({
      success: true,
      items: purchasedItems,
    });
  } catch (error) {
    console.error("Error verifying payment:", error);
    return NextResponse.json(
      { success: false, error: "Error verifying payment" },
      { status: 500 }
    );
  }
}
