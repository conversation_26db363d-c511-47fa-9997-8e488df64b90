import { NextResponse } from "next/server";
import { getTemplates } from "@/app/(website)/templates/_actions/template";

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get("category");
    const subcategory = searchParams.get("subcategory");

    let result;
    if (category) {
      result = await getTemplates({ categories: [category] });
    } else if (subcategory) {
      result = await getTemplates({ subcategories: [subcategory] });
    } else {
      result = await getTemplates({});
    }

    return NextResponse.json(result.templates);
  } catch (error) {
    console.error("Error in templates API:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
