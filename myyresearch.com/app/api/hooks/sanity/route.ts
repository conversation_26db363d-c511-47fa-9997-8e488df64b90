import { SIGNATURE_HEADER_NAME, isValidSignature } from "@sanity/webhook";
import { NextResponse } from "next/server";
import { revalidatePath, revalidateTag } from "next/cache";
import { SanityTag } from "@/sanity/lib/config";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { _type } = body;

    if (_type === "post") {
      const { slug } = body;
      revalidatePath(`/blog/${slug}`);
    } else if (_type === "webPage") {
      console.log("WEBHOOK SANITY WebPage");
      const { page } = body;
      if (page === SanityTag.HOME_PAGE) {
        console.log("WEBHOOK SANITY WebPage homePage");
        revalidateTag(SanityTag.HOME_PAGE);
      } else if (page === SanityTag.PRIVACY_POLICY_PAGE) {
        console.log("WEBHOOK SANITY WebPage privacyPolicyPage");
        revalidateTag(SanityTag.PRIVACY_POLICY_PAGE);
      } else if (page === SanityTag.TERMS_AND_CONDITIONS_PAGE) {
        console.log("WEBHOOK SANITY WebPage termsAndConditionsPage");
        revalidateTag(SanityTag.TERMS_AND_CONDITIONS_PAGE);
      } else if (page === SanityTag.ABOUT_US_PAGE) {
        console.log("WEBHOOK SANITY WebPage aboutUsPage");
        revalidateTag(SanityTag.ABOUT_US_PAGE);
      } else if (page === SanityTag.CONTACT_PAGE) {
        console.log("WEBHOOK SANITY WebPage contactPage");
        revalidateTag(SanityTag.CONTACT_PAGE);
      } else if (page === SanityTag.GALLERY_PAGE) {
        console.log("WEBHOOK SANITY WebPage gallery");
        revalidateTag(SanityTag.GALLERY_PAGE);
      } else if (page === SanityTag.TEAM) {
        console.log("WEBHOOK SANITY WebPage team");
        revalidateTag(SanityTag.TEAM);
      }
    }

    // console.log("WEBHOOK SANITY BODY", body);
    // const signature = req.headers.get(SIGNATURE_HEADER_NAME)?.toString();
    // console.log("WEBHOOK SANITY SIGNATURE", signature);
    // // revalidateTag("blog");
    // console.log(
    //   "WEBHOOK SANITY isValidSignature",
    //   await isValidSignature(JSON.stringify(body), signature || "", "asanka")
    // );

    // if (
    //   !signature ||
    //   !(await isValidSignature(
    //     JSON.stringify(body),
    //     signature,
    //     env.SANITY_WEBHOOK_SECRET || "secretsecret"
    //   ))
    // ) {
    //   return NextResponse.json(
    //     { message: "Invalid request!" },
    //     { status: 401 }
    //   );
    // }

    // console.log("WEBHOOK SANITY BODY", body);
    // if (_type === "webPage") {
    //   const { page } = body;
    //   if (page === "homePage") {
    //     revalidateTag("homePage");
    //   }
    // } else {
    //   const { basicInfo } = body;
    //   const slug = basicInfo.productSlug.current;
    //   const categorySlug = _type;

    //   revalidatePath(`/product/${slug}`);
    //   revalidatePath(`/category/${categorySlug}`);
    // }

    return NextResponse.json(
      { message: "Product pages revalidated." },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json({ err: "Something went Wrong!" }, { status: 500 });
  }
}
