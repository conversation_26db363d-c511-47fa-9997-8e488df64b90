import { NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-02-24.acacia",
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { sessionId, courseId } = body;

    // Retrieve the session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    // Verify the payment was successful
    if (session.payment_status !== "paid") {
      return NextResponse.json(
        { error: "Payment not completed" },
        { status: 400 }
      );
    }

    // Verify the course ID matches
    if (session.metadata?.courseId !== courseId) {
      return NextResponse.json(
        { error: "Course ID mismatch" },
        { status: 400 }
      );
    }

    // TODO: Update your database to grant access to the course
    // This could include:
    // 1. Creating an enrollment record
    // 2. Updating user's purchased courses
    // 3. Recording the transaction
    // 4. Sending confirmation email
    // Example:
    // await db.enrollments.create({
    //   userId: session.metadata.userId,
    //   courseId: courseId,
    //   transactionId: session.payment_intent as string,
    //   amount: session.amount_total! / 100,
    // });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error verifying payment:", error);
    return NextResponse.json(
      { error: "Error verifying payment" },
      { status: 500 }
    );
  }
}
