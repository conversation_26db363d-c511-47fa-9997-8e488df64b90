import type { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON>, Inter, Nunito, Pop<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { startupImage } from "@/app/startup-image";
import { PostHogPageview, PostHogProvider } from "@/providers/PostHogProvider";
import { GlobalProviders } from "@/providers/GlobalProviders";
import { Layout } from "@/components/craft";
import siteConfig from "@/site.config";
import dbConnect from "@/db/mongoose";
import { AuthProvider } from "@/contexts/auth-contexts";
import { CartProvider } from "@/contexts/cart-context";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`,
  },
  description: siteConfig.description,
  openGraph: {
    title: siteConfig.name,
    description: siteConfig.description,
    url: "https://www.myyresearch.com",
    siteName: siteConfig.name,
    images: [
      {
        url: "https://ck5j7nhrzu.ufs.sh/f/fuHn8dTHgZ7s1NHUXOsNrlAecR7SwTxWFnBdiXohMYVzKgD8",
        width: 1200,
        height: 630,
        alt: siteConfig.name,
      },
    ],
  },
};

export const viewport = {
  themeColor: "#FFF",
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Layout className="h-full">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen`}
      >
        <AuthProvider>
          <GlobalProviders>
            <CartProvider>{children}</CartProvider>
          </GlobalProviders>
        </AuthProvider>
      </body>
    </Layout>
  );
}
