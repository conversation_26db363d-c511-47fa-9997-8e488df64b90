import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsMongoId,
  IsOptional,
  IsPositive,
  Min,
  IsString,
  IsUUID,
} from 'class-validator';
import { CartItemType } from '../schemas/cart.schema';

export class AddToCartDto {
  @ApiProperty({
    description: 'Cart ID',
    example: '550e8400-e29b-41d4-a716-************',
    required: true,
  })
  @IsString()
  @IsUUID()
  cartId: string;

  @ApiProperty({
    description: 'Type of item to add to cart',
    enum: CartItemType,
    example: CartItemType.COURSE,
  })
  @IsEnum(CartItemType)
  itemType: CartItemType;

  @ApiProperty({
    description: 'ID of the course or template',
    example: '507f1f77bcf86cd799439011',
  })
  @IsMongoId()
  itemId: string;

  @ApiProperty({
    description: 'Quantity of items to add',
    example: 1,
    minimum: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @IsPositive()
  @Min(1)
  quantity?: number = 1;

  @ApiProperty({
    description: 'Public ID of the course or template',
    example: 'course-title',
  })
  @IsString()
  publicId: string;
}

export class AddToCartResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Item added to cart successfully' })
  message: string;

  @ApiProperty({
    description: 'Updated cart information',
  })
  cart: any;
}
