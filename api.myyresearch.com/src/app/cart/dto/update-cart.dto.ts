import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsPositive, Min, IsUUID, IsString } from 'class-validator';

export class UpdateCartItemDto {
  @ApiProperty({
    description: 'Cart ID',
    example: '550e8400-e29b-41d4-a716-************',
    required: true,
  })
  @IsString()
  @IsUUID()
  cartId: string;

  @ApiProperty({
    description: 'ID of the cart item to update',
    example: '507f1f77bcf86cd799439011',
  })
  @IsMongoId()
  itemId: string;

  @ApiProperty({
    description: 'New quantity for the cart item',
    example: 2,
    minimum: 1,
  })
  @IsPositive()
  @Min(1)
  quantity: number;
}

export class UpdateCartResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Cart updated successfully' })
  message: string;

  @ApiProperty({
    description: 'Updated cart information',
  })
  cart: any;
}
