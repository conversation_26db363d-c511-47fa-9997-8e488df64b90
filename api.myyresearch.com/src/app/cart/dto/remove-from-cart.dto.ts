import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsString, IsUUID } from 'class-validator';

export class RemoveFromCartDto {
  @ApiProperty({
    description: 'Cart ID',
    example: '550e8400-e29b-41d4-a716-************',
    required: true,
  })
  @IsString()
  @IsUUID()
  cartId: string;

  @ApiProperty({
    description: 'ID of the cart item to remove',
    example: '507f1f77bcf86cd799439011',
  })
  @IsMongoId()
  itemId: string;
}

export class RemoveFromCartResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Item removed from cart successfully' })
  message: string;

  @ApiProperty({
    description: 'Updated cart information',
  })
  cart: any;
}

export class ClearCartDto {
  @ApiProperty({
    description: 'Cart ID',
    example: '550e8400-e29b-41d4-a716-************',
    required: true,
  })
  @IsString()
  @IsUUID()
  cartId: string;
}

export class ClearCartResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Cart cleared successfully' })
  message: string;
}
