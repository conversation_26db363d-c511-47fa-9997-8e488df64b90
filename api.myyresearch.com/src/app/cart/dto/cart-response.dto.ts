import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUUID } from 'class-validator';
import { CartItemType } from '../schemas/cart.schema';

export class GetCartDto {
  @ApiProperty({
    description: 'Cart ID',
    example: '550e8400-e29b-41d4-a716-************',
    required: true,
  })
  @IsString()
  @IsUUID()
  cartId: string;
}

export class CartItemDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  _id: string;

  @ApiProperty({ enum: CartItemType, example: CartItemType.COURSE })
  itemType: CartItemType;

  @ApiProperty({ example: '507f1f77bcf86cd799439012' })
  itemId: string;

  @ApiProperty({ example: 1 })
  quantity: number;

  @ApiProperty({ example: 99.99 })
  price: number;

  @ApiProperty({ example: 79.99, required: false })
  discountPrice?: number;

  @ApiProperty({ example: 'Complete Web Development Course' })
  title: string;

  @ApiProperty({
    example: 'https://example.com/thumbnail.jpg',
    required: false,
  })
  thumbnail?: string;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  addedAt: Date;
}

export class CartDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439013' })
  _id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    required: true,
  })
  cartId: string;

  @ApiProperty({ type: [CartItemDto] })
  items: CartItemDto[];

  @ApiProperty({ example: 179.98 })
  totalAmount: number;

  @ApiProperty({ example: 2 })
  totalItems: number;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  lastUpdated: Date;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  createdAt: Date;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  updatedAt: Date;
}

export class GetCartResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Cart retrieved successfully' })
  message: string;

  @ApiProperty({ type: CartDto })
  cart: CartDto;
}
