import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Cart,
  CartDocument,
  CartItemType,
  DocumentStatus,
} from './schemas/cart.schema';
import { Course, CourseDocument } from '../courses/schemas/course.schema';
import {
  Template,
  TemplateDocument,
} from '../templates/schemas/template.schema';
import { AddToCartDto, AddToCartResponseDto } from './dto/add-to-cart.dto';
import {
  UpdateCartItemDto,
  UpdateCartResponseDto,
} from './dto/update-cart.dto';
import {
  RemoveFromCartDto,
  RemoveFromCartResponseDto,
  ClearCartResponseDto,
} from './dto/remove-from-cart.dto';
import { GetCartResponseDto, CartDto } from './dto/cart-response.dto';

@Injectable()
export class CartService {
  constructor(
    @InjectModel(Cart.name) private cartModel: Model<CartDocument>,
    @InjectModel(Course.name) private courseModel: Model<CourseDocument>,
    @InjectModel(Template.name) private templateModel: Model<TemplateDocument>,
  ) {}

  // Utility method to find cart by cart ID
  private async findCart(cartId: string): Promise<CartDocument | null> {
    return this.cartModel
      .findOne({
        cartId,
        documentStatus: DocumentStatus.ACTIVE,
      })
      .exec();
  }

  // Utility method to format cart document to DTO
  private formatCartToDto(cart: CartDocument): CartDto {
    return {
      _id: cart._id.toString(),
      cartId: cart.cartId,
      items: cart.items.map((item) => ({
        _id: item._id,
        itemType: item.itemType,
        itemId: item.itemId.toString(),
        quantity: item.quantity,
        price: item.price,
        discountPrice: item.discountPrice,
        title: item.title,
        thumbnail: item.thumbnail,
        addedAt: item.addedAt,
      })),
      totalAmount: cart.totalAmount,
      totalItems: cart.totalItems,
      lastUpdated: cart.lastUpdated,
      createdAt: (cart as any).createdAt || new Date(),
      updatedAt: (cart as any).updatedAt || new Date(),
    };
  }

  async getCart(cartId: string): Promise<GetCartResponseDto> {
    if (!cartId) {
      throw new BadRequestException('cartId must be provided');
    }

    let cart = await this.findCart(cartId);

    if (!cart) {
      // Create new cart
      const newCartData = {
        cartId,
        items: [],
        totalAmount: 0,
        totalItems: 0,
        lastUpdated: new Date(),
      };

      cart = await this.cartModel.create(newCartData);
    }

    const cartDto = this.formatCartToDto(cart);

    return {
      success: true,
      message: 'Cart retrieved successfully',
      cart: cartDto,
    };
  }

  async addToCart(addToCartDto: AddToCartDto): Promise<AddToCartResponseDto> {
    const { itemType, itemId, quantity = 1, cartId } = addToCartDto;

    if (!cartId) {
      throw new BadRequestException('cartId must be provided');
    }

    // Get item details
    const itemDetails = await this.getItemDetails(itemType, itemId);

    // Get or create cart
    let cart = await this.findCart(cartId);

    if (!cart) {
      const newCartData = {
        cartId,
        items: [],
        totalAmount: 0,
        totalItems: 0,
        lastUpdated: new Date(),
      };

      cart = new this.cartModel(newCartData);
    }

    // Check if item already exists in cart
    const existingItemIndex = cart.items.findIndex(
      (item) => item.itemType === itemType && item.itemId.toString() === itemId,
    );

    if (existingItemIndex >= 0) {
      // Update quantity
      cart.items[existingItemIndex].quantity += quantity;
    } else {
      // Add new item
      const newItem = {
        _id: new Date().getTime().toString(),
        itemType,
        itemId: itemId as any,
        quantity,
        price: itemDetails.price,
        discountPrice: itemDetails.discountPrice,
        title: itemDetails.title,
        thumbnail: itemDetails.thumbnail,
        addedAt: new Date(),
      };
      cart.items.push(newItem);
    }

    // Recalculate totals
    this.recalculateCartTotals(cart);
    cart.lastUpdated = new Date();

    await cart.save();

    return {
      success: true,
      message: 'Item added to cart successfully',
      cart: this.formatCartToDto(cart),
    };
  }

  async updateCartItem(
    updateCartDto: UpdateCartItemDto,
  ): Promise<UpdateCartResponseDto> {
    const { itemId, quantity, cartId } = updateCartDto;

    if (!cartId) {
      throw new BadRequestException('cartId must be provided');
    }

    const cart = await this.findCart(cartId);

    if (!cart) {
      throw new NotFoundException('Cart not found');
    }

    const itemIndex = cart.items.findIndex((item) => item._id === itemId);
    if (itemIndex === -1) {
      throw new NotFoundException('Item not found in cart');
    }

    cart.items[itemIndex].quantity = quantity;
    this.recalculateCartTotals(cart);
    cart.lastUpdated = new Date();

    await cart.save();

    return {
      success: true,
      message: 'Cart updated successfully',
      cart: this.formatCartToDto(cart),
    };
  }

  async removeFromCart(
    removeFromCartDto: RemoveFromCartDto,
  ): Promise<RemoveFromCartResponseDto> {
    const { itemId, cartId } = removeFromCartDto;

    if (!cartId) {
      throw new BadRequestException('cartId must be provided');
    }

    const cart = await this.findCart(cartId);

    if (!cart) {
      throw new NotFoundException('Cart not found');
    }

    const itemIndex = cart.items.findIndex((item) => item._id === itemId);
    if (itemIndex === -1) {
      throw new NotFoundException('Item not found in cart');
    }

    cart.items.splice(itemIndex, 1);
    this.recalculateCartTotals(cart);
    cart.lastUpdated = new Date();

    await cart.save();

    return {
      success: true,
      message: 'Item removed from cart successfully',
      cart: this.formatCartToDto(cart),
    };
  }

  async clearCart(cartId: string): Promise<ClearCartResponseDto> {
    if (!cartId) {
      throw new BadRequestException('cartId must be provided');
    }

    await this.cartModel.findOneAndUpdate(
      { cartId, documentStatus: DocumentStatus.ACTIVE },
      {
        items: [],
        totalAmount: 0,
        totalItems: 0,
        lastUpdated: new Date(),
      },
      { upsert: true },
    );

    return {
      success: true,
      message: 'Cart cleared successfully',
    };
  }

  private async getItemDetails(itemType: CartItemType, itemId: string) {
    if (itemType === CartItemType.COURSE) {
      const course = await this.courseModel.findById(itemId).lean();
      if (!course) {
        throw new NotFoundException('Course not found');
      }
      return {
        price: course.price,
        discountPrice: course.discountPrice,
        title: course.title,
        thumbnail: course.thumbnail?.key,
      };
    } else if (itemType === CartItemType.TEMPLATE) {
      const template = await this.templateModel.findById(itemId).lean();
      if (!template) {
        throw new NotFoundException('Template not found');
      }
      return {
        price: template.price,
        discountPrice: template.discountPrice,
        title: template.title,
        thumbnail: template.thumbnail?.key,
      };
    }
    throw new BadRequestException('Invalid item type');
  }

  private recalculateCartTotals(cart: CartDocument): void {
    cart.totalItems = cart.items.reduce(
      (total, item) => total + item.quantity,
      0,
    );
    cart.totalAmount = cart.items.reduce((total, item) => {
      const price = item.discountPrice || item.price;
      return total + price * item.quantity;
    }, 0);
  }
}
