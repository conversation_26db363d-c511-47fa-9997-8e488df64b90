import { Injectable, ExecutionContext, CanActivate } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@Injectable()
export class OptionalAuthGuard implements CanActivate {
  private jwtAuthGuard = new JwtAuthGuard();

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    // If no auth header, allow the request but set user to null
    if (!authHeader) {
      request.user = null;
      return true;
    }

    try {
      // Try to authenticate using the JWT guard
      const result = await this.jwtAuthGuard.canActivate(context);
      return result as boolean;
    } catch {
      // If authentication fails, allow request but set user to null
      request.user = null;
      return true;
    }
  }
}
