import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type CartDocument = Cart & Document;

export enum CartItemType {
  COURSE = 'course',
  TEMPLATE = 'template',
}

export enum DocumentStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

@Schema()
export class CartItem {
  @Prop({
    required: true,
    default: () => new MongooseSchema.Types.ObjectId('000000000000').toString(),
  })
  _id: string;

  @Prop({ type: String, enum: CartItemType, required: true })
  itemType: CartItemType;

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true })
  itemId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, min: 1, default: 1 })
  quantity: number;

  @Prop({ required: true })
  price: number;

  @Prop()
  discountPrice?: number;

  @Prop({ required: true })
  title: string;

  @Prop()
  thumbnail?: string;

  @Prop({ type: Date, default: Date.now })
  addedAt: Date;
}

@Schema({ timestamps: true })
export class Cart {
  @Prop({ type: String, unique: true, required: true })
  cartId: string;

  @Prop({ type: [CartItem], default: [] })
  items: CartItem[];

  @Prop({ required: true, default: 0 })
  totalAmount: number;

  @Prop({ required: true, default: 0 })
  totalItems: number;

  @Prop({ type: String, enum: DocumentStatus, default: DocumentStatus.ACTIVE })
  documentStatus: DocumentStatus;

  @Prop({ type: Date })
  lastUpdated: Date;
}

export const CartSchema = SchemaFactory.createForClass(Cart);

// Create index for cartId and document status
CartSchema.index({ cartId: 1, documentStatus: 1 });

// Ensure cartId is present
CartSchema.pre('save', function (next) {
  if (!this.cartId) {
    const error = new Error('cartId must be provided');
    return next(error);
  }
  next();
});
