import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CartService } from './cart.service';

import { AddToCartDto, AddToCartResponseDto } from './dto/add-to-cart.dto';
import {
  UpdateCartItemDto,
  UpdateCartResponseDto,
} from './dto/update-cart.dto';
import {
  RemoveFromCartDto,
  RemoveFromCartResponseDto,
  ClearCartResponseDto,
  ClearCartDto,
} from './dto/remove-from-cart.dto';
import { GetCartResponseDto, GetCartDto } from './dto/cart-response.dto';

@ApiTags('Cart')
@Controller('cart')
export class CartController {
  constructor(private readonly cartService: CartService) {}

  @Get()
  @ApiOperation({
    summary: 'Get cart',
    description: 'Get cart by cart ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Cart retrieved successfully',
    type: GetCartResponseDto,
  })
  async getCart(@Query() getCartDto: GetCartDto): Promise<GetCartResponseDto> {
    const { cartId } = getCartDto;
    return this.cartService.getCart(cartId);
  }

  @Post('add')
  @ApiOperation({
    summary: 'Add item to cart',
    description: 'Add item to cart by cart ID',
  })
  @ApiResponse({
    status: 201,
    description: 'Item added to cart successfully',
    type: AddToCartResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Course or template not found',
  })
  async addToCart(
    @Body() addToCartDto: AddToCartDto,
  ): Promise<AddToCartResponseDto> {
    return this.cartService.addToCart(addToCartDto);
  }

  @Put('update')
  @ApiOperation({
    summary: 'Update cart item quantity',
    description: 'Update cart item quantity by cart ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Cart updated successfully',
    type: UpdateCartResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Cart or item not found',
  })
  async updateCartItem(
    @Body() updateCartDto: UpdateCartItemDto,
  ): Promise<UpdateCartResponseDto> {
    return this.cartService.updateCartItem(updateCartDto);
  }

  @Delete('remove')
  @ApiOperation({
    summary: 'Remove item from cart',
    description: 'Remove item from cart by cart ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Item removed from cart successfully',
    type: RemoveFromCartResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Cart or item not found',
  })
  async removeFromCart(
    @Body() removeFromCartDto: RemoveFromCartDto,
  ): Promise<RemoveFromCartResponseDto> {
    return this.cartService.removeFromCart(removeFromCartDto);
  }

  @Delete('clear')
  @ApiOperation({
    summary: 'Clear entire cart',
    description: 'Clear entire cart by cart ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Cart cleared successfully',
    type: ClearCartResponseDto,
  })
  async clearCart(
    @Body() clearCartDto: ClearCartDto,
  ): Promise<ClearCartResponseDto> {
    const { cartId } = clearCartDto;
    return this.cartService.clearCart(cartId);
  }
}
