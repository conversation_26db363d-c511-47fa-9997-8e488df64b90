import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { TemplatesService } from './templates.service';
import { Roles } from '../users/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import { ApproveTemplateDto } from './dto/approve-template.dto';
import { UpdatePointsDto } from '../courses/dto/update-points.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { GetAllTemplatesResponseDto } from './dto/get-all-templates-response.dto';
import { TemplateFilterDto } from './dto/template-filter.dto';
import { CategoryFilterDto } from './dto/category-filter.dto';
import { CategoryTemplatesResponseDto } from './dto/category-templates-response.dto';
import { PublicTemplateResponseDto } from './dto/public-template-response.dto';
import { FilteredTemplatesResponseDto } from './dto/filtered-templates-response.dto';
import { TemplateFiltersDto } from './dto/template-filters.dto';
import { JwtAuthGuard } from '@app/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@app/auth/guards/roles.guard';
import { Public } from '@app/auth/decorators/public.decorator';
import { OptionalAuthGuard } from '../cart/guards/optional-auth.guard';
import { GetUser } from '../auth/decorators/get-user.decorator';

@ApiTags('templates')
@Controller('templates')
export class TemplatesController {
  constructor(private readonly templatesService: TemplatesService) {}

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all templates (Admin, Staff, Instructor)' })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered templates with pagination',
    type: GetAllTemplatesResponseDto,
  })
  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.STAFF, UserRole.INSTRUCTOR)
  findAllTemplates(
    @Query() filterDto: TemplateFilterDto,
    @Request() req,
  ): Promise<GetAllTemplatesResponseDto> {
    return this.templatesService.findAllWithAuth(
      {
        userId: req.user._id,
        role: req.user.role,
      },
      filterDto,
    );
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  findOne(@Param('id') id: string, @Request() req) {
    return this.templatesService.findOneWithAccess(id, req.user);
  }

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.INSTRUCTOR)
  create(@Body() createTemplateDto: CreateTemplateDto, @Request() req) {
    return this.templatesService.createWithType(createTemplateDto, req.user);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.STAFF, UserRole.INSTRUCTOR)
  update(
    @Param('id') id: string,
    @Body() updateTemplateDto: UpdateTemplateDto,
    @Request() req,
  ) {
    return this.templatesService.updateWithAccess(
      id,
      updateTemplateDto,
      req.user,
    );
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.STAFF, UserRole.INSTRUCTOR)
  @ApiOperation({ summary: 'Delete template (Admin, Staff, Instructor)' })
  @ApiResponse({
    status: 200,
    description: 'Template deleted successfully (soft delete)',
  })
  remove(@Param('id') id: string, @Request() req) {
    return this.templatesService.removeWithAuth(id, {
      userId: req.user._id,
      role: req.user.role,
    });
  }

  @Patch(':id/restore')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  restore(@Param('id') id: string) {
    return this.templatesService.restore(id);
  }

  @Patch(':id/archive')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  archive(@Param('id') id: string) {
    return this.templatesService.archive(id);
  }

  @Patch(':id/approve')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  approveTemplate(
    @Param('id') id: string,
    @Body() approveTemplateDto: ApproveTemplateDto,
    @Request() req,
  ) {
    return this.templatesService.approveTemplate(
      id,
      approveTemplateDto,
      req.user._id,
    );
  }

  @Patch(':id/points')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update template points (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Template points updated successfully',
  })
  updatePoints(
    @Param('id') id: string,
    @Body() updatePointsDto: UpdatePointsDto,
    @Request() req,
  ) {
    return this.templatesService.updatePointsWithAuth(
      id,
      updatePointsDto.points,
      {
        userId: req.user._id,
        role: req.user.role,
      },
    );
  }

  @Get('public/by-category')
  @ApiOperation({
    summary: 'Get all public templates by category with pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns templates filtered by category with pagination',
    type: CategoryTemplatesResponseDto,
  })
  @Public()
  async findByCategory(
    @Query() categoryFilterDto: CategoryFilterDto,
  ): Promise<CategoryTemplatesResponseDto> {
    return this.templatesService.findByCategory(categoryFilterDto);
  }

  @Get('public/filter')
  @ApiOperation({
    summary: 'Get all public templates with multiple filters and pagination',
    description:
      'Filter templates by categories, subcategories, and media types',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered templates with pagination',
    type: FilteredTemplatesResponseDto,
  })
  @Public()
  async findAllFiltered(
    @Query() filterDto: TemplateFiltersDto,
  ): Promise<FilteredTemplatesResponseDto> {
    return this.templatesService.findAllFiltered(filterDto);
  }

  @UseGuards(OptionalAuthGuard)
  @Get('public/:publicId')
  @ApiOperation({
    summary: 'Get public template details by publicId',
    description:
      'Returns template details including reviews and purchase status if authenticated',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns template details with reviews and purchase status',
    type: PublicTemplateResponseDto,
  })
  async findByPublicId(
    @Param('publicId') publicId: string,
    @GetUser() user?: any,
  ): Promise<PublicTemplateResponseDto> {
    return this.templatesService.findByPublicId(publicId, user?._id);
  }
}
