import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Template,
  TemplateDocument,
  CreatorType,
  DocumentStatus,
  UploadedFile,
  TemplateStatus,
} from './schemas/template.schema';
import { UserRole, User } from '../users/schemas/user.schema';
import { Review, ReviewStatus } from '../reviews/schemas/review.schema';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import {
  ApproveTemplateDto,
  TemplateApprovalStatus,
} from './dto/approve-template.dto';
import { TemplateFilterDto } from './dto/template-filter.dto';
import { GetAllTemplatesResponseDto } from './dto/get-all-templates-response.dto';
import { CategoryFilterDto } from './dto/category-filter.dto';
import { MinimalisticTemplateDto } from './dto/minimalistic-template.dto';
import { CategoryTemplatesResponseDto } from './dto/category-templates-response.dto';
import { PublicTemplateResponseDto } from './dto/public-template-response.dto';
import { FilteredTemplatesResponseDto } from './dto/filtered-templates-response.dto';
import { TemplateFiltersDto } from './dto/template-filters.dto';
import { OrdersService } from '../orders/orders.service';
import { OrderItemType } from '../orders/schemas/order.schema';

interface RequestUser {
  _id: string;
  role: UserRole;
}

@Injectable()
export class TemplatesService {
  constructor(
    @InjectModel(Template.name)
    private readonly templateModel: Model<TemplateDocument>,
    @InjectModel(User.name)
    private readonly userModel: Model<User>,
    @InjectModel(Review.name)
    private readonly reviewModel: Model<Review>,

    private ordersService: OrdersService,
  ) {}

  private addUploadedAtToFile(file: Partial<UploadedFile>): UploadedFile {
    return {
      ...file,
      uploadedAt: new Date(),
    } as UploadedFile;
  }

  async create(
    createTemplateDto: Partial<Template> & { createdBy: any },
  ): Promise<Template> {
    const createdTemplate = new this.templateModel({
      ...createTemplateDto,
      documentStatus: DocumentStatus.ACTIVE,
    });
    return createdTemplate.save();
  }

  async findAllForUser(
    user: RequestUser | null,
    status?: DocumentStatus,
  ): Promise<Template[]> {
    // Public access - only active templates
    if (!user) {
      return this.findAllWithStatus(DocumentStatus.ACTIVE);
    }

    // Admin access - all templates with optional status filter
    if (user.role === UserRole.ADMIN) {
      if (status) {
        return this.findAllWithStatus(status);
      }
      return this.findAll();
    }

    // Instructor access - own templates + active templates
    if (user.role === UserRole.INSTRUCTOR) {
      const [activeTemplates, ownTemplates] = await Promise.all([
        this.findAllWithStatus(DocumentStatus.ACTIVE),
        this.findByCreator(user._id),
      ]);
      return [...activeTemplates, ...ownTemplates];
    }

    // Regular user access - only active templates
    return this.findAllWithStatus(DocumentStatus.ACTIVE);
  }

  async findAllTemplates(status?: DocumentStatus): Promise<Template[]> {
    if (status) {
      return this.findAllWithStatus(status);
    }
    return this.templateModel.find().exec();
  }

  async findOneWithAccess(
    id: string,
    user: RequestUser | null,
  ): Promise<Template> {
    const template = await this.findOne(id);

    // Public access check - only active templates
    if (template.documentStatus === DocumentStatus.ACTIVE) {
      return template;
    }

    // Authentication check
    if (!user) {
      throw new ForbiddenException('Authentication required');
    }

    // Admin access check
    if (user.role === UserRole.ADMIN) {
      return template;
    }

    // Instructor access check - own templates only
    if (user.role === UserRole.INSTRUCTOR) {
      if (template.createdBy.toString() === user._id) {
        return template;
      }
      throw new ForbiddenException('You can only access your own templates');
    }

    throw new ForbiddenException('Access denied');
  }

  async updateWithAccess(
    id: string,
    updateTemplateDto: UpdateTemplateDto,
    user: RequestUser,
  ): Promise<Template> {
    const template = await this.findOne(id);

    // Process file uploads if present
    const processedDto = {
      ...updateTemplateDto,
      ...(updateTemplateDto.thumbnail && {
        thumbnail: this.addUploadedAtToFile(updateTemplateDto.thumbnail),
      }),
      ...(updateTemplateDto.downloadMedia && {
        downloadMedia: this.addUploadedAtToFile(
          updateTemplateDto.downloadMedia,
        ),
      }),
      ...(updateTemplateDto.previewMedia && {
        previewMedia: this.addUploadedAtToFile(updateTemplateDto.previewMedia),
      }),
      ...(updateTemplateDto.helpMedia && {
        helpMedia: this.addUploadedAtToFile(updateTemplateDto.helpMedia),
      }),
    };

    // Admin can update any template
    if (user.role === UserRole.ADMIN || user.role === UserRole.STAFF) {
      return this.update(id, {
        ...processedDto,
        updatedBy: user._id,
      });
    }

    // Instructors can only update their own templates
    if (template.createdBy.toString() !== user._id) {
      throw new ForbiddenException('You can only update your own templates');
    }

    return this.update(id, {
      ...processedDto,
      updatedBy: user._id,
    });
  }

  async removeWithAccess(id: string, user: RequestUser): Promise<Template> {
    const template = await this.findOne(id);

    // Admin can delete any template
    if (user.role === UserRole.ADMIN) {
      return this.remove(id);
    }

    // Instructors can only delete their own templates
    if (template.createdBy.toString() !== user._id) {
      throw new ForbiddenException('You can only delete your own templates');
    }

    return this.remove(id);
  }

  async createWithType(
    createTemplateDto: CreateTemplateDto,
    user: RequestUser,
  ): Promise<Template> {
    const creatorType =
      user.role === UserRole.ADMIN || user.role === UserRole.STAFF
        ? CreatorType.MYRESEARCH
        : CreatorType.INSTRUCTOR;

    // Process file uploads
    const processedDto = {
      ...createTemplateDto,
      thumbnail: this.addUploadedAtToFile(createTemplateDto.thumbnail),
      downloadMedia: this.addUploadedAtToFile(createTemplateDto.downloadMedia),
      ...(createTemplateDto.previewMedia && {
        previewMedia: this.addUploadedAtToFile(createTemplateDto.previewMedia),
      }),
      ...(createTemplateDto.helpMedia && {
        helpMedia: this.addUploadedAtToFile(createTemplateDto.helpMedia),
      }),
    };

    console.log('processedDto', processedDto);

    return this.create({
      ...processedDto,
      creatorType,
      createdBy: user._id,
      updatedBy: user._id,
    });
  }

  private async findAll(creatorType?: CreatorType): Promise<Template[]> {
    const query: any = { documentStatus: DocumentStatus.ACTIVE };
    if (creatorType) {
      query.creatorType = creatorType;
    }
    return this.templateModel.find(query).exec();
  }

  private async findOne(id: string): Promise<Template> {
    const template = await this.templateModel
      .findOne({
        _id: id,
        documentStatus: DocumentStatus.ACTIVE,
      })
      .exec();

    if (!template) {
      throw new NotFoundException(`Template with ID ${id} not found`);
    }
    return template;
  }

  private async update(
    id: string,
    updateTemplateDto: Partial<Template>,
  ): Promise<Template> {
    // First get the existing template
    const existingTemplate = await this.templateModel
      .findOne({
        _id: id,
        documentStatus: DocumentStatus.ACTIVE,
      })
      .exec();

    if (!existingTemplate) {
      throw new NotFoundException(`Template with ID ${id} not found`);
    }

    // Check if we're changing from published to draft
    if (
      existingTemplate.status === TemplateStatus.PUBLISHED &&
      updateTemplateDto.status === TemplateStatus.DRAFT
    ) {
      // Create an archived copy of the published version
      const archivedTemplate = new this.templateModel({
        ...existingTemplate.toObject(),
        _id: undefined, // Let MongoDB generate a new ID
        originalId: existingTemplate._id,
        publicId: null,
      });
      await archivedTemplate.save();
    }

    // Proceed with the update
    const updatedTemplate = await this.templateModel
      .findOneAndUpdate(
        { _id: id, documentStatus: DocumentStatus.ACTIVE },
        updateTemplateDto,
        { new: true },
      )
      .exec();

    if (!updatedTemplate) {
      throw new NotFoundException(`Template with ID ${id} not found`);
    }

    return updatedTemplate;
  }

  private async remove(id: string): Promise<Template> {
    const deletedTemplate = await this.templateModel
      .findOneAndUpdate(
        { _id: id, documentStatus: DocumentStatus.ACTIVE },
        { documentStatus: DocumentStatus.ARCHIVED },
        { new: true },
      )
      .exec();

    if (!deletedTemplate) {
      throw new NotFoundException(`Template with ID ${id} not found`);
    }
    return deletedTemplate;
  }

  private async findByCreator(creatorId: string): Promise<Template[]> {
    return this.templateModel
      .find({
        createdBy: creatorId,
        documentStatus: DocumentStatus.ACTIVE,
      })
      .exec();
  }

  private async findAllWithStatus(status: DocumentStatus): Promise<Template[]> {
    return this.templateModel.find({ documentStatus: status }).exec();
  }

  // Admin-only operations
  async restore(id: string): Promise<Template> {
    const restoredTemplate = await this.templateModel
      .findOneAndUpdate(
        { _id: id, documentStatus: DocumentStatus.ARCHIVED },
        { documentStatus: DocumentStatus.ACTIVE },
        { new: true },
      )
      .exec();

    if (!restoredTemplate) {
      throw new NotFoundException(
        `Template with ID ${id} not found or not in deleted status`,
      );
    }
    return restoredTemplate;
  }

  async archive(id: string): Promise<Template> {
    const archivedTemplate = await this.templateModel
      .findOneAndUpdate(
        { _id: id, documentStatus: DocumentStatus.ACTIVE },
        { documentStatus: DocumentStatus.ARCHIVED },
        { new: true },
      )
      .exec();

    if (!archivedTemplate) {
      throw new NotFoundException(`Template with ID ${id} not found`);
    }
    return archivedTemplate;
  }

  async approveTemplate(
    id: string,
    approveTemplateDto: ApproveTemplateDto,
    userId: string,
  ): Promise<Template> {
    const template = await this.templateModel.findOne({
      _id: id,
      documentStatus: 'active',
    });

    if (!template) {
      throw new NotFoundException('Template not found');
    }

    // if (template.status !== TemplateStatus.PENDING) {
    //   throw new BadRequestException('Template is not in pending status');
    // }

    const updatedTemplate = await this.templateModel.findByIdAndUpdate(
      id,
      {
        $set: {
          approvalStatus: approveTemplateDto.status,
          approvalComment: approveTemplateDto.comment,
          approvedBy: userId,
          approvalDate: new Date(),
          status:
            approveTemplateDto.status === TemplateApprovalStatus.APPROVED
              ? TemplateStatus.PUBLISHED
              : TemplateStatus.DRAFT,
        },
      },
      { new: true },
    );

    if (!updatedTemplate) {
      throw new NotFoundException('Template not found');
    }

    return updatedTemplate;
  }

  async findAllWithAuth(
    auth: { userId: string; role: UserRole },
    filterDto: TemplateFilterDto,
  ): Promise<GetAllTemplatesResponseDto> {
    const page = parseInt(filterDto.page) || 1;
    const limit = parseInt(filterDto.limit) || 10;
    const skip = (page - 1) * limit;

    const query: any = {};

    // If user is instructor, only return their courses
    if (auth.role === UserRole.INSTRUCTOR) {
      query['createdBy'] = auth.userId;
    }

    // Only return templates with non-null publicId
    query.publicId = { $ne: null };

    // Apply filters
    if (filterDto.search) {
      query.$or = [
        { title: { $regex: filterDto.search, $options: 'i' } },
        { shortDescription: { $regex: filterDto.search, $options: 'i' } },
      ];
    }

    if (filterDto.category) {
      query.category = filterDto.category;
    }

    if (filterDto.subcategory) {
      query.subcategory = filterDto.subcategory;
    }

    if (filterDto.mediaType) {
      query.mediaType = filterDto.mediaType;
    }

    if (filterDto.status) {
      query.status = filterDto.status;
    }

    if (filterDto.documentStatus) {
      query.documentStatus = filterDto.documentStatus;
    } else {
      query.documentStatus = DocumentStatus.ACTIVE;
    }

    // Apply role-based filtering
    if (auth.role === UserRole.INSTRUCTOR) {
      query.$or = [
        { documentStatus: DocumentStatus.ACTIVE },
        { createdBy: auth.userId },
      ];
    }

    const [templates, total] = await Promise.all([
      this.templateModel
        .find(query)
        .select({
          _id: 1,
          title: 1,
          publicId: 1,
          shortDescription: 1,
          thumbnail: 1,
          mediaType: 1,
          category: 1,
          subcategory: 1,
          tag: 1,
          price: 1,
          discountPrice: 1,
          downloadCount: 1,
          free: 1,
          status: 1,
          approvalStatus: 1,
          createdAt: 1,
          updatedAt: 1,
          creatorType: 1,
          createdBy: 1,
          averageReview: 1,
          reviewCount: 1,
        })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean()
        .exec(),
      this.templateModel.countDocuments(query),
    ]);

    // Get instructor usernames for templates created by instructors
    const instructorTemplates = templates.filter(
      (template) => template.creatorType === CreatorType.INSTRUCTOR,
    );
    const instructorIds = instructorTemplates.map(
      (template) => template.createdBy,
    );
    const instructors = await this.userModel
      .find({ _id: { $in: instructorIds } })
      .select('username')
      .lean()
      .exec();

    const instructorMap = new Map(
      instructors.map((instructor) => [
        instructor._id.toString(),
        instructor.username,
      ]),
    );

    const templatesWithInstructors = templates.map((template: any) => ({
      _id: template._id.toString(),
      title: template.title,
      publicId: template.publicId,
      shortDescription: template.shortDescription,
      thumbnail: template.thumbnail.key,
      mediaType: template.mediaType,
      category: template.category,
      subcategory: template.subcategory,
      tag: template.tag,
      price: template.price,
      discountPrice: template.discountPrice,
      downloadCount: template.downloadCount,
      free: template.free,
      status: template.status,
      approvalStatus: template.approvalStatus,
      createdAt: new Date(template.createdAt),
      updatedAt: new Date(template.updatedAt),
      creatorType: template.creatorType,
      instructorUsername:
        template.creatorType === CreatorType.INSTRUCTOR
          ? instructorMap.get(template.createdBy.toString())
          : undefined,
      averageReview: template.averageReview || 0,
      reviewCount: template.reviewCount || 0,
    }));

    return {
      templates: templatesWithInstructors,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async updatePointsWithAuth(
    id: string,
    points: number,
    auth: { userId: string; role: UserRole },
  ) {
    if (auth.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can update template points');
    }

    const updatedTemplate = await this.templateModel.findByIdAndUpdate(
      id,
      {
        points,
        updatedBy: auth.userId,
      },
      { new: true, select: 'points' },
    );

    if (!updatedTemplate) {
      throw new NotFoundException(`Template with ID ${id} not found`);
    }

    return updatedTemplate;
  }

  async removeWithAuth(
    id: string,
    auth: { userId: string; role: UserRole },
  ): Promise<Template> {
    const template = await this.findOne(id);

    // Admin can delete any template
    if (auth.role === UserRole.ADMIN) {
      return this.remove(id);
    }

    // Staff can delete any template
    if (auth.role === UserRole.STAFF) {
      return this.remove(id);
    }

    // Instructors can only delete their own templates
    if (template.createdBy.toString() !== auth.userId) {
      throw new ForbiddenException('You can only delete your own templates');
    }

    return this.remove(id);
  }

  async findByCategory(
    categoryFilterDto: CategoryFilterDto,
  ): Promise<CategoryTemplatesResponseDto> {
    const {
      category,
      subcategory,
      mediaType,
      page = 1,
      limit = 10,
    } = categoryFilterDto;
    const skip = (page - 1) * limit;

    // Build the base query with approval logic
    const baseQuery = {
      documentStatus: DocumentStatus.ACTIVE,
      category,
      status: TemplateStatus.PUBLISHED,
      $or: [
        // Always show items with creatorType 'myyresearch'
        { creatorType: CreatorType.MYRESEARCH },
        // For other creatorTypes, only show if approved
        {
          creatorType: { $ne: CreatorType.MYRESEARCH },
          approvalStatus: TemplateApprovalStatus.APPROVED,
        },
      ],
    };

    // Add subcategory if provided
    if (subcategory) {
      baseQuery['subcategory'] = subcategory;
    }

    // Add mediaType if provided
    if (mediaType) {
      baseQuery['mediaType'] = mediaType;
    }

    // Find all templates matching the criteria with only required fields
    const [templates, total] = await Promise.all([
      this.templateModel
        .find(baseQuery)
        .select({
          _id: 1,
          title: 1,
          publicId: 1,
          shortDescription: 1,
          thumbnail: 1,
          mediaType: 1,
          category: 1,
          subcategory: 1,
          tag: 1,
          price: 1,
          discountPrice: 1,
          downloadCount: 1,
          free: 1,
          status: 1,
          approvalStatus: 1,
          createdAt: 1,
          updatedAt: 1,
          creatorType: 1,
          instructorUsername: 1,
          averageReview: 1,
          reviewCount: 1,
        })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean()
        .exec(),
      this.templateModel.countDocuments(baseQuery),
    ]);

    const processedTemplates = templates.map((template) =>
      this.mapToMinimalisticDto(template),
    );
    const pages = Math.ceil(total / limit);

    return {
      data: processedTemplates,
      total,
      pages,
    };
  }

  private mapToMinimalisticDto(template: any): MinimalisticTemplateDto {
    return {
      _id: template._id,
      title: template.title,
      publicId: template.publicId,
      shortDescription: template.shortDescription,
      thumbnail: template.thumbnail.key,
      mediaType: template.mediaType,
      category: template.category,
      subcategory: template.subcategory,
      tag: template.tag,
      price: template.price,
      discountPrice: template.discountPrice,
      downloadCount: template.downloadCount,
      free: template.free,
      status: template.status,
      approvalStatus: template.approvalStatus,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
      creatorType: template.creatorType,
      instructorUsername: template.instructorUsername,
      averageReview: template.averageReview || 0,
      reviewCount: template.reviewCount || 0,
    };
  }

  async findByPublicId(
    publicId: string,
    userId?: string,
  ): Promise<PublicTemplateResponseDto> {
    // First find if template exists and get basic info
    const templateCheck = await this.templateModel
      .findOne({
        publicId,
        documentStatus: DocumentStatus.ACTIVE,
        status: TemplateStatus.PUBLISHED,
        $or: [
          // Always show items with creatorType 'myyresearch'
          { creatorType: CreatorType.MYRESEARCH },
          // For other creatorTypes, only show if approved
          {
            creatorType: { $ne: CreatorType.MYRESEARCH },
            approvalStatus: TemplateApprovalStatus.APPROVED,
          },
        ],
      })
      .select('free price _id')
      .lean()
      .exec();

    if (!templateCheck) {
      throw new NotFoundException(
        `Template with public ID ${publicId} not found`,
      );
    }

    // Check if user has purchased this template
    let purchased = false;
    if (userId) {
      purchased = await this.ordersService.hasUserPurchasedItem(
        userId,
        templateCheck._id.toString(),
        OrderItemType.TEMPLATE,
      );
    }

    // Determine if full details should be shown
    const shouldShowFullDetails =
      templateCheck.free || purchased || templateCheck.price === 0;

    // Then fetch full template with or without downloadMedia
    const template = await this.templateModel
      .findOne({
        publicId,
        documentStatus: DocumentStatus.ACTIVE,
        status: TemplateStatus.PUBLISHED,
        $or: [
          // Always show items with creatorType 'myyresearch'
          { creatorType: CreatorType.MYRESEARCH },
          // For other creatorTypes, only show if approved
          {
            creatorType: { $ne: CreatorType.MYRESEARCH },
            approvalStatus: TemplateApprovalStatus.APPROVED,
          },
        ],
      })
      .select(
        shouldShowFullDetails
          ? {} // Include all fields for accessible templates
          : { downloadMedia: 0 }, // Exclude download media for inaccessible templates
      )
      .lean()
      .exec();

    if (!template) {
      throw new NotFoundException(
        `Template with public ID ${publicId} not found`,
      );
    }

    // Get instructor username if template is created by instructor
    let instructorUsername: string | undefined;
    if (template.creatorType === CreatorType.INSTRUCTOR) {
      const instructor = await this.userModel
        .findById(template.createdBy)
        .select('username')
        .lean()
        .exec();
      instructorUsername = instructor?.username;
    }

    // Get reviews for the template
    const reviews = await this.reviewModel
      .find({
        item: template._id,
        itemType: 'Template',
        status: ReviewStatus.APPROVED,
      })
      .sort({ createdAt: -1 })
      .lean()
      .exec();

    // Get all user IDs from reviews
    const userIds = reviews.map((review) => review.user);

    // Fetch user details in bulk
    const users = await this.userModel
      .find({ _id: { $in: userIds } })
      .select('firstName lastName avatar')
      .lean()
      .exec();

    // Create a map of user details for quick lookup
    const userMap = new Map(
      users.map((user) => [
        user._id.toString(),
        {
          firstName: user['firstName'] as string,
          lastName: user['lastName'] as string,
          avatar: user['avatar'] as string | undefined,
        },
      ]),
    );

    // Map reviews with user details
    const mappedReviews = reviews.map((review) => {
      const user = userMap.get(review.user.toString()) || {
        firstName: '',
        lastName: '',
        avatar: undefined,
      };
      return {
        _id: review._id.toString(),
        user: {
          _id: review.user.toString(),
          firstName: user.firstName,
          lastName: user.lastName,
          avatar: user.avatar,
        },
        rating: review.rating,
        comment: review.comment,
        createdAt: review['createdAt'] as Date,
      };
    });

    const responseDto: PublicTemplateResponseDto = {
      ...template,
      _id: template._id.toString(),
      createdAt: template['createdAt'] as Date,
      updatedAt: template['updatedAt'] as Date,
      title: template.title,
      publicId: template.publicId,
      shortDescription: template.shortDescription,
      description: template.description,
      thumbnail: template.thumbnail,
      mediaType: template.mediaType,
      category: template.category,
      subcategory: template.subcategory,
      tag: template.tag,
      price: template.price,
      discountPrice: template.discountPrice,
      downloadCount: template.downloadCount,
      free: template.free,
      status: template.status,
      creatorType: template.creatorType,
      instructorUsername,
      reviews: mappedReviews,
      averageReview: template.averageReview || 0,
      reviewCount: template.reviewCount || 0,
      previewMedia: template.previewMedia,
      downloadMedia: shouldShowFullDetails ? template.downloadMedia : undefined,
      purchased,
    };

    return responseDto;
  }

  async findAllFiltered(
    filterDto: TemplateFiltersDto,
  ): Promise<FilteredTemplatesResponseDto> {
    const {
      categories,
      subcategories,
      mediaTypes,
      page = 1,
      limit = 10,
    } = filterDto;
    const skip = (page - 1) * limit;

    // Build the base query with approval logic
    const baseQuery = {
      documentStatus: DocumentStatus.ACTIVE,
      status: TemplateStatus.PUBLISHED,
      $or: [
        // Always show items with creatorType 'myyresearch'
        { creatorType: CreatorType.MYRESEARCH },
        // For other creatorTypes, only show if approved
        {
          creatorType: { $ne: CreatorType.MYRESEARCH },
          approvalStatus: TemplateApprovalStatus.APPROVED,
        },
      ],
    };

    // Add category filter if provided
    if (categories && categories.length > 0) {
      baseQuery['category'] = { $in: categories };
    }

    // Add subcategory filter if provided
    if (subcategories && subcategories.length > 0) {
      baseQuery['subcategory'] = { $in: subcategories };
    }

    // Add mediaType filter if provided
    if (mediaTypes && mediaTypes.length > 0) {
      baseQuery['mediaType'] = { $in: mediaTypes };
    }

    // Find all templates matching the criteria with only required fields
    const [templates, total] = await Promise.all([
      this.templateModel
        .find(baseQuery)
        .select({
          _id: 1,
          title: 1,
          publicId: 1,
          shortDescription: 1,
          thumbnail: 1,
          mediaType: 1,
          category: 1,
          subcategory: 1,
          tag: 1,
          price: 1,
          discountPrice: 1,
          downloadCount: 1,
          free: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          creatorType: 1,
          instructorUsername: 1,
          averageReview: 1,
          reviewCount: 1,
        })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean()
        .exec(),
      this.templateModel.countDocuments(baseQuery),
    ]);

    const processedTemplates = templates.map((template) =>
      this.mapToMinimalisticDto(template),
    );

    return {
      data: processedTemplates,
      total,
      page,
      limit,
      pages: Math.ceil(total / limit),
    };
  }
}
