import { ApiProperty } from '@nestjs/swagger';
import {
  MediaType,
  TemplateStatus,
  TemplateTag,
  CreatorType,
  UploadedFile,
} from '../schemas/template.schema';

export class ReviewUserDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  firstName: string;

  @ApiProperty()
  lastName: string;

  @ApiProperty({ required: false })
  avatar?: string;
}

export class ReviewDto {
  @ApiProperty()
  _id: string;

  @ApiProperty({ type: ReviewUserDto })
  user: ReviewUserDto;

  @ApiProperty()
  rating: number;

  @ApiProperty()
  comment: string;

  @ApiProperty()
  createdAt: Date;
}

export class PublicTemplateResponseDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  publicId: string;

  @ApiProperty()
  shortDescription: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  thumbnail: UploadedFile;

  @ApiProperty({ enum: MediaType })
  mediaType: MediaType;

  @ApiProperty()
  category: string;

  @ApiProperty()
  subcategory: string;

  @ApiProperty({ enum: TemplateTag })
  tag: TemplateTag;

  @ApiProperty()
  price: number;

  @ApiProperty()
  discountPrice?: number;

  @ApiProperty()
  downloadCount: number;

  @ApiProperty()
  free: boolean;

  @ApiProperty({ enum: TemplateStatus })
  status: TemplateStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ enum: CreatorType })
  creatorType: CreatorType;

  @ApiProperty()
  instructorUsername?: string;

  @ApiProperty()
  averageReview: number;

  @ApiProperty()
  reviewCount: number;

  @ApiProperty({ type: [ReviewDto] })
  reviews: ReviewDto[];

  @ApiProperty()
  previewMedia?: UploadedFile;

  @ApiProperty({ required: false })
  downloadMedia?: UploadedFile;

  @ApiProperty({
    description: 'Whether the current user has purchased this template',
    default: false,
  })
  purchased: boolean;
}
