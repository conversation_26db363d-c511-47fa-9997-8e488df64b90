import {
  IsString,
  <PERSON><PERSON><PERSON>ber,
  IsEnum,
  IsBoolean,
  IsOptional,
  ValidateNested,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MediaType, TemplateStatus } from '../schemas/template.schema';

export class UploadedFileDto {
  @IsString()
  @IsNotEmpty()
  key: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  type: string;

  @IsNumber()
  @IsNotEmpty()
  size: number;
}

export class CreateTemplateDto {
  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsNotEmpty()
  shortDescription: string;

  @IsString()
  @IsOptional()
  description?: string;

  @ValidateNested()
  @Type(() => UploadedFileDto)
  thumbnail: UploadedFileDto;

  @IsEnum(MediaType)
  mediaType: MediaType;

  @IsString()
  @IsNotEmpty()
  category: string;

  @IsString()
  @IsNotEmpty()
  subcategory: string;

  @IsNumber()
  price: number;

  @IsNumber()
  @IsOptional()
  discountPrice?: number;

  @ValidateNested()
  @Type(() => UploadedFileDto)
  downloadMedia: UploadedFileDto;

  @ValidateNested()
  @Type(() => UploadedFileDto)
  @IsOptional()
  previewMedia?: UploadedFileDto;

  @ValidateNested()
  @Type(() => UploadedFileDto)
  @IsOptional()
  helpMedia?: UploadedFileDto;

  @IsBoolean()
  @IsOptional()
  free?: boolean;

  @IsString()
  @IsNotEmpty()
  version: string;

  @IsEnum(TemplateStatus)
  @IsOptional()
  status?: TemplateStatus;
}
