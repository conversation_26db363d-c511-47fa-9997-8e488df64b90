import { ApiProperty } from '@nestjs/swagger';
import { MinimalisticTemplateDto } from './minimalistic-template.dto';

export class FilteredTemplatesResponseDto {
  @ApiProperty({ type: [MinimalisticTemplateDto] })
  data: MinimalisticTemplateDto[];

  @ApiProperty({
    description: 'Total number of templates matching the criteria',
  })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages available' })
  pages: number;
}
