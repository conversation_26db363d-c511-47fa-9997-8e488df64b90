import { ApiProperty } from '@nestjs/swagger';
import {
  MediaType,
  TemplateStatus,
  TemplateTag,
  CreatorType,
} from '../schemas/template.schema';
import { TemplateApprovalStatus } from './approve-template.dto';

export class MinimalisticTemplateDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  publicId: string;

  @ApiProperty()
  shortDescription: string;

  @ApiProperty()
  thumbnail: string;

  @ApiProperty({ enum: MediaType })
  mediaType: MediaType;

  @ApiProperty()
  category: string;

  @ApiProperty()
  subcategory: string;

  @ApiProperty({ enum: TemplateTag })
  tag: TemplateTag;

  @ApiProperty()
  price: number;

  @ApiProperty()
  discountPrice?: number;

  @ApiProperty()
  downloadCount: number;

  @ApiProperty()
  free: boolean;

  @ApiProperty({ enum: TemplateStatus })
  status: TemplateStatus;

  @ApiProperty({ enum: TemplateApprovalStatus })
  approvalStatus: TemplateApprovalStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ enum: CreatorType })
  creatorType: CreatorType;

  @ApiProperty()
  instructorUsername?: string;

  @ApiProperty({ description: 'Average review rating', default: 0 })
  averageReview: number;

  @ApiProperty({ description: 'Total number of reviews', default: 0 })
  reviewCount: number;
}
