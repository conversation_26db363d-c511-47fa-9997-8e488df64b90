import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsOptional, IsNumber, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { MediaType } from '../schemas/template.schema';

export class TemplateFiltersDto {
  @ApiProperty({
    required: false,
    type: [String],
    description: 'Array of categories to filter by',
  })
  @IsArray()
  @IsOptional()
  @Transform(({ value }) =>
    Array.isArray(value) ? value : value ? [value] : [],
  )
  categories?: string[] = [];

  @ApiProperty({
    required: false,
    type: [String],
    description: 'Array of subcategories to filter by',
  })
  @IsArray()
  @IsOptional()
  @Transform(({ value }) =>
    Array.isArray(value) ? value : value ? [value] : [],
  )
  subcategories?: string[] = [];

  @ApiProperty({
    required: false,
    type: [String],
    enum: MediaType,
    isArray: true,
    description: 'Array of media types to filter by',
  })
  @IsArray()
  @IsOptional()
  @Transform(({ value }) =>
    Array.isArray(value) ? value : value ? [value] : [],
  )
  mediaTypes?: MediaType[] = [];

  @ApiProperty({ required: false, default: 1 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ required: false, default: 10 })
  @IsNumber()
  @Min(1)
  @Max(100)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @Type(() => Number)
  limit?: number = 10;
}
