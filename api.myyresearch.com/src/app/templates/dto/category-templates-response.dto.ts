import { ApiProperty } from '@nestjs/swagger';
import { MinimalisticTemplateDto } from './minimalistic-template.dto';

export class CategoryTemplatesResponseDto {
  @ApiProperty({ type: [MinimalisticTemplateDto] })
  data: MinimalisticTemplateDto[];

  @ApiProperty({
    description: 'Total number of templates matching the criteria',
  })
  total: number;

  @ApiProperty({ description: 'Total number of pages available' })
  pages: number;
}
