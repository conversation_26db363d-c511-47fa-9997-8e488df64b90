import {
  IsString,
  IsN<PERSON>ber,
  IsEnum,
  IsBoolean,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MediaType, TemplateStatus } from '../schemas/template.schema';
import { UploadedFileDto } from './create-template.dto';

export class UpdateTemplateDto {
  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  slug?: string;

  @IsString()
  @IsOptional()
  shortDescription?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @ValidateNested()
  @Type(() => UploadedFileDto)
  @IsOptional()
  thumbnail?: UploadedFileDto;

  @IsEnum(MediaType)
  @IsOptional()
  mediaType?: MediaType;

  @IsString()
  @IsOptional()
  category?: string;

  @IsString()
  @IsOptional()
  subcategory?: string;

  @IsNumber()
  @IsOptional()
  price?: number;

  @IsNumber()
  @IsOptional()
  discountPrice?: number;

  @ValidateNested()
  @Type(() => UploadedFileDto)
  @IsOptional()
  downloadMedia?: UploadedFileDto;

  @ValidateNested()
  @Type(() => UploadedFileDto)
  @IsOptional()
  previewMedia?: UploadedFileDto;

  @ValidateNested()
  @Type(() => UploadedFileDto)
  @IsOptional()
  helpMedia?: UploadedFileDto;

  @IsBoolean()
  @IsOptional()
  free?: boolean;

  @IsString()
  @IsOptional()
  version?: string;

  @IsEnum(TemplateStatus)
  @IsOptional()
  status?: TemplateStatus;
}
