import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { TemplateApprovalStatus } from '../dto/approve-template.dto';
import { nanoid } from 'nanoid';

export type TemplateDocument = Template & Document;

export enum MediaType {
  DOC = 'doc',
  EXCEL = 'excel',
  PDF = 'pdf',
  PPT = 'ppt',
  TXT = 'txt',
  CSV = 'csv',
  AUDIO = 'audio',
  IMAGE = 'image',
  VIDEO = 'video',
  TEXT = 'text',
  OTHER = 'other',
}

export enum TemplateTag {
  NONE = 'none',
  BEST_SELLER = 'best-seller',
  NEW_ARRIVAL = 'new-arrival',
  TRENDING = 'trending',
}

export enum TemplateStatus {
  PUBLISHED = 'published',
  DRAFT = 'draft',
  PENDING = 'pending',
  REJECTED = 'rejected',
}

export enum DocumentStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

export enum CreatorType {
  ADMIN = 'admin',
  INSTRUCTOR = 'instructor',
  MYRESEARCH = 'myresearch',
}

// 'thumbnail.property name should not exist',
// 'thumbnail.property url should not exist',
// 'thumbnail.property key should not exist',
// 'thumbnail.property type should not exist',
// 'thumbnail.property size should not exist',

export class UploadedFile {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  key: string;

  @Prop({ required: true })
  type: string;

  @Prop({
    required: true,
    type: Number,
    min: 0,
  })
  size: number;
}

@Schema({ timestamps: true })
export class Template {
  @Prop({ required: true })
  title: string;

  @Prop({
    required: false,
    default: () => nanoid(10),
  })
  publicId?: string;

  @Prop({ required: true })
  shortDescription: string;

  @Prop()
  description: string;

  @Prop({ type: UploadedFile, required: true })
  thumbnail: UploadedFile;

  @Prop({ type: String, enum: MediaType, required: true })
  mediaType: MediaType;

  @Prop({ required: true })
  category: string;

  @Prop({ required: true })
  subcategory: string;

  @Prop({ type: String, enum: TemplateTag, default: TemplateTag.NONE })
  tag: TemplateTag;

  @Prop({ required: true })
  price: number;

  @Prop()
  discountPrice: number;

  @Prop({ default: 0 })
  value: number;

  @Prop({ default: 0 })
  downloadCount: number;

  @Prop({ type: UploadedFile, required: true })
  downloadMedia: UploadedFile;

  @Prop({ type: UploadedFile })
  previewMedia: UploadedFile;

  @Prop({ type: UploadedFile })
  helpMedia: UploadedFile;

  @Prop({ default: false })
  free: boolean;

  @Prop({ required: true })
  version: string;

  @Prop({ type: String, enum: TemplateStatus, default: TemplateStatus.DRAFT })
  status: TemplateStatus;

  @Prop({ type: String, enum: DocumentStatus, default: DocumentStatus.ACTIVE })
  documentStatus: DocumentStatus;

  @Prop({
    type: String,
    enum: TemplateApprovalStatus,
    default: TemplateApprovalStatus.PENDING,
  })
  approvalStatus: TemplateApprovalStatus;

  @Prop()
  approvalComment?: string;

  @Prop({ type: MongooseSchema.Types.ObjectId })
  approvedBy?: string;

  @Prop()
  approvalDate?: Date;

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true })
  createdBy: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true })
  updatedBy: string;

  @Prop({ type: String, enum: CreatorType, required: true })
  creatorType: CreatorType;

  @Prop({ default: 0 })
  points: number;

  @Prop({ type: MongooseSchema.Types.ObjectId })
  originalId?: string;

  @Prop({ default: 0 })
  averageReview: number;

  @Prop({ default: 0 })
  reviewCount: number;
}

export const TemplateSchema = SchemaFactory.createForClass(Template);

TemplateSchema.index({ publicId: 1 }, { unique: true, sparse: true });
