import { Controller, Get, UseGuards, Request } from '@nestjs/common';
import { StatsService } from './stats.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('stats')
@UseGuards(JwtAuthGuard)
export class StatsController {
  constructor(private readonly statsService: StatsService) {}

  @Get()
  async getOverallStats(@Request() req) {
    return this.statsService.getOverallStats({
      _id: req.user._id,
      role: req.user.role,
    });
  }

  @Get('students')
  async getStudentStats(@Request() req) {
    return this.statsService.getStudentStats({
      _id: req.user._id,
      role: req.user.role,
    });
  }

  @Get('enrollments')
  async getEnrollmentStats(@Request() req) {
    return this.statsService.getEnrollmentStats({
      _id: req.user._id,
      role: req.user.role,
    });
  }

  @Get('courses')
  async getCourseStats(@Request() req) {
    return this.statsService.getCourseStats({
      _id: req.user._id,
      role: req.user.role,
    });
  }

  @Get('templates')
  async getTemplateStats(@Request() req) {
    return this.statsService.getTemplateStats({
      _id: req.user._id,
      role: req.user.role,
    });
  }
}
