import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Student } from '../students/schemas/student.schema';
import { Enrollment } from '../enrollments/schemas/enrollment.schema';
import { Course } from '../courses/schemas/course.schema';
import { Template } from '../templates/schemas/template.schema';

interface AuthUser {
  _id: string;
  role: string;
}

@Injectable()
export class StatsService {
  constructor(
    @InjectModel(Student.name) private studentModel: Model<Student>,
    @InjectModel(Enrollment.name) private enrollmentModel: Model<Enrollment>,
    @InjectModel(Course.name) private courseModel: Model<Course>,
    @InjectModel(Template.name) private templateModel: Model<Template>,
  ) {}

  async getOverallStats(user: AuthUser) {
    const isAdmin = user.role === 'admin';
    const query = isAdmin ? {} : { createdBy: user._id };

    const totalStudents = await this.studentModel.countDocuments();
    const totalEnrollments = await this.enrollmentModel.countDocuments();
    const totalCourses = await this.courseModel.countDocuments(query);
    const totalTemplates = await this.templateModel.countDocuments(query);

    return {
      totalStudents: isAdmin ? totalStudents : null,
      totalEnrollments: isAdmin ? totalEnrollments : null,
      totalCourses,
      totalTemplates,
    };
  }

  async getStudentStats(user: AuthUser) {
    if (user.role !== 'admin') {
      return null;
    }

    const totalStudents = await this.studentModel.countDocuments();
    const activeStudents = await this.studentModel.countDocuments({
      isActive: true,
    });

    return {
      total: totalStudents,
      active: activeStudents,
      inactive: totalStudents - activeStudents,
    };
  }

  async getEnrollmentStats(user: AuthUser) {
    if (user.role !== 'admin') {
      return null;
    }

    const totalEnrollments = await this.enrollmentModel.countDocuments();
    const activeEnrollments = await this.enrollmentModel.countDocuments({
      status: 'active',
    });
    const completedEnrollments = await this.enrollmentModel.countDocuments({
      status: 'completed',
    });

    return {
      total: totalEnrollments,
      active: activeEnrollments,
      completed: completedEnrollments,
      other: totalEnrollments - (activeEnrollments + completedEnrollments),
    };
  }

  async getCourseStats(user: AuthUser) {
    const isAdmin = user.role === 'admin';
    const baseQuery = isAdmin ? {} : { createdBy: user._id };

    const totalCourses = await this.courseModel.countDocuments(baseQuery);
    const publishedCourses = await this.courseModel.countDocuments({
      ...baseQuery,
      status: 'published',
      documentStatus: 'active',
    });
    const draftCourses = await this.courseModel.countDocuments({
      ...baseQuery,
      status: 'draft',
      documentStatus: 'active',
    });

    // Get courses by category
    const coursesByCategory = await this.courseModel.aggregate([
      {
        $match: {
          ...baseQuery,
          documentStatus: 'active',
          status: 'published',
        },
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          subcategories: {
            $push: {
              name: '$subcategory',
              count: 1,
            },
          },
        },
      },
      {
        $project: {
          category: '$_id',
          count: 1,
          subcategories: {
            $reduce: {
              input: '$subcategories',
              initialValue: [],
              in: {
                $concatArrays: [
                  '$$value',
                  [
                    {
                      name: '$$this.name',
                      count: {
                        $sum: {
                          $map: {
                            input: '$subcategories',
                            as: 'sub',
                            in: {
                              $cond: [
                                { $eq: ['$$sub.name', '$$this.name'] },
                                1,
                                0,
                              ],
                            },
                          },
                        },
                      },
                    },
                  ],
                ],
              },
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          category: 1,
          count: 1,
          subcategories: {
            $reduce: {
              input: '$subcategories',
              initialValue: [],
              in: {
                $cond: [
                  {
                    $in: ['$$this.name', '$$value.name'],
                  },
                  '$$value',
                  { $concatArrays: ['$$value', ['$$this']] },
                ],
              },
            },
          },
        },
      },
    ]);

    return {
      total: totalCourses,
      published: publishedCourses,
      draft: draftCourses,
      byCategory: coursesByCategory,
    };
  }

  async getTemplateStats(user: AuthUser) {
    const isAdmin = user.role === 'admin';
    const baseQuery = isAdmin ? {} : { createdBy: user._id };

    const totalTemplates = await this.templateModel.countDocuments(baseQuery);
    const publishedTemplates = await this.templateModel.countDocuments({
      ...baseQuery,
      status: 'published',
      documentStatus: 'active',
    });
    const draftTemplates = await this.templateModel.countDocuments({
      ...baseQuery,
      status: 'draft',
      documentStatus: 'active',
    });

    // Get templates by category
    const templatesByCategory = await this.templateModel.aggregate([
      {
        $match: {
          ...baseQuery,
          documentStatus: 'active',
          status: 'published',
        },
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          subcategories: {
            $push: {
              name: '$subcategory',
              count: 1,
            },
          },
        },
      },
      {
        $project: {
          category: '$_id',
          count: 1,
          subcategories: {
            $reduce: {
              input: '$subcategories',
              initialValue: [],
              in: {
                $concatArrays: [
                  '$$value',
                  [
                    {
                      name: '$$this.name',
                      count: {
                        $sum: {
                          $map: {
                            input: '$subcategories',
                            as: 'sub',
                            in: {
                              $cond: [
                                { $eq: ['$$sub.name', '$$this.name'] },
                                1,
                                0,
                              ],
                            },
                          },
                        },
                      },
                    },
                  ],
                ],
              },
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          category: 1,
          count: 1,
          subcategories: {
            $reduce: {
              input: '$subcategories',
              initialValue: [],
              in: {
                $cond: [
                  {
                    $in: ['$$this.name', '$$value.name'],
                  },
                  '$$value',
                  { $concatArrays: ['$$value', ['$$this']] },
                ],
              },
            },
          },
        },
      },
    ]);

    return {
      total: totalTemplates,
      published: publishedTemplates,
      draft: draftTemplates,
      byCategory: templatesByCategory,
    };
  }
}
