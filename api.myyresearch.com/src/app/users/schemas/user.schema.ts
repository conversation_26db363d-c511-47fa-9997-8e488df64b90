import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserDocument = User &
  Document & {
    createdAt: Date;
    updatedAt: Date;
  };

export enum AccountType {
  EMAIL = 'email',
  GOOGLE = 'google',
  FACEBOOK = 'facebook',
}

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  STAFF = 'staff',
  INSTRUCTOR = 'instructor',
  CONSULTANT = 'consultant',
}

export enum TwoFactorMethod {
  NONE = 'none',
  EMAIL = 'email',
  AUTHENTICATOR = 'authenticator',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

export enum DocumentStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

export interface LinkedAccount {
  provider: AccountType;
  providerId?: string; // ID from the provider (e.g., Google ID, Facebook ID)
  email: string;
}

@Schema({ timestamps: true })
export class User {
  @Prop({ required: true, unique: true, lowercase: true })
  username: string;

  @Prop({ required: true, unique: true, lowercase: true })
  email: string;

  @Prop({ required: false })
  password: string;

  @Prop({ default: null })
  emailVerified: Date | null;

  @Prop({ type: String, enum: AccountType, default: AccountType.EMAIL })
  accountType: AccountType;

  @Prop({ type: String, enum: UserRole, default: UserRole.USER })
  role: UserRole;

  @Prop({ type: String, enum: TwoFactorMethod, default: TwoFactorMethod.NONE })
  twoFactorMethod: TwoFactorMethod;

  @Prop({ default: null })
  twoFactorSecret: string | null;

  @Prop({ type: String, enum: UserStatus, default: UserStatus.ACTIVE })
  status: UserStatus;

  @Prop({ type: String, enum: DocumentStatus, default: DocumentStatus.ACTIVE })
  documentStatus: DocumentStatus;

  @Prop({ type: String, default: null })
  refreshToken: string | null;

  @Prop({ default: false })
  twoFactorEnabled: boolean;

  @Prop()
  resetPasswordToken: string;

  @Prop()
  resetPasswordExpires: Date;

  @Prop({ default: null })
  avatar: string | null;

  @Prop([
    {
      provider: { type: String, enum: AccountType },
      providerId: String,
      email: String,
    },
  ])
  linkedAccounts: LinkedAccount[];

  @Prop({ type: String })
  stripeCustomerId?: string; // For students' payments

  @Prop({ type: String })
  stripeAccountId?: string; // For instructors' payouts

  createdAt: Date;
  updatedAt: Date;
}

export const UserSchema = SchemaFactory.createForClass(User);

// Add compound index for case-insensitive uniqueness
UserSchema.index(
  { username: 1 },
  {
    unique: true,
    collation: { locale: 'en', strength: 2 },
  },
);

UserSchema.index(
  { email: 1 },
  {
    unique: true,
    collation: { locale: 'en', strength: 2 },
  },
);
