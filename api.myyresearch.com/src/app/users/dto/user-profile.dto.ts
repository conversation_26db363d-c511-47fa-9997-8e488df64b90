import { ApiProperty } from '@nestjs/swagger';
import { AccountType, TwoFactorMethod, UserRole } from '../schemas/user.schema';

export class UserProfileDto {
  @ApiProperty({
    example: '507f1f77bcf86cd799439011',
    description: 'User ID',
  })
  _id: string;

  @ApiProperty({
    example: 'john_doe',
    description: 'Username',
  })
  username: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address',
  })
  email: string;

  @ApiProperty({
    example: '2024-03-20T12:00:00Z',
    description: 'Email verification timestamp',
    nullable: true,
  })
  emailVerified: Date | null;

  @ApiProperty({
    example: 'EMAIL',
    enum: AccountType,
    description: 'Type of user account',
  })
  accountType: AccountType;

  @ApiProperty({
    example: 'USER',
    enum: UserRole,
    description: 'User role',
  })
  role: UserRole;

  @ApiProperty({
    example: 'NONE',
    enum: TwoFactorMethod,
    description: '2FA method',
  })
  twoFactorMethod: TwoFactorMethod;

  @ApiProperty({
    example: true,
    description: '2FA enabled',
  })
  twoFactorEnabled: boolean;

  @ApiProperty({
    example: 'https://example.com/avatars/user123.jpg',
    description: 'URL to user avatar image',
    required: false,
    nullable: true,
  })
  avatar: string | null;
}
