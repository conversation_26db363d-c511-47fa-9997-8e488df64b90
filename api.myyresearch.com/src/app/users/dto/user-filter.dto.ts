import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsDateString } from 'class-validator';
import { Transform } from 'class-transformer';
import {
  UserRole,
  UserStatus,
  AccountType,
} from '../../users/schemas/user.schema';
import { Type } from 'class-transformer';
import { SortItemDto } from './sort.dto';
import { FilterItemDto, JoinOperator } from './filter.dto';

export class UserFilterDto {
  @ApiPropertyOptional({ description: 'Search by username or email' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ enum: UserRole, description: 'Filter by user role' })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @ApiPropertyOptional({
    enum: UserStatus,
    description: 'Filter by user status',
  })
  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @ApiPropertyOptional({
    enum: AccountType,
    description: 'Filter by account type',
  })
  @IsOptional()
  @IsEnum(AccountType)
  accountType?: AccountType;

  @ApiPropertyOptional({ description: 'Number of items per page' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Page number' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @ApiPropertyOptional({
    type: [SortItemDto],
    description:
      'Sort criteria array. Example: [{"id":"createdAt","desc":true}]',
  })
  @IsOptional()
  @Type(() => SortItemDto)
  sort?: SortItemDto[];

  @ApiPropertyOptional({
    type: [FilterItemDto],
    description: 'Advanced filter criteria array',
  })
  @IsOptional()
  @Type(() => FilterItemDto)
  filters?: FilterItemDto[];

  @ApiPropertyOptional({
    enum: JoinOperator,
    description: 'Operator to join multiple filters (and/or)',
    default: JoinOperator.AND,
  })
  @IsOptional()
  @IsEnum(JoinOperator)
  joinOperator?: JoinOperator = JoinOperator.AND;

  @ApiPropertyOptional({
    description: 'Start date for filtering (format: YYYY-MM-DD)',
    example: '2024-12-13',
  })
  @IsOptional()
  @IsDateString()
  from?: string;

  @ApiPropertyOptional({
    description: 'End date for filtering (format: YYYY-MM-DD)',
    example: '2024-12-13',
  })
  @IsOptional()
  @IsDateString()
  to?: string;
}
