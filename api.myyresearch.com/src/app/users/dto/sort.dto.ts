import { IsString, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SortItemDto {
  @ApiProperty({
    description: 'Field name to sort by (e.g. createdAt, username, email, etc)',
    example: 'createdAt',
    enum: ['createdAt', 'username', 'email', 'status', 'role', 'updatedAt'],
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Sort direction (true for descending, false for ascending)',
    example: true,
    type: Boolean,
  })
  @IsBoolean()
  desc: boolean;
}
