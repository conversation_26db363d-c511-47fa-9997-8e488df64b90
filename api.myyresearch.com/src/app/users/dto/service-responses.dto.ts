import { ApiProperty } from '@nestjs/swagger';
import { UserProfileDto } from './user-profile.dto';
import { DocumentStatus, UserRole, UserStatus } from '../schemas/user.schema';

export class GetProfileResponseDto extends UserProfileDto {}

export class DeleteUserResponseDto {
  @ApiProperty({ example: 'User archived successfully' })
  message: string;

  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  userId: string;
}

export class DeleteUsersResponseDto {
  @ApiProperty({ example: 'Users archived successfully' })
  message: string;

  @ApiProperty({ example: 5 })
  matchedCount: number;

  @ApiProperty({ example: 5 })
  modifiedCount: number;
}

export class UserListItemDto extends UserProfileDto {
  @ApiProperty({ enum: UserStatus })
  status: UserStatus;

  @ApiProperty({ enum: DocumentStatus })
  documentStatus: DocumentStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class GetAllUsersResponseDto {
  @ApiProperty({ type: [UserListItemDto] })
  users: UserListItemDto[];

  @ApiProperty({ example: 100 })
  total: number;

  @ApiProperty({ example: 1 })
  page: number;

  @ApiProperty({ example: 10 })
  totalPages: number;
}

export class StatusCountDto {
  @ApiProperty({ example: { active: 10, inactive: 5, suspended: 2 } })
  counts: Record<UserStatus, number>;

  @ApiProperty({ example: 17 })
  total: number;
}

export class RoleCountDto {
  @ApiProperty({ example: { admin: 2, user: 15, shop: 5 } })
  counts: Record<UserRole, number>;

  @ApiProperty({ example: 22 })
  total: number;
}
