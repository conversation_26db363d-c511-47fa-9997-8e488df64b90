import {
  Injectable,
  BadRequestException,
  UnauthorizedException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcrypt';
import {
  User,
  UserDocument,
  DocumentStatus,
  UserRole,
  UserStatus,
} from './schemas/user.schema';
import { UpdateUserDto, UpdateUserResponseDto } from './dto/update-user.dto';
import {
  AdminUpdateUserDto,
  AdminUpdateUserResponseDto,
} from './dto/admin-update-user.dto';
import { UserFilterDto } from './dto/user-filter.dto';
import { UserProfileDto } from './dto/user-profile.dto';
import {
  GetProfileResponseDto,
  DeleteUserResponseDto,
  DeleteUsersResponseDto,
  GetAllUsersResponseDto,
  UserListItemDto,
  StatusCountDto,
  RoleCountDto,
} from './dto/service-responses.dto';
import { FilterOperator, FilterType, JoinOperator } from './dto/filter.dto';

interface CreateUserParams {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  role: string;
}

@Injectable()
export class UsersService {
  constructor(@InjectModel(User.name) private userModel: Model<UserDocument>) {}

  async getProfile(user: UserDocument): Promise<GetProfileResponseDto> {
    const userData = await this.userModel
      .findById(user._id)
      .select('-password')
      .lean();

    if (!userData) {
      throw new UnauthorizedException('User not found');
    }

    return userData;
  }

  async updateProfile(
    userId: string,
    updateUserDto: UpdateUserDto,
  ): Promise<UpdateUserResponseDto> {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const updateData: any = {};

    if (updateUserDto.username) {
      const existingUser = await this.userModel.findOne({
        username: updateUserDto.username,
        _id: { $ne: userId },
      });
      if (existingUser) {
        throw new BadRequestException('Username is already taken');
      }
      updateData.username = updateUserDto.username;
    }

    if (updateUserDto.email) {
      const existingUser = await this.userModel.findOne({
        email: updateUserDto.email,
        _id: { $ne: userId },
      });
      if (existingUser) {
        throw new BadRequestException('Email is already taken');
      }
      updateData.email = updateUserDto.email;
      updateData.emailVerified = null;
    }

    if (updateUserDto.newPassword) {
      if (!updateUserDto.currentPassword) {
        throw new BadRequestException(
          'Current password is required to set new password',
        );
      }

      const isPasswordValid = await bcrypt.compare(
        updateUserDto.currentPassword,
        user.password,
      );
      if (!isPasswordValid) {
        throw new BadRequestException('Current password is incorrect');
      }

      updateData.password = await bcrypt.hash(updateUserDto.newPassword, 10);
    }

    if (Object.keys(updateData).length === 0) {
      throw new BadRequestException('No valid update fields provided');
    }

    const updatedUser = await this.userModel.findByIdAndUpdate(
      userId,
      { $set: updateData },
      { new: true },
    );

    const userProfile: UserProfileDto = {
      _id: updatedUser._id,
      username: updatedUser.username,
      email: updatedUser.email,
      emailVerified: updatedUser.emailVerified,
      accountType: updatedUser.accountType,
      role: updatedUser.role,
      twoFactorMethod: updatedUser.twoFactorMethod,
      twoFactorEnabled: updatedUser.twoFactorEnabled,
      avatar: updatedUser.avatar,
    };

    return {
      success: true,
      message: 'User details updated successfully',
      user: userProfile,
    };
  }

  async adminUpdateUser(
    updateUserDto: AdminUpdateUserDto,
  ): Promise<AdminUpdateUserResponseDto> {
    const { userId, ...updateData } = updateUserDto;

    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (updateData.username || updateData.email) {
      const existingUser = await this.userModel.findOne({
        _id: { $ne: userId },
        $or: [
          updateData.username ? { username: updateData.username } : null,
          updateData.email ? { email: updateData.email } : null,
        ].filter(Boolean),
      });

      if (existingUser) {
        throw new BadRequestException(
          'Username or email is already taken by another user',
        );
      }
    }

    if (updateData.password) {
      updateData.password = await bcrypt.hash(updateData.password, 10);
    }

    const updatedUser = await this.userModel.findByIdAndUpdate(
      userId,
      { $set: updateData },
      { new: true },
    );

    const userProfile: UserListItemDto = {
      _id: updatedUser._id,
      username: updatedUser.username,
      email: updatedUser.email,
      emailVerified: updatedUser.emailVerified,
      accountType: updatedUser.accountType,
      role: updatedUser.role,
      twoFactorMethod: updatedUser.twoFactorMethod,
      twoFactorEnabled: updatedUser.twoFactorEnabled,
      status: updatedUser.status,
      documentStatus: updatedUser.documentStatus,
      createdAt: updatedUser.createdAt,
      updatedAt: updatedUser.updatedAt,
      avatar: updatedUser.avatar,
    };

    return {
      success: true,
      message: 'User updated successfully',
      user: userProfile,
    };
  }

  async deleteUser(id: string): Promise<DeleteUserResponseDto> {
    const user = await this.userModel.findByIdAndUpdate(
      id,
      { documentStatus: DocumentStatus.ARCHIVED },
      { new: true },
    );

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return {
      message: 'User archived successfully',
      userId: user._id,
    };
  }

  async deleteUsers(userIds: string[]): Promise<DeleteUsersResponseDto> {
    const result = await this.userModel.updateMany(
      { _id: { $in: userIds } },
      { documentStatus: DocumentStatus.ARCHIVED },
    );

    if (result.matchedCount === 0) {
      throw new BadRequestException('No users found with the provided IDs');
    }

    return {
      message: 'Users archived successfully',
      matchedCount: result.matchedCount,
      modifiedCount: result.modifiedCount,
    };
  }

  async getAllUsers(filterDto: UserFilterDto): Promise<GetAllUsersResponseDto> {
    const {
      search,
      role,
      status,
      accountType,
      page = 1,
      limit = 10,
      sort,
      filters,
      joinOperator = JoinOperator.AND,
      from,
      to,
    } = filterDto;

    const query: any = {
      documentStatus: DocumentStatus.ACTIVE,
    };

    // Add date range filtering
    if (from || to) {
      query.createdAt = {};
      if (from) {
        // Set time to start of day (00:00:00)
        const fromDate = new Date(from);
        fromDate.setHours(0, 0, 0, 0);
        query.createdAt.$gte = fromDate;
      }
      if (to) {
        // Set time to end of day (23:59:59.999)
        const toDate = new Date(to);
        toDate.setHours(23, 59, 59, 999);
        query.createdAt.$lte = toDate;
      }
    }

    // Handle basic filters
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    if (role) query.role = role;
    if (status) query.status = status;
    if (accountType) query.accountType = accountType;

    // Handle advanced filters
    if (filters && filters.length > 0) {
      const filterConditions = filters.map((filter) => {
        const condition: any = {};

        switch (filter.operator) {
          case FilterOperator.ILIKE:
            condition[filter.id] = { $regex: filter.value, $options: 'i' };
            break;
          case FilterOperator.NOT_ILIKE:
            condition[filter.id] = {
              $not: { $regex: filter.value, $options: 'i' },
            };
            break;
          case FilterOperator.EQ:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $in: filter.value };
            } else {
              condition[filter.id] = filter.value;
            }
            break;
          case FilterOperator.NE:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $nin: filter.value };
            } else {
              condition[filter.id] = { $ne: filter.value };
            }
            break;
          case FilterOperator.IS_EMPTY:
            condition[filter.id] = { $exists: false };
            break;
          case FilterOperator.IS_NOT_EMPTY:
            condition[filter.id] = { $exists: true };
            break;
        }
        return condition;
      });

      if (filterConditions.length > 0) {
        const operator = joinOperator === JoinOperator.AND ? '$and' : '$or';
        query[operator] = filterConditions;
      }
    }

    const skip = (page - 1) * limit;

    // Create sort object for mongoose
    let sortCriteria = {};
    if (sort && Array.isArray(sort)) {
      sort.forEach((sortItem) => {
        sortCriteria[sortItem.id] = sortItem.desc ? -1 : 1;
      });
    } else {
      // Default sort by createdAt desc if no sort provided
      sortCriteria = { createdAt: -1 };
    }

    const [users, total] = await Promise.all([
      this.userModel
        .find(query)
        .select('-password')
        .sort(sortCriteria) // Apply the sort criteria
        .skip(skip)
        .limit(limit)
        .lean(),
      this.userModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(total / limit);

    const mappedUsers: UserListItemDto[] = users.map((user) => ({
      _id: user._id,
      username: user.username,
      email: user.email,
      emailVerified: user.emailVerified,
      accountType: user.accountType,
      role: user.role,
      twoFactorMethod: user.twoFactorMethod,
      twoFactorEnabled: user.twoFactorEnabled,
      status: user.status,
      documentStatus: user.documentStatus,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      avatar: user.avatar,
    }));

    return {
      users: mappedUsers,
      total,
      page,
      totalPages,
    };
  }

  async getStatusCounts(filterDto: UserFilterDto): Promise<StatusCountDto> {
    const {
      search,
      role,
      accountType,
      filters,
      joinOperator = JoinOperator.AND,
      from,
      to,
    } = filterDto;

    const pipeline: any[] = [];

    // Build match conditions
    const matchConditions: any = {
      documentStatus: DocumentStatus.ACTIVE,
    };

    // Add date range filtering
    if (from || to) {
      matchConditions.createdAt = {};
      if (from) {
        // Set time to start of day (00:00:00)
        const fromDate = new Date(from);
        fromDate.setHours(0, 0, 0, 0);
        matchConditions.createdAt.$gte = fromDate;
      }
      if (to) {
        // Set time to end of day (23:59:59.999)
        const toDate = new Date(to);
        toDate.setHours(23, 59, 59, 999);
        matchConditions.createdAt.$lte = toDate;
      }
    }

    // Handle basic filters
    if (search) {
      matchConditions.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    if (role) matchConditions.role = role;
    if (accountType) matchConditions.accountType = accountType;

    // Handle advanced filters
    if (filters && filters.length > 0) {
      const filterConditions = filters.map((filter) => {
        const condition: any = {};

        switch (filter.operator) {
          case FilterOperator.ILIKE:
            condition[filter.id] = { $regex: filter.value, $options: 'i' };
            break;
          case FilterOperator.NOT_ILIKE:
            condition[filter.id] = {
              $not: { $regex: filter.value, $options: 'i' },
            };
            break;
          case FilterOperator.EQ:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $in: filter.value };
            } else {
              condition[filter.id] = filter.value;
            }
            break;
          case FilterOperator.NE:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $nin: filter.value };
            } else {
              condition[filter.id] = { $ne: filter.value };
            }
            break;
          case FilterOperator.IS_EMPTY:
            condition[filter.id] = { $exists: false };
            break;
          case FilterOperator.IS_NOT_EMPTY:
            condition[filter.id] = { $exists: true };
            break;
        }
        return condition;
      });

      if (filterConditions.length > 0) {
        const operator = joinOperator === JoinOperator.AND ? '$and' : '$or';
        matchConditions[operator] = filterConditions;
      }
    }

    // Add match conditions to pipeline if any exist
    if (Object.keys(matchConditions).length > 0) {
      pipeline.push({ $match: matchConditions });
    }

    pipeline.push({
      $group: {
        _id: '$status',
        count: { $sum: 1 },
      },
    });

    const results = await this.userModel.aggregate(pipeline);

    // Initialize counts with 0 for all status types
    const counts = Object.values(UserStatus).reduce(
      (acc, status) => {
        acc[status] = 0;
        return acc;
      },
      {} as Record<UserStatus, number>,
    );

    // Update counts with actual values
    results.forEach((result) => {
      counts[result._id] = result.count;
    });

    const total = Object.values(counts).reduce((sum, count) => sum + count, 0);

    return {
      counts,
      total,
    };
  }

  async getRoleCounts(filterDto: UserFilterDto): Promise<RoleCountDto> {
    const {
      search,
      status,
      accountType,
      filters,
      joinOperator = JoinOperator.AND,
      from,
      to,
    } = filterDto;

    const pipeline: any[] = [];

    // Build match conditions
    const matchConditions: any = {
      documentStatus: DocumentStatus.ACTIVE,
    };

    // Add date range filtering
    if (from || to) {
      matchConditions.createdAt = {};
      if (from) {
        // Set time to start of day (00:00:00)
        const fromDate = new Date(from);
        fromDate.setHours(0, 0, 0, 0);
        matchConditions.createdAt.$gte = fromDate;
      }
      if (to) {
        // Set time to end of day (23:59:59.999)
        const toDate = new Date(to);
        toDate.setHours(23, 59, 59, 999);
        matchConditions.createdAt.$lte = toDate;
      }
    }

    // Handle basic filters
    if (search) {
      matchConditions.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    if (status) matchConditions.status = status;
    if (accountType) matchConditions.accountType = accountType;

    // Handle advanced filters
    if (filters && filters.length > 0) {
      const filterConditions = filters.map((filter) => {
        const condition: any = {};

        switch (filter.operator) {
          case FilterOperator.ILIKE:
            condition[filter.id] = { $regex: filter.value, $options: 'i' };
            break;
          case FilterOperator.NOT_ILIKE:
            condition[filter.id] = {
              $not: { $regex: filter.value, $options: 'i' },
            };
            break;
          case FilterOperator.EQ:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $in: filter.value };
            } else {
              condition[filter.id] = filter.value;
            }
            break;
          case FilterOperator.NE:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $nin: filter.value };
            } else {
              condition[filter.id] = { $ne: filter.value };
            }
            break;
          case FilterOperator.IS_EMPTY:
            condition[filter.id] = { $exists: false };
            break;
          case FilterOperator.IS_NOT_EMPTY:
            condition[filter.id] = { $exists: true };
            break;
        }
        return condition;
      });

      if (filterConditions.length > 0) {
        const operator = joinOperator === JoinOperator.AND ? '$and' : '$or';
        matchConditions[operator] = filterConditions;
      }
    }

    // Add match conditions to pipeline if any exist
    if (Object.keys(matchConditions).length > 0) {
      pipeline.push({ $match: matchConditions });
    }

    pipeline.push({
      $group: {
        _id: '$role',
        count: { $sum: 1 },
      },
    });

    const results = await this.userModel.aggregate(pipeline);

    // Initialize counts with 0 for all role types
    const counts = Object.values(UserRole).reduce(
      (acc, role) => {
        acc[role] = 0;
        return acc;
      },
      {} as Record<UserRole, number>,
    );

    // Update counts with actual values
    results.forEach((result) => {
      counts[result._id] = result.count;
    });

    const total = Object.values(counts).reduce((sum, count) => sum + count, 0);

    return {
      counts,
      total,
    };
  }

  async create(createUserParams: CreateUserParams): Promise<UserDocument> {
    const hashedPassword = await bcrypt.hash(createUserParams.password, 10);

    const user = await this.userModel.create({
      ...createUserParams,
      password: hashedPassword,
    });

    return user;
  }
}
