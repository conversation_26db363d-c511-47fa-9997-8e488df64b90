import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Request,
  Query,
  Param,
  Delete,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from './schemas/user.schema';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UpdateUserDto, UpdateUserResponseDto } from './dto/update-user.dto';
import {
  AdminUpdateUserDto,
  AdminUpdateUserResponseDto,
} from './dto/admin-update-user.dto';
import { UserFilterDto } from './dto/user-filter.dto';
import { DeleteUsersDto } from './dto/delete-users.dto';
import { UserProfileDto } from './dto/user-profile.dto';
import {
  DeleteUserResponseDto,
  DeleteUsersResponseDto,
  GetAllUsersResponseDto,
  StatusCountDto,
  RoleCountDto,
} from './dto/service-responses.dto';

@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({
    status: 200,
    description: 'Returns user profile',
    type: UserProfileDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @UseGuards(JwtAuthGuard)
  @Get('profile')
  getProfile(@Request() req) {
    return this.usersService.getProfile(req.user);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user details' })
  @ApiResponse({
    status: 200,
    description: 'User details updated successfully',
    type: UpdateUserResponseDto,
  })
  @UseGuards(JwtAuthGuard)
  @Post('update-profile')
  updateProfile(
    @Request() req,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<UpdateUserResponseDto> {
    return this.usersService.updateProfile(req.user._id, updateUserDto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user details (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: AdminUpdateUserResponseDto,
  })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Post('admin/update-user')
  adminUpdateUser(
    @Body() updateUserDto: AdminUpdateUserDto,
  ): Promise<AdminUpdateUserResponseDto> {
    return this.usersService.adminUpdateUser(updateUserDto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete user (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'User archived successfully',
    type: DeleteUserResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Delete(':id')
  deleteUser(@Param('id') id: string) {
    return this.usersService.deleteUser(id);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete multiple users (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Users archived successfully',
    type: DeleteUsersResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Post('delete-many')
  deleteUsers(@Body() deleteUsersDto: DeleteUsersDto) {
    return this.usersService.deleteUsers(deleteUsersDto.userIds);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all users (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered users with pagination',
    type: [GetAllUsersResponseDto],
  })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get()
  getAllUsers(@Query() filterDto: UserFilterDto) {
    return this.usersService.getAllUsers(filterDto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user status counts (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns count of users by status',
    type: StatusCountDto,
  })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get('status-counts')
  getStatusCounts(@Query() filterDto: UserFilterDto): Promise<StatusCountDto> {
    return this.usersService.getStatusCounts(filterDto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user role counts (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns count of users by role',
    type: RoleCountDto,
  })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get('role-counts')
  getRoleCounts(@Query() filterDto: UserFilterDto): Promise<RoleCountDto> {
    return this.usersService.getRoleCounts(filterDto);
  }
}
