import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { nanoid } from 'nanoid';
import { CreatorType } from '../../courses/schemas/course.schema';
import { UploadedFile } from '../../templates/schemas/template.schema';

export type OrderDocument = Order & Document;
export type OrderItemDetailsDocument = OrderItemDetails & Document;

export enum OrderStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

export enum OrderItemType {
  COURSE = 'course',
  TEMPLATE = 'template',
}

export enum DocumentStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

@Schema()
export class OrderItem {
  @Prop({
    required: true,
    default: () => new MongooseSchema.Types.ObjectId('000000000000').toString(),
  })
  _id: string;

  @Prop({ type: String, enum: OrderItemType, required: true })
  itemType: OrderItemType;

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true })
  itemId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, min: 1 })
  quantity: number;

  @Prop({ required: true })
  unitPrice: number;

  @Prop()
  discountPrice?: number;

  @Prop({ required: true })
  totalPrice: number;

  @Prop({ required: true })
  title: string;

  @Prop()
  thumbnail?: string;
}

@Schema({ timestamps: true })
export class OrderItemDetails {
  @Prop({ type: String, enum: OrderItemType, required: true })
  itemType: OrderItemType;

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true })
  itemId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true, min: 1 })
  quantity: number;

  @Prop({ required: true })
  unitPrice: number;

  @Prop({ required: true })
  totalPrice: number;

  @Prop({ required: true })
  publicId: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true })
  createdBy: MongooseSchema.Types.ObjectId;

  @Prop({ type: String, enum: CreatorType, required: true })
  creatorType: CreatorType;

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true })
  orderedBy: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  orderDate: Date;

  @Prop({ required: true })
  itemTitle: string;

  @Prop({ type: UploadedFile, required: true })
  thumbnail: UploadedFile;

  @Prop({ required: true })
  shortDescription: string;

  @Prop({ required: true })
  orderId: string;

  @Prop({ required: true })
  category: string;

  @Prop({ required: true })
  subCategory: string;
}

@Schema({ timestamps: true })
export class Order {
  @Prop({
    required: true,
    default: () => nanoid(12),
    unique: true,
  })
  orderNumber: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: MongooseSchema.Types.ObjectId;

  @Prop({ type: [OrderItem], required: true })
  items: OrderItem[];

  @Prop({ required: true })
  subtotal: number;

  @Prop({ default: 0 })
  discount: number;

  @Prop({ default: 0 })
  tax: number;

  @Prop({ required: true })
  total: number;

  @Prop({ type: String, enum: OrderStatus, default: OrderStatus.PENDING })
  status: OrderStatus;

  @Prop({ type: String, enum: PaymentStatus, default: PaymentStatus.PENDING })
  paymentStatus: PaymentStatus;

  @Prop()
  paymentIntentId?: string;

  @Prop()
  stripePaymentId?: string;

  @Prop({ type: Date })
  paidAt?: Date;

  @Prop({ type: Date })
  completedAt?: Date;

  @Prop({ type: Date })
  cancelledAt?: Date;

  @Prop()
  cancellationReason?: string;

  @Prop({ type: String, enum: DocumentStatus, default: DocumentStatus.ACTIVE })
  documentStatus: DocumentStatus;

  @Prop({ type: Object })
  metadata?: Record<string, any>;
}

export const OrderSchema = SchemaFactory.createForClass(Order);
export const OrderItemDetailsSchema =
  SchemaFactory.createForClass(OrderItemDetails);

// Create indexes
OrderSchema.index({ orderNumber: 1 }, { unique: true });
OrderSchema.index({ userId: 1, documentStatus: 1 });
OrderSchema.index({ status: 1 });
OrderSchema.index({ paymentStatus: 1 });
OrderSchema.index({ createdAt: -1 });

// Create indexes for OrderItemDetails
OrderItemDetailsSchema.index({ itemId: 1, itemType: 1 });
OrderItemDetailsSchema.index({ orderedBy: 1 });
OrderItemDetailsSchema.index({ createdBy: 1 });
OrderItemDetailsSchema.index({ orderDate: -1 });
OrderItemDetailsSchema.index({ publicId: 1 });
OrderItemDetailsSchema.index({ orderId: 1 });
OrderItemDetailsSchema.index({ category: 1 });
OrderItemDetailsSchema.index({ subCategory: 1 });
OrderItemDetailsSchema.index({ category: 1, subCategory: 1 });
