import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Order,
  OrderSchema,
  OrderItemDetails,
  OrderItemDetailsSchema,
} from './schemas/order.schema';
import { Cart, CartSchema } from '../cart/schemas/cart.schema';
import { Course, CourseSchema } from '../courses/schemas/course.schema';
import { Template, TemplateSchema } from '../templates/schemas/template.schema';
import { OrdersController } from './orders.controller';
import { OrdersService } from './orders.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Order.name, schema: OrderSchema },
      { name: OrderItemDetails.name, schema: OrderItemDetailsSchema },
      { name: Cart.name, schema: CartSchema },
      { name: Course.name, schema: CourseSchema },
      { name: Template.name, schema: TemplateSchema },
    ]),
  ],
  controllers: [OrdersController],
  providers: [OrdersService],
  exports: [OrdersService, MongooseModule],
})
export class OrdersModule {}
