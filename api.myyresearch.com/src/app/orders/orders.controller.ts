import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { OrdersService } from './orders.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateOrderDto, CreateOrderResponseDto } from './dto/create-order.dto';
import {
  GetOrderResponseDto,
  GetOrdersResponseDto,
} from './dto/order-response.dto';
import { OrderFilterDto } from './dto/order-filter.dto';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import {
  OrderItemDetailsFilterDto,
  GetOrderItemDetailsResponseDto,
} from './dto/order-item-details.dto';
import { OrderItemType } from './schemas/order.schema';

@ApiTags('Orders')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Get('my-purchases')
  @ApiOperation({ summary: "Get user's personal purchase history" })
  @ApiResponse({
    status: 200,
    description: 'User purchases retrieved successfully',
    type: GetOrderItemDetailsResponseDto,
  })
  async getUserPurchases(
    @Request() req: any,
    @Query() filterDto: OrderItemDetailsFilterDto,
  ): Promise<GetOrderItemDetailsResponseDto> {
    console.log('getUserPurchases', filterDto);
    return this.ordersService.getUserPurchases(req.user._id, filterDto);
  }

  @Get('my-courses')
  @ApiOperation({ summary: "Get user's purchased courses" })
  @ApiResponse({
    status: 200,
    description: 'User courses retrieved successfully',
    type: GetOrderItemDetailsResponseDto,
  })
  async getUserCourses(
    @Request() req: any,
    @Query() filterDto: OrderItemDetailsFilterDto,
  ): Promise<GetOrderItemDetailsResponseDto> {
    return this.ordersService.getUserPurchases(req.user._id, {
      ...filterDto,
      itemType: OrderItemType.COURSE,
    });
  }

  @Get('my-templates')
  @ApiOperation({ summary: "Get user's purchased templates" })
  @ApiResponse({
    status: 200,
    description: 'User templates retrieved successfully',
    type: GetOrderItemDetailsResponseDto,
  })
  async getUserTemplates(
    @Request() req: any,
    @Query() filterDto: OrderItemDetailsFilterDto,
  ): Promise<GetOrderItemDetailsResponseDto> {
    return this.ordersService.getUserPurchases(req.user._id, {
      ...filterDto,
      itemType: OrderItemType.TEMPLATE,
    });
  }

  @Post('create')
  @ApiOperation({ summary: 'Create order from cart using cartId' })
  @ApiResponse({
    status: 201,
    description: 'Order created successfully',
    type: CreateOrderResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Cart is empty or cartId is invalid',
  })
  async createOrder(
    @Request() req: any,
    @Body() createOrderDto: CreateOrderDto,
  ): Promise<CreateOrderResponseDto> {
    return this.ordersService.createOrderFromCart(req.user._id, createOrderDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get user orders with filtering' })
  @ApiResponse({
    status: 200,
    description: 'Orders retrieved successfully',
    type: GetOrdersResponseDto,
  })
  async getUserOrders(
    @Request() req: any,
    @Query() filterDto: OrderFilterDto,
  ): Promise<GetOrdersResponseDto> {
    return this.ordersService.getUserOrders(req.user._id, filterDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get specific order by ID' })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Order retrieved successfully',
    type: GetOrderResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
  })
  async getOrder(
    @Request() req: any,
    @Param('id') orderId: string,
  ): Promise<GetOrderResponseDto> {
    return this.ordersService.getOrder(req.user._id, orderId);
  }

  @Get('items/all')
  @ApiOperation({
    summary: 'Get order items with role-based filtering (Admin, Instructor)',
  })
  @ApiResponse({
    status: 200,
    description: 'Order items retrieved successfully',
    type: GetOrderItemDetailsResponseDto,
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.INSTRUCTOR)
  async getOrderItems(
    @Request() req: any,
    @Query() filterDto: OrderItemDetailsFilterDto,
  ): Promise<GetOrderItemDetailsResponseDto> {
    return this.ordersService.getOrderItems(
      {
        userId: req.user._id,
        role: req.user.role,
      },
      filterDto,
    );
  }

  @Get('items/enrollments')
  @ApiOperation({ summary: 'Get course enrollments (Admin, Instructor)' })
  @ApiResponse({
    status: 200,
    description: 'Course enrollments retrieved successfully',
    type: GetOrderItemDetailsResponseDto,
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.INSTRUCTOR)
  async getCourseEnrollments(
    @Request() req: any,
    @Query() filterDto: OrderItemDetailsFilterDto,
  ): Promise<GetOrderItemDetailsResponseDto> {
    return this.ordersService.getOrderItems(
      {
        userId: req.user._id,
        role: req.user.role,
      },
      { ...filterDto, itemType: OrderItemType.COURSE },
    );
  }

  @Get('items/downloads')
  @ApiOperation({ summary: 'Get template downloads (Admin, Instructor)' })
  @ApiResponse({
    status: 200,
    description: 'Template downloads retrieved successfully',
    type: GetOrderItemDetailsResponseDto,
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.INSTRUCTOR)
  async getTemplateDownloads(
    @Request() req: any,
    @Query() filterDto: OrderItemDetailsFilterDto,
  ): Promise<GetOrderItemDetailsResponseDto> {
    return this.ordersService.getOrderItems(
      {
        userId: req.user._id,
        role: req.user.role,
      },
      { ...filterDto, itemType: OrderItemType.TEMPLATE },
    );
  }
}
