import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsObject, IsString, IsUUID } from 'class-validator';

export class CreateOrderDto {
  @ApiProperty({
    description: 'Cart ID to create order from',
    example: '550e8400-e29b-41d4-a716-************',
    required: true,
  })
  @IsString()
  @IsUUID()
  cartId: string;

  @ApiProperty({
    description: 'Additional metadata for the order',
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class CreateOrderResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Order created successfully' })
  message: string;

  @ApiProperty({
    description: 'Created order details',
  })
  order: any;
}
