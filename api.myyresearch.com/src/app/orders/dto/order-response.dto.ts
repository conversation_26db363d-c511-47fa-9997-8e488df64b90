import { ApiProperty } from '@nestjs/swagger';
import { OrderStatus, PaymentStatus, OrderItemType } from '../schemas/order.schema';

export class OrderItemDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  _id: string;

  @ApiProperty({ enum: OrderItemType, example: OrderItemType.COURSE })
  itemType: OrderItemType;

  @ApiProperty({ example: '507f1f77bcf86cd799439012' })
  itemId: string;

  @ApiProperty({ example: 1 })
  quantity: number;

  @ApiProperty({ example: 99.99 })
  unitPrice: number;

  @ApiProperty({ example: 79.99, required: false })
  discountPrice?: number;

  @ApiProperty({ example: 99.99 })
  totalPrice: number;

  @ApiProperty({ example: 'Complete Web Development Course' })
  title: string;

  @ApiProperty({ example: 'https://example.com/thumbnail.jpg', required: false })
  thumbnail?: string;
}

export class OrderDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439013' })
  _id: string;

  @ApiProperty({ example: 'ORD-ABC123DEF456' })
  orderNumber: string;

  @ApiProperty({ example: '507f1f77bcf86cd799439014' })
  userId: string;

  @ApiProperty({ type: [OrderItemDto] })
  items: OrderItemDto[];

  @ApiProperty({ example: 179.98 })
  subtotal: number;

  @ApiProperty({ example: 0 })
  discount: number;

  @ApiProperty({ example: 0 })
  tax: number;

  @ApiProperty({ example: 179.98 })
  total: number;

  @ApiProperty({ enum: OrderStatus, example: OrderStatus.PENDING })
  status: OrderStatus;

  @ApiProperty({ enum: PaymentStatus, example: PaymentStatus.PENDING })
  paymentStatus: PaymentStatus;

  @ApiProperty({ example: 'pi_1234567890', required: false })
  paymentIntentId?: string;

  @ApiProperty({ example: '2024-01-15T10:30:00Z', required: false })
  paidAt?: Date;

  @ApiProperty({ example: '2024-01-15T10:30:00Z', required: false })
  completedAt?: Date;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  createdAt: Date;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  updatedAt: Date;
}

export class GetOrderResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Order retrieved successfully' })
  message: string;

  @ApiProperty({ type: OrderDto })
  order: OrderDto;
}

export class GetOrdersResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Orders retrieved successfully' })
  message: string;

  @ApiProperty({ type: [OrderDto] })
  orders: OrderDto[];

  @ApiProperty({ example: 25 })
  total: number;

  @ApiProperty({ example: 1 })
  page: number;

  @ApiProperty({ example: 3 })
  totalPages: number;
}
