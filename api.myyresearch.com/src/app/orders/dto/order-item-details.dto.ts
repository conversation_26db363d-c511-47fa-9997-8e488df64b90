import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsDateString,
  IsNumber,
  Min,
  Max,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { OrderItemType } from '../schemas/order.schema';
import { CreatorType } from '@app/templates/schemas/template.schema';

export class OrderItemDetailsFilterDto {
  @ApiPropertyOptional({ description: 'Search term for item title' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    enum: OrderItemType,
    description: 'Filter by item type',
  })
  @IsOptional()
  @IsEnum(OrderItemType)
  itemType?: OrderItemType;

  @ApiPropertyOptional({
    enum: CreatorType,
    description: 'Filter by creator type',
  })
  @IsOptional()
  @IsEnum(CreatorType)
  creatorType?: CreatorType;

  @ApiPropertyOptional({ description: 'Filter by category' })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ description: 'Filter by subcategory' })
  @IsOptional()
  @IsString()
  subCategory?: string;

  @ApiPropertyOptional({ description: 'Filter by creator ID' })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiPropertyOptional({ description: 'Filter by orderer ID' })
  @IsOptional()
  @IsString()
  orderedBy?: string;

  @ApiPropertyOptional({
    description: 'Start date for order date range (ISO string)',
  })
  @IsOptional()
  @IsDateString()
  from?: string;

  @ApiPropertyOptional({
    description: 'End date for order date range (ISO string)',
  })
  @IsOptional()
  @IsDateString()
  to?: string;

  @ApiPropertyOptional({ description: 'Page number', minimum: 1, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Items per page',
    minimum: 1,
    maximum: 100,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Sort field', default: 'orderDate' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'orderDate';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class OrderItemDetailsDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  _id: string;

  @ApiProperty({ enum: OrderItemType, example: OrderItemType.COURSE })
  itemType: OrderItemType;

  @ApiProperty({ example: '507f1f77bcf86cd799439012' })
  itemId: string;

  @ApiProperty({ example: 1 })
  quantity: number;

  @ApiProperty({ example: 99.99 })
  unitPrice: number;

  @ApiProperty({ example: 99.99 })
  totalPrice: number;

  @ApiProperty({ example: 'ABC123DEF456' })
  publicId: string;

  @ApiProperty({ example: '507f1f77bcf86cd799439013' })
  createdBy: string;

  @ApiProperty({ enum: CreatorType, example: CreatorType.INSTRUCTOR })
  creatorType: CreatorType;

  @ApiProperty({ example: '507f1f77bcf86cd799439014' })
  orderedBy: string;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  orderDate: Date;

  @ApiProperty({ example: 'Complete Web Development Course' })
  itemTitle: string;

  @ApiProperty({
    example: {
      url: 'https://example.com/thumbnail.jpg',
      key: 'thumbnails/course-123.jpg',
      name: 'course-thumbnail.jpg',
      size: 1024000,
    },
  })
  thumbnail: {
    url: string;
    key: string;
    name: string;
    size: number;
  };

  @ApiProperty({
    example:
      'Learn web development from scratch with this comprehensive course',
  })
  shortDescription: string;

  @ApiProperty({ example: 'ORD123456789' })
  orderId: string;

  @ApiProperty({ example: 'Web Development' })
  category: string;

  @ApiProperty({ example: 'Frontend Development' })
  subCategory: string;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  createdAt: Date;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  updatedAt: Date;
}

export class GetOrderItemDetailsResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Order items retrieved successfully' })
  message: string;

  @ApiProperty({ type: [OrderItemDetailsDto] })
  data: OrderItemDetailsDto[];

  @ApiProperty({ example: 100 })
  total: number;

  @ApiProperty({ example: 1 })
  page: number;

  @ApiProperty({ example: 10 })
  limit: number;

  @ApiProperty({ example: 10 })
  totalPages: number;
}
