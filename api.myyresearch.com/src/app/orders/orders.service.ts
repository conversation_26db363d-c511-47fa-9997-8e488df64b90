import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Order,
  OrderDocument,
  OrderItemDetails,
  OrderItemDetailsDocument,
  OrderStatus,
  PaymentStatus,
  DocumentStatus,
  OrderItemType,
} from './schemas/order.schema';
import { Cart, CartDocument } from '../cart/schemas/cart.schema';
import { Course, CourseDocument } from '../courses/schemas/course.schema';
import {
  Template,
  TemplateDocument,
} from '../templates/schemas/template.schema';
import { CreateOrderDto, CreateOrderResponseDto } from './dto/create-order.dto';
import {
  GetOrderResponseDto,
  GetOrdersResponseDto,
  OrderDto,
} from './dto/order-response.dto';
import { OrderFilterDto } from './dto/order-filter.dto';
import {
  OrderItemDetailsFilterDto,
  GetOrderItemDetailsResponseDto,
} from './dto/order-item-details.dto';
import { UserRole } from '../users/schemas/user.schema';

@Injectable()
export class OrdersService {
  constructor(
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    @InjectModel(OrderItemDetails.name)
    private orderItemDetailsModel: Model<OrderItemDetailsDocument>,
    @InjectModel(Cart.name) private cartModel: Model<CartDocument>,
    @InjectModel(Course.name) private courseModel: Model<CourseDocument>,
    @InjectModel(Template.name) private templateModel: Model<TemplateDocument>,
  ) {}

  async createOrderFromCart(
    userId: string,
    createOrderDto: CreateOrderDto,
  ): Promise<CreateOrderResponseDto> {
    const { cartId } = createOrderDto;

    // Get cart by cartId
    const cart = await this.cartModel.findOne({
      cartId,
      documentStatus: DocumentStatus.ACTIVE,
    });

    if (!cart || cart.items.length === 0) {
      throw new BadRequestException('Cart is empty');
    }

    // Create order items from cart items
    const orderItems = cart.items.map((item) => ({
      _id: item._id,
      itemType: item.itemType,
      itemId: item.itemId,
      quantity: item.quantity,
      unitPrice: item.price,
      discountPrice: item.discountPrice,
      totalPrice: (item.discountPrice || item.price) * item.quantity,
      title: item.title,
      thumbnail: item.thumbnail,
    }));

    // Calculate totals
    const subtotal = orderItems.reduce(
      (total, item) => total + item.totalPrice,
      0,
    );
    const discount = 0; // Can be implemented later
    const tax = 0; // Can be implemented later
    const total = subtotal - discount + tax;

    // Create order
    const order = new this.orderModel({
      userId,
      items: orderItems,
      subtotal,
      discount,
      tax,
      total,
      status: OrderStatus.PENDING,
      paymentStatus: PaymentStatus.PENDING,
      metadata: createOrderDto.metadata,
    });

    await order.save();

    // Clear the cart after successful order creation
    await this.cartModel.findOneAndUpdate(
      { cartId, documentStatus: DocumentStatus.ACTIVE },
      {
        items: [],
        totalAmount: 0,
        totalItems: 0,
        lastUpdated: new Date(),
      },
    );

    return {
      success: true,
      message: 'Order created successfully',
      order: order.toObject(),
    };
  }

  async getOrder(
    userId: string,
    orderId: string,
  ): Promise<GetOrderResponseDto> {
    const order = await this.orderModel
      .findOne({
        _id: orderId,
        userId,
        documentStatus: DocumentStatus.ACTIVE,
      })
      .lean();

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    const orderDto: OrderDto = {
      _id: order._id.toString(),
      orderNumber: order.orderNumber,
      userId: order.userId.toString(),
      items: order.items.map((item) => ({
        ...item,
        itemId: item.itemId.toString(),
      })),
      subtotal: order.subtotal,
      discount: order.discount,
      tax: order.tax,
      total: order.total,
      status: order.status,
      paymentStatus: order.paymentStatus,
      paymentIntentId: order.paymentIntentId,
      paidAt: order.paidAt,
      completedAt: order.completedAt,
      createdAt: (order as any).createdAt || new Date(),
      updatedAt: (order as any).updatedAt || new Date(),
    };

    return {
      success: true,
      message: 'Order retrieved successfully',
      order: orderDto,
    };
  }

  async getUserOrders(
    userId: string,
    filterDto: OrderFilterDto,
  ): Promise<GetOrdersResponseDto> {
    const { status, paymentStatus, page = 1, limit = 10, from, to } = filterDto;

    const query: any = {
      userId,
      documentStatus: DocumentStatus.ACTIVE,
    };

    // Add filters
    if (status) query.status = status;
    if (paymentStatus) query.paymentStatus = paymentStatus;

    // Add date range filtering
    if (from || to) {
      query.createdAt = {};
      if (from) {
        const fromDate = new Date(from);
        fromDate.setHours(0, 0, 0, 0);
        query.createdAt.$gte = fromDate;
      }
      if (to) {
        const toDate = new Date(to);
        toDate.setHours(23, 59, 59, 999);
        query.createdAt.$lte = toDate;
      }
    }

    const skip = (page - 1) * limit;

    const [orders, total] = await Promise.all([
      this.orderModel
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      this.orderModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(total / limit);

    const orderDtos: OrderDto[] = orders.map((order) => ({
      _id: order._id.toString(),
      orderNumber: order.orderNumber,
      userId: order.userId.toString(),
      items: order.items.map((item) => ({
        ...item,
        itemId: item.itemId.toString(),
      })),
      subtotal: order.subtotal,
      discount: order.discount,
      tax: order.tax,
      total: order.total,
      status: order.status,
      paymentStatus: order.paymentStatus,
      paymentIntentId: order.paymentIntentId,
      paidAt: order.paidAt,
      completedAt: order.completedAt,
      createdAt: (order as any).createdAt || new Date(),
      updatedAt: (order as any).updatedAt || new Date(),
    }));

    return {
      success: true,
      message: 'Orders retrieved successfully',
      orders: orderDtos,
      total,
      page,
      totalPages,
    };
  }

  async updateOrderStatus(
    orderId: string,
    status: OrderStatus,
  ): Promise<OrderDocument> {
    const order = await this.orderModel.findByIdAndUpdate(
      orderId,
      {
        status,
        ...(status === OrderStatus.COMPLETED && { completedAt: new Date() }),
        ...(status === OrderStatus.CANCELLED && { cancelledAt: new Date() }),
      },
      { new: true },
    );

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    return order;
  }

  async updatePaymentStatus(
    orderId: string,
    paymentStatus: PaymentStatus,
    paymentData?: any,
  ): Promise<OrderDocument> {
    const updateData: any = { paymentStatus };

    if (paymentStatus === PaymentStatus.COMPLETED) {
      updateData.paidAt = new Date();
      updateData.status = OrderStatus.COMPLETED;
      updateData.completedAt = new Date();
    }

    if (paymentData) {
      updateData.paymentIntentId = paymentData.paymentIntentId;
      updateData.stripePaymentId = paymentData.stripePaymentId;
    }

    const order = await this.orderModel.findByIdAndUpdate(orderId, updateData, {
      new: true,
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // Create OrderItemDetails records when payment is successful
    if (paymentStatus === PaymentStatus.COMPLETED) {
      await this.createOrderItemDetails(order);
    }

    return order;
  }

  private async createOrderItemDetails(order: OrderDocument): Promise<void> {
    try {
      const orderItemDetailsPromises = order.items.map(async (item) => {
        // Fetch item details based on itemType
        let itemDetails: any;

        if (item.itemType === OrderItemType.COURSE) {
          itemDetails = await this.courseModel
            .findById(item.itemId)
            .select(
              'publicId createdBy creatorType title thumbnail shortDescription category subcategory',
            )
            .lean()
            .exec();
        } else if (item.itemType === OrderItemType.TEMPLATE) {
          itemDetails = await this.templateModel
            .findById(item.itemId)
            .select(
              'publicId createdBy creatorType title thumbnail shortDescription category subcategory',
            )
            .lean()
            .exec();
        }

        if (!itemDetails) {
          console.error(
            `Item not found for itemType: ${item.itemType}, itemId: ${item.itemId}`,
          );
          return;
        }

        // Create OrderItemDetails record
        const orderItemDetails = new this.orderItemDetailsModel({
          itemType: item.itemType,
          itemId: item.itemId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          publicId: itemDetails.publicId,
          createdBy: itemDetails.createdBy,
          creatorType: itemDetails.creatorType,
          orderedBy: order.userId,
          orderDate: new Date(),
          itemTitle: itemDetails.title,
          thumbnail: itemDetails.thumbnail,
          shortDescription: itemDetails.shortDescription,
          orderId: order.orderNumber,
          category: itemDetails.category,
          subCategory: itemDetails.subcategory,
        });

        return orderItemDetails.save();
      });

      await Promise.all(orderItemDetailsPromises);
    } catch (error) {
      console.error('Error creating OrderItemDetails:', error);
      // Don't throw error to avoid breaking the payment flow
      // Log the error for monitoring purposes
    }
  }

  async getOrderByPaymentIntent(
    paymentIntentId: string,
  ): Promise<OrderDocument | null> {
    return this.orderModel.findOne({ paymentIntentId });
  }

  async getOrderItems(
    userContext: { userId: string; role: UserRole },
    filterDto: OrderItemDetailsFilterDto,
  ): Promise<GetOrderItemDetailsResponseDto> {
    const {
      search,
      itemType,
      creatorType,
      category,
      subCategory,
      createdBy,
      orderedBy,
      from,
      to,
      page = 1,
      limit = 10,
      sortBy = 'orderDate',
      sortOrder = 'desc',
    } = filterDto;

    // Build query based on user role
    const query: any = {};

    // Role-based filtering
    if (userContext.role === UserRole.INSTRUCTOR) {
      // Instructors can only see items they created
      query.createdBy = userContext.userId;
    }
    // Admin can see all items (no additional filtering)

    // Apply filters
    if (search) {
      query.itemTitle = { $regex: search, $options: 'i' };
    }

    if (itemType) {
      query.itemType = itemType;
    }

    if (creatorType) {
      query.creatorType = creatorType;
    }

    if (category) {
      query.category = { $regex: category, $options: 'i' };
    }

    if (subCategory) {
      query.subCategory = { $regex: subCategory, $options: 'i' };
    }

    if (createdBy) {
      query.createdBy = createdBy;
    }

    if (orderedBy) {
      query.orderedBy = orderedBy;
    }

    // Date range filtering
    if (from || to) {
      query.orderDate = {};
      if (from) {
        const fromDate = new Date(from);
        fromDate.setHours(0, 0, 0, 0);
        query.orderDate.$gte = fromDate;
      }
      if (to) {
        const toDate = new Date(to);
        toDate.setHours(23, 59, 59, 999);
        query.orderDate.$lte = toDate;
      }
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    try {
      // Execute query with pagination
      const [items, total] = await Promise.all([
        this.orderItemDetailsModel
          .find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean()
          .exec(),
        this.orderItemDetailsModel.countDocuments(query),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        success: true,
        message: 'Order items retrieved successfully',
        data: items as any[],
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      console.error('Error fetching order items:', error);
      throw new BadRequestException('Failed to fetch order items');
    }
  }

  /**
   * Check if a user has purchased a specific item
   * @param userId - The user ID to check
   * @param itemId - The item ID to check
   * @param itemType - The type of item (COURSE or TEMPLATE)
   * @returns Promise<boolean> - true if purchased, false otherwise
   */
  async hasUserPurchasedItem(
    userId: string,
    itemId: string,
    itemType: OrderItemType,
  ): Promise<boolean> {
    if (!userId) {
      return false;
    }

    const purchase = await this.orderItemDetailsModel
      .findOne({
        orderedBy: userId,
        itemId: itemId,
        itemType: itemType,
      })
      .lean()
      .exec();

    return !!purchase;
  }

  async getUserPurchases(
    userId: string,
    filterDto: OrderItemDetailsFilterDto,
  ): Promise<GetOrderItemDetailsResponseDto> {
    const {
      search,
      itemType,
      creatorType,
      category,
      subCategory,
      from,
      to,
      page = 1,
      limit = 10,
      sortBy = 'orderDate',
      sortOrder = 'desc',
    } = filterDto;

    // Build query - always filter by the user's ID
    const query: any = {
      orderedBy: userId,
    };

    // Apply additional filters
    if (search) {
      query.itemTitle = { $regex: search, $options: 'i' };
    }

    if (itemType) {
      query.itemType = itemType;
    }

    if (creatorType) {
      query.creatorType = creatorType;
    }

    if (category) {
      query.category = { $regex: category, $options: 'i' };
    }

    if (subCategory) {
      query.subCategory = { $regex: subCategory, $options: 'i' };
    }

    // Date range filtering
    if (from || to) {
      query.orderDate = {};
      if (from) {
        const fromDate = new Date(from);
        fromDate.setHours(0, 0, 0, 0);
        query.orderDate.$gte = fromDate;
      }
      if (to) {
        const toDate = new Date(to);
        toDate.setHours(23, 59, 59, 999);
        query.orderDate.$lte = toDate;
      }
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    try {
      // Execute query with pagination
      const [items, total] = await Promise.all([
        this.orderItemDetailsModel
          .find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean()
          .exec(),
        this.orderItemDetailsModel.countDocuments(query),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        success: true,
        message: 'User purchases retrieved successfully',
        data: items as any[],
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      console.error('Error fetching user purchases:', error);
      throw new BadRequestException('Failed to fetch user purchases');
    }
  }
}
