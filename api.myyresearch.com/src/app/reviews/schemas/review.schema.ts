import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { User } from '../../users/schemas/user.schema';

export type ReviewDocument = Review & Document;

export enum ItemType {
  TEMPLATE = 'Template',
  COURSE = 'Course',
}

export enum ReviewStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Schema({ timestamps: true })
export class Review {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  user: MongooseSchema.Types.ObjectId;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: true,
    refPath: 'itemType',
  })
  item: MongooseSchema.Types.ObjectId;

  @Prop({ type: String, enum: ItemType, required: true })
  itemType: ItemType;

  @Prop({ type: Number, required: true, min: 1, max: 5 })
  rating: number;

  @Prop()
  comment: string;

  @Prop({ type: String, enum: ReviewStatus, default: ReviewStatus.PENDING })
  status: ReviewStatus;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User' })
  moderatedBy: User;
}

export const ReviewSchema = SchemaFactory.createForClass(Review);
