import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Review, ReviewDocument, ReviewStatus } from './schemas/review.schema';
import { ModerateReviewDto } from './dto/moderate-review.dto';
import { ReviewFilterDto } from './dto/review-filter.dto';
import { GetAllReviewsResponseDto } from './dto/get-all-reviews-response.dto';
import { MinimalReviewDto } from './dto/minimal-review.dto';
import { Course, CourseDocument } from '../courses/schemas/course.schema';
import {
  Template,
  TemplateDocument,
} from '../templates/schemas/template.schema';

@Injectable()
export class ReviewsService {
  constructor(
    @InjectModel(Review.name)
    private readonly reviewModel: Model<ReviewDocument>,
    @InjectModel(Course.name)
    private readonly courseModel: Model<CourseDocument>,
    @InjectModel(Template.name)
    private readonly templateModel: Model<TemplateDocument>,
  ) {}

  async create(createReviewDto: Partial<Review>): Promise<Review> {
    const createdReview = new this.reviewModel({
      ...createReviewDto,
      status: ReviewStatus.PENDING,
    });
    return createdReview.save();
  }

  async findAll(
    filterDto: ReviewFilterDto,
    auth?: { userId: string; role: string },
  ): Promise<GetAllReviewsResponseDto> {
    const { page = 1, limit = 10, search, itemType, status } = filterDto;
    const skip = (page - 1) * limit;

    const query: any = {};

    if (search) {
      query.comment = { $regex: search, $options: 'i' };
    }

    if (itemType) {
      query.itemType = itemType;
    }

    if (status) {
      query.status = status;
    }

    // If user is an instructor, only show reviews for their courses/templates
    if (auth?.role === 'instructor') {
      const itemIds = await this.getInstructorItemIds(auth.userId);
      query.item = { $in: itemIds };
    }

    const [reviews, total] = await Promise.all([
      this.reviewModel
        .find(query)
        .populate('user', 'firstName lastName email _id')
        .populate('moderatedBy', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean()
        .exec(),
      this.reviewModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(total / limit);
    const hasMore = page < totalPages;

    // Get all unique item IDs from reviews
    const courseIds = reviews
      .filter((review) => review.itemType === 'Course')
      .map((review) => review.item);
    const templateIds = reviews
      .filter((review) => review.itemType === 'Template')
      .map((review) => review.item);

    // Fetch course and template names in parallel
    const [courses, templates] = await Promise.all([
      courseIds.length > 0
        ? this.courseModel
            .find({ _id: { $in: courseIds } })
            .select('_id name')
            .lean()
            .exec()
        : [],
      templateIds.length > 0
        ? this.templateModel
            .find({ _id: { $in: templateIds } })
            .select('_id name')
            .lean()
            .exec()
        : [],
    ]);

    // Create maps for quick lookup
    const courseMap = new Map<string, string>(
      courses.map((course: any): [string, string] => [
        course._id.toString(),
        course.name as string,
      ]),
    );
    const templateMap = new Map<string, string>(
      templates.map((template: any): [string, string] => [
        template._id.toString(),
        template.name as string,
      ]),
    );

    const minimalReviews: MinimalReviewDto[] = reviews.map((review: any) => {
      const itemId = review.item.toString();
      const itemName =
        review.itemType === 'Course'
          ? courseMap.get(itemId) || 'Unknown Course'
          : templateMap.get(itemId) || 'Unknown Template';

      return {
        _id: review._id.toString(),
        user: review.user._id.toString(),
        itemId,
        itemName,
        itemType: review.itemType,
        rating: review.rating,
        comment: review.comment,
        status: review.status,
        createdAt: review.createdAt,
        updatedAt: review.updatedAt,
      };
    });

    return {
      reviews: minimalReviews,
      total,
      page,
      totalPages,
      hasMore,
    };
  }

  private async getInstructorItemIds(instructorId: string): Promise<string[]> {
    const [courses, templates] = await Promise.all([
      this.courseModel
        .find({ instructor: instructorId })
        .select('_id')
        .lean()
        .exec(),
      this.templateModel
        .find({ instructor: instructorId })
        .select('_id')
        .lean()
        .exec(),
    ]);

    return [
      ...courses.map((course) => course._id.toString()),
      ...templates.map((template) => template._id.toString()),
    ];
  }

  async findOne(id: string): Promise<Review> {
    const review = await this.reviewModel
      .findById(id)
      .populate('user')
      .populate('moderatedBy')
      .exec();

    if (!review) {
      throw new NotFoundException('Review not found');
    }

    return review;
  }

  async findByItem(itemId: string, itemType: string): Promise<Review[]> {
    return this.reviewModel
      .find({
        item: itemId,
        itemType,
        status: ReviewStatus.APPROVED,
      })
      .populate('user')
      .exec();
  }

  async findPendingReviews(): Promise<Review[]> {
    return this.reviewModel
      .find({ status: ReviewStatus.PENDING })
      .populate('user')
      .exec();
  }

  async moderateReview(
    id: string,
    moderateReviewDto: ModerateReviewDto,
    moderatorId: string,
  ): Promise<Review> {
    const review = await this.reviewModel.findById(id);
    if (!review) {
      throw new NotFoundException('Review not found');
    }

    const updatedReview = await this.reviewModel
      .findByIdAndUpdate(
        id,
        {
          status: moderateReviewDto.status,
          moderatedBy: moderatorId,
          moderationNotes: moderateReviewDto.moderationNotes,
          moderatedAt: new Date(),
        },
        { new: true },
      )
      .populate('user')
      .populate('moderatedBy')
      .exec();

    return updatedReview;
  }

  async update(id: string, updateReviewDto: Partial<Review>): Promise<Review> {
    const review = await this.reviewModel.findById(id);
    if (!review) {
      throw new NotFoundException('Review not found');
    }

    return this.reviewModel
      .findByIdAndUpdate(id, updateReviewDto, { new: true })
      .populate('user')
      .populate('moderatedBy')
      .exec();
  }

  async remove(id: string): Promise<Review> {
    const review = await this.reviewModel.findById(id);
    if (!review) {
      throw new NotFoundException('Review not found');
    }

    return this.reviewModel.findByIdAndDelete(id).exec();
  }
}
