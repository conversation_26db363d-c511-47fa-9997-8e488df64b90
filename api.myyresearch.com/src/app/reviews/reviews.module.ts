import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Review, ReviewSchema } from './schemas/review.schema';
import {
  ReviewsController,
  AdminReviewsController,
} from './reviews.controller';
import { ReviewsService } from './reviews.service';
import { Course, CourseSchema } from '../courses/schemas/course.schema';
import { Template, TemplateSchema } from '../templates/schemas/template.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Review.name, schema: ReviewSchema },
      { name: Course.name, schema: CourseSchema },
      { name: Template.name, schema: TemplateSchema },
    ]),
  ],
  controllers: [ReviewsController, AdminReviewsController],
  providers: [ReviewsService],
  exports: [ReviewsService],
})
export class ReviewsModule {}
