import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ReviewsService } from './reviews.service';
import { Review } from './schemas/review.schema';
import { ModerateReviewDto } from './dto/moderate-review.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../users/guards/roles.guard';
import { Roles } from '../users/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ReviewFilterDto } from './dto/review-filter.dto';
import { GetAllReviewsResponseDto } from './dto/get-all-reviews-response.dto';

@ApiTags('Reviews')
@Controller('reviews')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new review' })
  @ApiResponse({ status: 201, description: 'Review created successfully' })
  @Roles(UserRole.USER)
  create(@Body() createReviewDto: Partial<Review>, @Request() req) {
    return this.reviewsService.create({
      ...createReviewDto,
      user: req.user.userId,
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get all reviews (Admin, Staff, Instructor)' })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered reviews with pagination',
    type: GetAllReviewsResponseDto,
  })
  @Roles(UserRole.ADMIN, UserRole.STAFF, UserRole.INSTRUCTOR)
  findAll(
    @Query() filterDto: ReviewFilterDto,
    @Request() req,
  ): Promise<GetAllReviewsResponseDto> {
    return this.reviewsService.findAll(filterDto, {
      userId: req.user._id,
      role: req.user.role,
    });
  }

  @Get('item')
  @ApiOperation({ summary: 'Get reviews by item' })
  @ApiResponse({
    status: 200,
    description: 'Returns reviews for specific item',
  })
  findByItem(
    @Query('itemId') itemId: string,
    @Query('itemType') itemType: string,
  ) {
    return this.reviewsService.findByItem(itemId, itemType);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get review by ID' })
  @ApiResponse({ status: 200, description: 'Returns a single review' })
  findOne(@Param('id') id: string) {
    return this.reviewsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update review' })
  @ApiResponse({ status: 200, description: 'Review updated successfully' })
  @Roles(UserRole.USER)
  update(@Param('id') id: string, @Body() updateReviewDto: Partial<Review>) {
    // Only allow updating comment and rating
    const allowedUpdates = {
      comment: updateReviewDto.comment,
      rating: updateReviewDto.rating,
    };
    return this.reviewsService.update(id, allowedUpdates);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete review' })
  @ApiResponse({ status: 200, description: 'Review deleted successfully' })
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  remove(@Param('id') id: string) {
    return this.reviewsService.remove(id);
  }
}

@ApiTags('Admin Reviews')
@Controller('admin/reviews')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN, UserRole.STAFF)
@ApiBearerAuth()
export class AdminReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Get('pending')
  @ApiOperation({ summary: 'Get pending reviews' })
  @ApiResponse({ status: 200, description: 'Returns pending reviews' })
  findPendingReviews() {
    return this.reviewsService.findPendingReviews();
  }

  @Post(':id/moderate')
  @ApiOperation({ summary: 'Moderate a review' })
  @ApiResponse({ status: 200, description: 'Review moderated successfully' })
  moderateReview(
    @Param('id') id: string,
    @Body() moderateReviewDto: ModerateReviewDto,
    @Request() req,
  ) {
    return this.reviewsService.moderateReview(
      id,
      moderateReviewDto,
      req.user.userId,
    );
  }
}
