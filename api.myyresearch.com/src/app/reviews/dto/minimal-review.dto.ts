import { ApiProperty } from '@nestjs/swagger';
import { ItemType, ReviewStatus } from '../schemas/review.schema';

export class MinimalReviewDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  user: string;

  @ApiProperty()
  itemId: string;

  @ApiProperty()
  itemName: string;

  @ApiProperty({ enum: ItemType })
  itemType: ItemType;

  @ApiProperty()
  rating: number;

  @ApiProperty()
  comment: string;

  @ApiProperty({ enum: ReviewStatus })
  status: ReviewStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
