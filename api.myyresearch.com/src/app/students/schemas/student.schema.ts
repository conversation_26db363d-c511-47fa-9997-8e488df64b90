import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { User } from '../../users/schemas/user.schema';

export type StudentDocument = Student & Document;

export enum DocumentStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

export enum TaskType {
  ASSIGNMENT_HELP = 'assignment_help',
  RESEARCH_HELP = 'research_help',
}

export enum AcademicLevel {
  UNDERGRADUATE = 'undergraduate',
  POSTGRADUATE = 'postgraduate',
  PHD = 'phd',
}

export enum AssignmentType {
  ESSAY = 'essay',
  REPORT = 'report',
  CASE_STUDY = 'case_study',
  THESIS = 'thesis',
  PRESENTATION = 'presentation',
}

export enum FileFormat {
  WORD = 'word',
  POWERPOINT = 'powerpoint',
  PDF = 'pdf',
  EXCEL = 'excel',
}

export enum ResearchType {
  QUANTITATIVE = 'quantitative',
  QUALITATIVE = 'qualitative',
  MIXED_METHODS = 'mixed_methods',
}

export enum ResearchStage {
  IDEA_GENERATION = 'idea_generation',
  LITERATURE_REVIEW = 'literature_review',
  DATA_ANALYSIS = 'data_analysis',
  MANUSCRIPT_WRITING = 'manuscript_writing',
}

export enum ResearchSupport {
  RESEARCH_PROPOSAL = 'research_proposal',
  FULL_RESEARCH = 'full_research',
  PRESENTATION = 'presentation',
  RESEARCH_DESIGN = 'research_design',
  DATA_ANALYSIS = 'data_analysis',
  PAPER_PUBLICATION = 'paper_publication',
}

export enum ResearchTool {
  SPSS = 'spss',
  R = 'r',
  NVIVO = 'nvivo',
  MINITAB = 'minitab',
  EXCEL = 'excel',
  SMART_PLS = 'smart_pls',
  STAT = 'stat',
  EVIEWS = 'eviews',
}

@Schema({ _id: false })
export class AssignmentTask {
  @Prop({
    type: String,
    enum: TaskType,
    required: true,
    default: TaskType.ASSIGNMENT_HELP,
  })
  type: TaskType.ASSIGNMENT_HELP;

  @Prop({ type: [String], required: true })
  subjects: string[];

  @Prop({ type: String, enum: AssignmentType, required: true })
  assignmentType: AssignmentType;

  @Prop({ type: String, enum: AcademicLevel, required: true })
  academicLevel: AcademicLevel;

  @Prop({ type: String, enum: FileFormat, required: true })
  preferredFormat: FileFormat;

  @Prop({ type: Date, required: true })
  assignedDate: Date;
}

@Schema({ _id: false })
export class ResearchTask {
  @Prop({
    type: String,
    enum: TaskType,
    required: true,
    default: TaskType.RESEARCH_HELP,
  })
  type: TaskType.RESEARCH_HELP;

  @Prop({ required: true })
  researchTopic: string;

  @Prop({ type: String, required: true })
  researchType: string;

  @Prop({ required: true })
  fieldOfStudy: string;

  @Prop({ type: String, required: true })
  researchStage: string;

  @Prop({ type: String, required: true })
  requiredSupport: string;

  @Prop({ type: [String], required: true })
  toolsUsed: string[];

  @Prop({ type: Date, required: true })
  assignedDate: Date;
}

@Schema({ timestamps: true })
export class Student {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  user: User;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  idPassport: string;

  @Prop({ required: true })
  age: number;

  @Prop({ type: String, enum: Gender, required: true })
  gender: Gender;

  @Prop({ required: true })
  region: string;

  @Prop({ required: true })
  occupation: string;

  @Prop({ required: true })
  primaryReasons: string;

  @Prop({ required: true })
  email: string;

  @Prop({
    type: String,
    enum: DocumentStatus,
    default: DocumentStatus.ACTIVE,
    required: true,
  })
  documentStatus: DocumentStatus;

  @Prop({
    type: [
      {
        type: MongooseSchema.Types.Mixed,
        required: true,
        validate: {
          validator: function (v) {
            return v instanceof AssignmentTask || v instanceof ResearchTask;
          },
          message: 'Task must be either AssignmentTask or ResearchTask',
        },
      },
    ],
    required: true,
    _id: false,
  })
  tasks: (AssignmentTask | ResearchTask)[];
}

export const StudentSchema = SchemaFactory.createForClass(Student);
