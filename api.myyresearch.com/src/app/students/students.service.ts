import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Document } from 'mongoose';
import {
  Student,
  StudentDocument,
  DocumentStatus,
} from './schemas/student.schema';
import { CreateStudentDto } from './dto/create-student.dto';
import { StudentFilterDto } from './dto/student-filter.dto';
import {
  GetAllStudentsResponseDto,
  StudentListItemDto,
} from './dto/service-responses.dto';
import { FilterOperator, FilterType, JoinOperator } from './dto/filter.dto';

@Injectable()
export class StudentsService {
  constructor(
    @InjectModel(Student.name)
    private readonly studentModel: Model<StudentDocument>,
  ) {}

  async create(
    userId: string,
    createStudentDto: CreateStudentDto,
  ): Promise<Student> {
    const createdStudent = new this.studentModel({
      user: userId,
      ...createStudentDto,
      documentStatus: DocumentStatus.ACTIVE,
    });
    return createdStudent.save();
  }

  async findAll(
    filterDto: StudentFilterDto,
  ): Promise<GetAllStudentsResponseDto> {
    const {
      search,
      gender,
      region,
      occupation,
      page = 1,
      limit = 10,
      sort,
      filters,
      joinOperator = JoinOperator.AND,
      from,
      to,
    } = filterDto;

    const query: any = {
      documentStatus: DocumentStatus.ACTIVE,
    };

    // Add date range filtering
    if (from || to) {
      query.createdAt = {};
      if (from) {
        // Set time to start of day (00:00:00)
        const fromDate = new Date(from);
        fromDate.setHours(0, 0, 0, 0);
        query.createdAt.$gte = fromDate;
      }
      if (to) {
        // Set time to end of day (23:59:59.999)
        const toDate = new Date(to);
        toDate.setHours(23, 59, 59, 999);
        query.createdAt.$lte = toDate;
      }
    }

    // Handle basic filters
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    if (gender) query.gender = gender;
    if (region) query.region = region;
    if (occupation) query.occupation = occupation;

    // Handle advanced filters
    if (filters && filters.length > 0) {
      const filterConditions = filters.map((filter) => {
        const condition: any = {};

        switch (filter.operator) {
          case FilterOperator.ILIKE:
            condition[filter.id] = { $regex: filter.value, $options: 'i' };
            break;
          case FilterOperator.NOT_ILIKE:
            condition[filter.id] = {
              $not: { $regex: filter.value, $options: 'i' },
            };
            break;
          case FilterOperator.EQ:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $in: filter.value };
            } else {
              condition[filter.id] = filter.value;
            }
            break;
          case FilterOperator.NE:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $nin: filter.value };
            } else {
              condition[filter.id] = { $ne: filter.value };
            }
            break;
          case FilterOperator.IS_EMPTY:
            condition[filter.id] = { $exists: false };
            break;
          case FilterOperator.IS_NOT_EMPTY:
            condition[filter.id] = { $exists: true };
            break;
        }
        return condition;
      });

      if (filterConditions.length > 0) {
        const operator = joinOperator === JoinOperator.AND ? '$and' : '$or';
        query[operator] = filterConditions;
      }
    }

    const skip = (page - 1) * limit;

    // Create sort object for mongoose
    let sortCriteria = {};
    if (sort && Array.isArray(sort)) {
      sort.forEach((sortItem) => {
        sortCriteria[sortItem.id] = sortItem.desc ? -1 : 1;
      });
    } else {
      // Default sort by createdAt desc if no sort provided
      sortCriteria = { createdAt: -1 };
    }

    const [students, total] = await Promise.all([
      this.studentModel
        .find(query)
        .populate('user')
        .sort(sortCriteria)
        .skip(skip)
        .limit(limit)
        .lean(),
      this.studentModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(total / limit);

    const mappedStudents: StudentListItemDto[] = students.map(
      (student: Document & Student) => ({
        _id: student._id,
        name: student.name,
        email: student.email,
        age: student.age,
        gender: student.gender,
        region: student.region,
        occupation: student.occupation,
        documentStatus: student.documentStatus,
        createdAt: (student as any).createdAt,
      }),
    );

    return {
      students: mappedStudents,
      total,
      page,
      totalPages,
    };
  }

  async findOne(id: string): Promise<Student> {
    const student = await this.studentModel
      .findOne({ _id: id, documentStatus: DocumentStatus.ACTIVE })
      .populate('user')
      .exec();
    if (!student) {
      throw new NotFoundException('Student not found');
    }
    return student;
  }

  async findByUser(userId: string): Promise<Student> {
    const student = await this.studentModel
      .findOne({ user: userId, documentStatus: DocumentStatus.ACTIVE })
      .populate('user')
      .exec();
    if (!student) {
      throw new NotFoundException('Student not found');
    }
    return student;
  }

  async update(
    id: string,
    updateStudentDto: Partial<CreateStudentDto>,
  ): Promise<Student> {
    const student = await this.studentModel
      .findOneAndUpdate(
        { _id: id, documentStatus: DocumentStatus.ACTIVE },
        updateStudentDto,
        { new: true },
      )
      .populate('user')
      .exec();

    if (!student) {
      throw new NotFoundException('Student not found');
    }
    return student;
  }

  async remove(id: string): Promise<Student> {
    const student = await this.studentModel
      .findOneAndUpdate(
        { _id: id, documentStatus: DocumentStatus.ACTIVE },
        { documentStatus: DocumentStatus.ARCHIVED },
        { new: true },
      )
      .exec();

    if (!student) {
      throw new NotFoundException('Student not found');
    }
    return student;
  }
}
