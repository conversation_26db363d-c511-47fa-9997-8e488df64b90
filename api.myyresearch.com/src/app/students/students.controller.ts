import {
  Controller,
  Get,
  Post,
  Body,
  Delete,
  UseGuards,
  Request,
  Query,
  Param,
  Patch,
} from '@nestjs/common';
import { StudentsService } from './students.service';
import { CreateStudentDto } from './dto/create-student.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { StudentFilterDto } from './dto/student-filter.dto';
import {
  GetAllStudentsResponseDto,
  StudentResponseDto,
} from './dto/service-responses.dto';

@ApiTags('students')
@Controller('students')
@UseGuards(JwtAuthGuard, RolesGuard)
export class StudentsController {
  constructor(private readonly studentsService: StudentsService) {}

  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create new student (Admin and Staff only)' })
  @ApiResponse({
    status: 201,
    description: 'Student created successfully',
    type: StudentResponseDto,
  })
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  create(@Body() createStudentDto: CreateStudentDto, @Request() req) {
    return this.studentsService.create(
      req.user._id.toString(),
      createStudentDto,
    );
  }

  @Get()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all students (Admin and Staff only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered students with pagination',
    type: GetAllStudentsResponseDto,
  })
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  findAll(@Query() filterDto: StudentFilterDto) {
    return this.studentsService.findAll(filterDto);
  }

  @Get('me')
  @Roles(UserRole.USER)
  findMe(@Request() req) {
    return this.studentsService.findByUser(req.user._id.toString());
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get student by ID (Admin and Staff only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns student details',
    type: StudentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Student not found' })
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  findOne(@Param('id') id: string) {
    return this.studentsService.findOne(id);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  remove(@Param('id') id: string) {
    return this.studentsService.remove(id);
  }

  @Get('user/:userId')
  @UseGuards(JwtAuthGuard)
  findByUser(@Param('userId') userId: string) {
    return this.studentsService.findByUser(userId);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update student (Admin and Staff only)' })
  @ApiResponse({
    status: 200,
    description: 'Student updated successfully',
    type: StudentResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Student not found' })
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  update(
    @Param('id') id: string,
    @Body() updateStudentDto: Partial<CreateStudentDto>,
  ) {
    return this.studentsService.update(id, updateStudentDto);
  }
}
