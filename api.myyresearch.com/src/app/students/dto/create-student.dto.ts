import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsEnum,
  IsEmail,
  IsArray,
  ValidateNested,
  Min,
  Max,
  IsNotEmpty,
  IsDate,
} from 'class-validator';
import { Type } from 'class-transformer';
import { Gender, TaskType } from '../schemas/student.schema';

export class AssignmentTaskDto {
  @IsString()
  @IsEnum(TaskType)
  type: string = TaskType.ASSIGNMENT_HELP;

  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  subjects: string[];

  @IsString()
  assignmentType: string;

  @IsString()
  academicLevel: string;

  @IsString()
  preferredFormat: string;

  @IsDate()
  @Type(() => Date)
  assignedDate: Date;
}

export class ResearchTaskDto {
  @IsString()
  @IsEnum(TaskType)
  type: string = TaskType.RESEARCH_HELP;

  @IsString()
  @IsNotEmpty()
  researchTopic: string;

  @IsString()
  @IsNotEmpty()
  researchType: string;

  @IsString()
  @IsNotEmpty()
  fieldOfStudy: string;

  @IsString()
  @IsNotEmpty()
  researchStage: string;

  @IsString()
  @IsNotEmpty()
  requiredSupport: string;

  @IsArray()
  @IsNotEmpty({ each: true })
  toolsUsed: string[];

  @IsDate()
  @Type(() => Date)
  assignedDate: Date;
}

export class CreateStudentDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  idPassport: string;

  @IsNumber()
  @Min(16)
  @Max(100)
  age: number;

  @IsEnum(Gender)
  gender: Gender;

  @IsString()
  @IsNotEmpty()
  region: string;

  @IsString()
  @IsNotEmpty()
  occupation: string;

  @IsString()
  @IsNotEmpty()
  primaryReasons: string;

  @IsEmail()
  email: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AssignmentTaskDto, {
    discriminator: {
      property: 'type',
      subTypes: [
        { value: ResearchTaskDto, name: 'research_help' },
        { value: AssignmentTaskDto, name: 'assignment_help' },
      ],
    },
  })
  @IsNotEmpty()
  tasks: (AssignmentTaskDto | ResearchTaskDto)[];
}
