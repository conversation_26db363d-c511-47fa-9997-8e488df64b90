import { ApiProperty } from '@nestjs/swagger';
import {
  DocumentStatus,
  Gender,
  TaskType,
  AssignmentType,
  AcademicLevel,
  FileFormat,
} from '../schemas/student.schema';

// DTO for list view
export class StudentListItemDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  age: number;

  @ApiProperty()
  gender: string;

  @ApiProperty()
  region: string;

  @ApiProperty()
  occupation: string;

  @ApiProperty({ enum: DocumentStatus })
  documentStatus: DocumentStatus;

  @ApiProperty()
  createdAt: Date;
}

// DTOs for tasks
export class AssignmentTaskDto {
  @ApiProperty({ enum: TaskType })
  type: TaskType;

  @ApiProperty({ type: [String] })
  subjects: string[];

  @ApiProperty({ enum: AssignmentType })
  assignmentType: AssignmentType;

  @ApiProperty({ enum: AcademicLevel })
  academicLevel: AcademicLevel;

  @ApiProperty({ enum: FileFormat })
  preferredFormat: FileFormat;

  @ApiProperty()
  assignedDate: Date;
}

export class ResearchTaskDto {
  @ApiProperty({ enum: TaskType })
  type: TaskType;

  @ApiProperty()
  researchTopic: string;

  @ApiProperty()
  researchType: string;

  @ApiProperty()
  fieldOfStudy: string;

  @ApiProperty()
  researchStage: string;

  @ApiProperty()
  requiredSupport: string;

  @ApiProperty({ type: [String] })
  toolsUsed: string[];

  @ApiProperty()
  assignedDate: Date;
}

// DTO for detailed student view
export class StudentResponseDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  age: number;

  @ApiProperty({ enum: Gender })
  gender: Gender;

  @ApiProperty()
  region: string;

  @ApiProperty()
  occupation: string;

  @ApiProperty()
  primaryReasons: string;

  @ApiProperty()
  idPassport: string;

  @ApiProperty({ enum: DocumentStatus })
  documentStatus: DocumentStatus;

  @ApiProperty({
    type: [Object],
    description: 'Array of AssignmentTask or ResearchTask',
  })
  tasks: (AssignmentTaskDto | ResearchTaskDto)[];

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ type: Object, description: 'User reference' })
  user: any;
}

// DTO for list response
export class GetAllStudentsResponseDto {
  @ApiProperty({ type: [StudentListItemDto] })
  students: StudentListItemDto[];

  @ApiProperty({ example: 100 })
  total: number;

  @ApiProperty({ example: 1 })
  page: number;

  @ApiProperty({ example: 10 })
  totalPages: number;
}
