import { IsString, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum FilterOperator {
  ILIKE = 'iLike',
  NOT_ILIKE = 'notILike',
  EQ = 'eq',
  NE = 'ne',
  IS_EMPTY = 'isEmpty',
  IS_NOT_EMPTY = 'isNotEmpty',
}

export enum FilterType {
  TEXT = 'text',
  MULTI_SELECT = 'multi-select',
}

export enum JoinOperator {
  AND = 'and',
  OR = 'or',
}

export class FilterItemDto {
  @ApiProperty({
    description: 'Field name to filter by',
    example: 'email',
    enum: [
      'name',
      'email',
      'age',
      'gender',
      'region',
      'occupation',
      'createdAt',
      'updatedAt',
    ],
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Filter value (string or array for multi-select)',
    example: '<EMAIL>',
    required: false,
  })
  value: string | string[];

  @ApiProperty({
    description: 'Type of filter field',
    enum: FilterType,
    example: FilterType.TEXT,
  })
  @IsEnum(FilterType)
  type: FilterType;

  @ApiProperty({
    description: 'Filter operator',
    enum: FilterOperator,
    example: FilterOperator.ILIKE,
  })
  @IsEnum(FilterOperator)
  operator: FilterOperator;

  @ApiProperty({
    description: 'Unique identifier for the filter row',
    example: 'PfsMrx',
  })
  @IsString()
  rowId: string;
}
