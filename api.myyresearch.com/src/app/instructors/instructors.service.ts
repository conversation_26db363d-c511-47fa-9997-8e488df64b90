import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Instructor, InstructorDocument } from './schemas/instructor.schema';
import { CompleteProfileDto } from './dto/complete-profile.dto';
import { UsersService } from '../users/users.service';
import { CreateInstructorDto } from './dto/create-instructor.dto';
import { UserRole } from '../users/schemas/user.schema';
import { InstructorDetailDto } from './dto/instructor-detail.dto';
import { InstructorFilterDto } from './dto/instructor-filter.dto';
import { GetAllInstructorsResponseDto } from './dto/get-all-instructors-response.dto';
import { GetInstructorCoursesResponseDto } from './dto/instructor-courses-response.dto';
import { GetInstructorTemplatesResponseDto } from './dto/instructor-templates-response.dto';
import { JoinOperator } from '../users/dto/filter.dto';
import { Course, CourseDocument } from '../courses/schemas/course.schema';
import {
  Template,
  TemplateDocument,
} from '../templates/schemas/template.schema';
import {
  Enrollment,
  EnrollmentDocument,
} from '../enrollments/schemas/enrollment.schema';
import {
  TemplateEnrollment,
  TemplateEnrollmentDocument,
} from '../template-enrollments/schemas/template-enrollment.schema';
import { CreatorType as CourseCreatorType } from '../courses/schemas/course.schema';
import { CreatorType as TemplateCreatorType } from '../templates/schemas/template.schema';
import { DocumentStatus } from '../courses/schemas/course.schema';
import { FilterOperator } from '../users/dto/filter.dto';

@Injectable()
export class InstructorsService {
  constructor(
    @InjectModel(Instructor.name)
    private readonly instructorModel: Model<InstructorDocument>,
    @InjectModel(Course.name)
    private readonly courseModel: Model<CourseDocument>,
    @InjectModel(Template.name)
    private readonly templateModel: Model<TemplateDocument>,
    @InjectModel(Enrollment.name)
    private readonly enrollmentModel: Model<EnrollmentDocument>,
    @InjectModel(TemplateEnrollment.name)
    private readonly templateEnrollmentModel: Model<TemplateEnrollmentDocument>,
    private readonly usersService: UsersService,
  ) {}

  async completeProfile(
    userId: string,
    completeProfileDto: CompleteProfileDto,
  ): Promise<Instructor> {
    // Check if profile already exists
    const existingProfile = await this.instructorModel.findOne({
      user: userId,
    });
    if (existingProfile) {
      throw new BadRequestException('Instructor profile already exists');
    }

    const instructor = await this.instructorModel.create({
      user: userId,
      ...completeProfileDto,
    });

    return instructor.populate('user');
  }

  async updateProfile(
    userId: string,
    updateProfileDto: Partial<CompleteProfileDto>,
  ): Promise<Instructor> {
    const instructor = await this.instructorModel.findOne({ user: userId });
    if (!instructor) {
      throw new NotFoundException('Instructor profile not found');
    }

    Object.assign(instructor, updateProfileDto);
    await instructor.save();

    return instructor.populate('user');
  }

  async findAll(): Promise<Instructor[]> {
    return this.instructorModel.find().populate('user').exec();
  }

  async findOne(id: string): Promise<Instructor> {
    const instructor = await this.instructorModel
      .findById(id)
      .populate('user')
      .exec();

    if (!instructor) {
      throw new NotFoundException('Instructor not found');
    }

    return instructor;
  }

  async findByUser(userId: string): Promise<Instructor> {
    const instructor = await this.instructorModel
      .findOne({ user: userId })
      .populate('user')
      .exec();

    if (!instructor) {
      throw new NotFoundException('Instructor profile not found');
    }

    return instructor;
  }

  async create(createInstructorDto: CreateInstructorDto): Promise<Instructor> {
    // Create user first
    const user = await this.usersService.create({
      firstName: createInstructorDto.firstName,
      lastName: createInstructorDto.lastName,
      email: createInstructorDto.email,
      password: createInstructorDto.password,
      role: UserRole.INSTRUCTOR,
    });

    // Create instructor profile
    const instructor = await this.instructorModel.create({
      user: user._id,
    });

    return instructor.populate('user');
  }

  async findAllMinimal(
    filterDto: InstructorFilterDto,
  ): Promise<GetAllInstructorsResponseDto> {
    const {
      search,
      page = 1,
      limit = 10,
      sort,
      filters,
      joinOperator = JoinOperator.AND,
      from,
      to,
    } = filterDto;

    const query: any = {};

    // Add date range filtering
    if (from || to) {
      query.createdAt = {};
      if (from) {
        const fromDate = new Date(from);
        fromDate.setHours(0, 0, 0, 0);
        query.createdAt.$gte = fromDate;
      }
      if (to) {
        const toDate = new Date(to);
        toDate.setHours(23, 59, 59, 999);
        query.createdAt.$lte = toDate;
      }
    }

    // Handle search
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { 'user.email': { $regex: search, $options: 'i' } },
      ];
    }

    // Handle advanced filters
    if (filters && filters.length > 0) {
      const filterConditions = filters.map((filterItem) => {
        const { id, operator, value } = filterItem;
        switch (operator) {
          case FilterOperator.EQ:
            return { [id]: value };
          case FilterOperator.ILIKE:
            return { [id]: { $regex: value, $options: 'i' } };
          case FilterOperator.NOT_ILIKE:
            return { [id]: { $not: { $regex: value, $options: 'i' } } };
          case FilterOperator.NE:
            return { [id]: { $ne: value } };
          case FilterOperator.IS_EMPTY:
            return { [id]: { $exists: false } };
          case FilterOperator.IS_NOT_EMPTY:
            return { [id]: { $exists: true } };
          default:
            return {};
        }
      });

      if (filterConditions.length > 0) {
        const operator = joinOperator === JoinOperator.AND ? '$and' : '$or';
        query[operator] = filterConditions;
      }
    }

    const skip = (page - 1) * limit;

    // Create sort object
    let sortCriteria = {};
    if (sort && Array.isArray(sort)) {
      sort.forEach((sortItem) => {
        sortCriteria[sortItem.id] = sortItem.desc ? -1 : 1;
      });
    } else {
      sortCriteria = { createdAt: -1 };
    }

    const [instructors, total] = await Promise.all([
      this.instructorModel
        .find(query)
        .select({
          firstName: 1,
          middleName: 1,
          lastName: 1,
          'address.country': 1,
          'address.city': 1,
          'profileImage.url': 1,
        })
        .populate('user', 'email')
        .sort(sortCriteria)
        .skip(skip)
        .limit(limit)
        .lean()
        .exec(),
      this.instructorModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      instructors: instructors.map((instructor) => ({
        id: instructor._id,
        firstName: instructor.firstName,
        middleName: instructor.middleName,
        lastName: instructor.lastName,
        email: instructor.user['email'],
        country: instructor.address?.country,
        city: instructor.address?.city,
        profileImageKey: instructor.profileImage?.key,
      })),
      total,
      page,
      totalPages,
    };
  }

  async findOneDetailed(id: string): Promise<InstructorDetailDto> {
    const instructor = await this.instructorModel
      .findById(id)
      .select({
        firstName: 1,
        middleName: 1,
        lastName: 1,
        profileImage: 1,
        dateOfBirth: 1,
        gender: 1,
        governmentId: 1,
        qualifications: 1,
        areasOfExpertise: 1,
        teachingSubjects: 1,
        address: 1,
        createdAt: 1,
        updatedAt: 1,
      })
      .populate('user', 'email')
      .lean()
      .exec();

    if (!instructor) {
      throw new NotFoundException('Instructor not found');
    }

    return {
      id: instructor._id,
      email: instructor.user['email'],
      firstName: instructor.firstName,
      middleName: instructor.middleName,
      lastName: instructor.lastName,
      profileImage: instructor.profileImage,
      dateOfBirth: instructor.dateOfBirth,
      gender: instructor.gender,
      governmentId: {
        documentType: instructor.governmentId.documentType,
        number: instructor.governmentId.number,
        frontImage: instructor.governmentId.frontImage,
        backImage: instructor.governmentId.backImage,
        expiryDate: instructor.governmentId.expiryDate,
      },
      qualifications: instructor.qualifications.map((q) => ({
        degree: q.degree,
        institution: q.institution,
        fieldOfStudy: q.fieldOfStudy,
        yearCompleted: q.yearCompleted,
        certificate: q.certificate,
      })),
      areasOfExpertise: instructor.areasOfExpertise,
      teachingSubjects: instructor.teachingSubjects,
      address: {
        streetAddress: instructor.address.streetAddress,
        streetAddress2: instructor.address.streetAddress2,
        city: instructor.address.city,
        state: instructor.address.state,
        country: instructor.address.country,
        postalCode: instructor.address.postalCode,
        timezone: instructor.address.timezone,
      },
      createdAt: instructor.createdAt,
      updatedAt: instructor.updatedAt,
    };
  }

  async findByUserDetailed(userId: string): Promise<InstructorDetailDto> {
    const instructor = await this.instructorModel
      .findOne({ user: userId })
      .select({
        firstName: 1,
        middleName: 1,
        lastName: 1,
        profileImage: 1,
        dateOfBirth: 1,
        gender: 1,
        governmentId: 1,
        qualifications: 1,
        areasOfExpertise: 1,
        teachingSubjects: 1,
        address: 1,
        createdAt: 1,
        updatedAt: 1,
      })
      .populate('user', 'email')
      .lean()
      .exec();

    if (!instructor) {
      throw new NotFoundException('Instructor profile not found');
    }

    return {
      id: instructor._id,
      email: instructor.user['email'],
      firstName: instructor.firstName,
      middleName: instructor.middleName,
      lastName: instructor.lastName,
      profileImage: instructor.profileImage,
      dateOfBirth: instructor.dateOfBirth,
      gender: instructor.gender,
      governmentId: {
        documentType: instructor.governmentId.documentType,
        number: instructor.governmentId.number,
        frontImage: instructor.governmentId.frontImage,
        backImage: instructor.governmentId.backImage,
        expiryDate: instructor.governmentId.expiryDate,
      },
      qualifications: instructor.qualifications.map((q) => ({
        degree: q.degree,
        institution: q.institution,
        fieldOfStudy: q.fieldOfStudy,
        yearCompleted: q.yearCompleted,
        certificate: q.certificate,
      })),
      areasOfExpertise: instructor.areasOfExpertise,
      teachingSubjects: instructor.teachingSubjects,
      address: {
        streetAddress: instructor.address.streetAddress,
        streetAddress2: instructor.address.streetAddress2,
        city: instructor.address.city,
        state: instructor.address.state,
        country: instructor.address.country,
        postalCode: instructor.address.postalCode,
        timezone: instructor.address.timezone,
      },
      createdAt: instructor.createdAt,
      updatedAt: instructor.updatedAt,
    };
  }

  async getInstructorCoursesWithEnrollments(
    instructorId: string,
  ): Promise<GetInstructorCoursesResponseDto> {
    const instructor = await this.findOne(instructorId);

    const courses = await this.courseModel.aggregate([
      {
        $match: {
          createdBy: instructor.user['_id'],
          creatorType: CourseCreatorType.INSTRUCTOR,
          documentStatus: DocumentStatus.ACTIVE,
        },
      },
      {
        $lookup: {
          from: 'enrollments',
          localField: '_id',
          foreignField: 'course',
          as: 'enrollments',
        },
      },
      {
        $project: {
          _id: 1,
          publicId: 1,
          title: 1,
          shortDescription: 1,
          thumbnail: 1,
          price: 1,
          discountPrice: 1,
          status: 1,
          approvalStatus: 1,
          createdAt: 1,
          enrollmentCount: { $size: '$enrollments' },
        },
      },
    ]);

    return { courses };
  }

  async getInstructorTemplatesWithEnrollments(
    instructorId: string,
  ): Promise<GetInstructorTemplatesResponseDto> {
    const instructor = await this.findOne(instructorId);

    const templates = await this.templateModel.aggregate([
      {
        $match: {
          createdBy: instructor.user['_id'],
          creatorType: TemplateCreatorType.INSTRUCTOR,
          documentStatus: DocumentStatus.ACTIVE,
        },
      },
      {
        $lookup: {
          from: 'templateenrollments',
          localField: '_id',
          foreignField: 'template',
          as: 'enrollments',
        },
      },
      {
        $project: {
          _id: 1,
          publicId: 1,
          title: 1,
          shortDescription: 1,
          thumbnail: 1,
          price: 1,
          discountPrice: 1,
          status: 1,
          approvalStatus: 1,
          createdAt: 1,
          enrollmentCount: { $size: '$enrollments' },
        },
      },
    ]);

    return { templates };
  }
}
