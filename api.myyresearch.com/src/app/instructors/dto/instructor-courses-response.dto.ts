import { ApiProperty } from '@nestjs/swagger';
import { UploadedFile } from '../../templates/schemas/template.schema';
import {
  CourseStatus,
  CourseApprovalStatus,
} from '../../courses/schemas/course.schema';

export class InstructorCourseDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  publicId: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  shortDescription: string;

  @ApiProperty()
  thumbnail: UploadedFile;

  @ApiProperty()
  price: number;

  @ApiProperty()
  discountPrice?: number;

  @ApiProperty({ enum: CourseStatus })
  status: CourseStatus;

  @ApiProperty({ enum: CourseApprovalStatus })
  approvalStatus: CourseApprovalStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  enrollmentCount: number;
}

export class GetInstructorCoursesResponseDto {
  @ApiProperty({ type: [InstructorCourseDto] })
  courses: InstructorCourseDto[];
}
