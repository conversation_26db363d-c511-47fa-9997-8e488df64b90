import {
  IsString,
  IsEnum,
  IsDateString,
  IsArray,
  ValidateNested,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';
import { Gender, DocumentType } from '../schemas/instructor.schema';
import { UploadedFile } from '../../templates/schemas/template.schema';

export class GovernmentIdDto {
  @IsEnum(DocumentType)
  documentType: DocumentType;

  @IsString()
  number: string;

  @IsOptional()
  @IsString()
  frontImage?: UploadedFile;

  @IsOptional()
  @IsString()
  backImage?: UploadedFile;

  @IsOptional()
  @IsDateString()
  expiryDate?: Date;
}

export class QualificationDto {
  @IsString()
  degree: string;

  @IsString()
  institution: string;

  @IsString()
  fieldOfStudy: string;

  @IsString()
  yearCompleted: number;

  @IsOptional()
  @IsString()
  certificate?: UploadedFile;
}

export class AddressDto {
  @IsString()
  streetAddress: string;

  @IsOptional()
  @IsString()
  streetAddress2?: string;

  @IsString()
  city: string;

  @IsString()
  state: string;

  @IsString()
  country: string;

  @IsOptional()
  @IsString()
  postalCode?: string;

  @IsString()
  timezone: string;
}

export class CompleteProfileDto {
  @IsOptional()
  profileImage?: UploadedFile;

  @IsString()
  firstName: string;

  @IsOptional()
  @IsString()
  middleName?: string;

  @IsString()
  lastName: string;

  @IsDateString()
  dateOfBirth: Date;

  @IsEnum(Gender)
  gender: Gender;

  @ValidateNested()
  @Type(() => GovernmentIdDto)
  governmentId: GovernmentIdDto;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QualificationDto)
  qualifications: QualificationDto[];

  @IsArray()
  @IsString({ each: true })
  areasOfExpertise: string[];

  @IsArray()
  @IsString({ each: true })
  teachingSubjects: string[];

  @ValidateNested()
  @Type(() => AddressDto)
  address: AddressDto;
}
