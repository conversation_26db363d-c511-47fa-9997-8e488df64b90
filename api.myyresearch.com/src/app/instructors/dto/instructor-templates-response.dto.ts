import { ApiProperty } from '@nestjs/swagger';
import {
  UploadedFile,
  TemplateStatus,
} from '../../templates/schemas/template.schema';
import { TemplateApprovalStatus } from '../../templates/dto/approve-template.dto';

export class InstructorTemplateDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  publicId: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  shortDescription: string;

  @ApiProperty()
  thumbnail: UploadedFile;

  @ApiProperty()
  price: number;

  @ApiProperty()
  discountPrice?: number;

  @ApiProperty({ enum: TemplateStatus })
  status: TemplateStatus;

  @ApiProperty({ enum: TemplateApprovalStatus })
  approvalStatus: TemplateApprovalStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  enrollmentCount: number;
}

export class GetInstructorTemplatesResponseDto {
  @ApiProperty({ type: [InstructorTemplateDto] })
  templates: InstructorTemplateDto[];
}
