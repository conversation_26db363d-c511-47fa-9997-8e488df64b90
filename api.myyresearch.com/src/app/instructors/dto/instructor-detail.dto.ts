import { Gender, DocumentType } from '../schemas/instructor.schema';
import { UploadedFile } from '../../templates/schemas/template.schema';

export class GovernmentIdDetailDto {
  documentType: DocumentType;
  number: string;
  frontImage?: UploadedFile;
  backImage?: UploadedFile;
  expiryDate?: Date;
}

export class QualificationDetailDto {
  degree: string;
  institution: string;
  fieldOfStudy: string;
  yearCompleted: number;
  certificate?: UploadedFile;
}

export class AddressDetailDto {
  streetAddress: string;
  streetAddress2?: string;
  city: string;
  state: string;
  country: string;
  postalCode?: string;
  timezone: string;
}

export class InstructorDetailDto {
  id: string;
  email: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  profileImage?: UploadedFile;
  dateOfBirth: Date;
  gender: Gender;
  governmentId: GovernmentIdDetailDto;
  qualifications: QualificationDetailDto[];
  areasOfExpertise: string[];
  teachingSubjects: string[];
  address: AddressDetailDto;
  createdAt: Date;
  updatedAt: Date;
}
