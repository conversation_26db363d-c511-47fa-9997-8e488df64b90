import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { User } from '../../users/schemas/user.schema';
import { UploadedFile } from '../../templates/schemas/template.schema';

export type InstructorDocument = Instructor & Document;

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

export enum DocumentType {
  PASSPORT = 'passport',
  NATIONAL_ID = 'national-id',
  DRIVING_LICENSE = 'driving-license',
}

@Schema()
export class GovernmentId {
  @Prop({ type: String, enum: DocumentType, required: true })
  documentType: DocumentType;

  @Prop({ required: true })
  number: string;

  @Prop({ type: UploadedFile })
  frontImage: UploadedFile;

  @Prop({ type: UploadedFile })
  backImage: UploadedFile;

  @Prop()
  expiryDate: Date;
}

@Schema()
export class Qualification {
  @Prop()
  degree: string;

  @Prop()
  institution: string;

  @Prop()
  fieldOfStudy: string;

  @Prop()
  yearCompleted: number;

  @Prop({ type: UploadedFile })
  certificate: UploadedFile;
}

@Schema()
export class Address {
  @Prop({ required: true })
  streetAddress: string;

  @Prop()
  streetAddress2: string;

  @Prop({ required: true })
  city: string;

  @Prop({ required: true })
  state: string;

  @Prop({ required: true })
  country: string;

  @Prop()
  postalCode: string;

  @Prop({ required: true })
  timezone: string;
}

@Schema({ timestamps: true })
export class Instructor {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
  })
  user: User;

  // profile image
  @Prop({ type: UploadedFile })
  profileImage: UploadedFile;

  @Prop({ required: true })
  firstName: string;

  @Prop()
  middleName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ required: true })
  dateOfBirth: Date;

  @Prop({ type: String, enum: Gender, required: true })
  gender: Gender;

  @Prop({ type: GovernmentId, required: true })
  governmentId: GovernmentId;

  @Prop({ type: [Qualification] })
  qualifications: Qualification[];

  @Prop({ type: [String], required: true })
  areasOfExpertise: string[];

  @Prop({ type: [String], required: true })
  teachingSubjects: string[];

  @Prop({ type: Address, required: true })
  address: Address;

  createdAt: Date;
  updatedAt: Date;
}

export const InstructorSchema = SchemaFactory.createForClass(Instructor);
