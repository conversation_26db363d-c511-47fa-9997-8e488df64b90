import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Instructor, InstructorSchema } from './schemas/instructor.schema';
import { InstructorsController } from './instructors.controller';
import { InstructorsService } from './instructors.service';
import { UsersModule } from '../users/users.module';
import { Course, CourseSchema } from '../courses/schemas/course.schema';
import { Template, TemplateSchema } from '../templates/schemas/template.schema';
import {
  Enrollment,
  EnrollmentSchema,
} from '../enrollments/schemas/enrollment.schema';
import {
  TemplateEnrollment,
  TemplateEnrollmentSchema,
} from '../template-enrollments/schemas/template-enrollment.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Instructor.name, schema: InstructorSchema },
      { name: Course.name, schema: CourseSchema },
      { name: Template.name, schema: TemplateSchema },
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: TemplateEnrollment.name, schema: TemplateEnrollmentSchema },
    ]),
    UsersModule,
  ],
  controllers: [InstructorsController],
  providers: [InstructorsService],
  exports: [InstructorsService],
})
export class InstructorsModule {}
