import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { InstructorsService } from './instructors.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../users/guards/roles.guard';
import { Roles } from '../users/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import { CompleteProfileDto } from './dto/complete-profile.dto';
import { InstructorDetailDto } from './dto/instructor-detail.dto';
import { InstructorFilterDto } from './dto/instructor-filter.dto';
import { GetAllInstructorsResponseDto } from './dto/get-all-instructors-response.dto';
import { GetInstructorCoursesResponseDto } from './dto/instructor-courses-response.dto';
import { GetInstructorTemplatesResponseDto } from './dto/instructor-templates-response.dto';
import { ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@Controller('instructors')
@UseGuards(JwtAuthGuard, RolesGuard)
export class InstructorsController {
  constructor(private readonly instructorsService: InstructorsService) {}

  @Post('profile')
  @Roles(UserRole.INSTRUCTOR)
  completeProfile(
    @Request() req,
    @Body() completeProfileDto: CompleteProfileDto,
  ) {
    return this.instructorsService.completeProfile(
      req.user._id,
      completeProfileDto,
    );
  }

  @Get('me')
  @Roles(UserRole.INSTRUCTOR)
  getOwnProfile(@Request() req): Promise<InstructorDetailDto> {
    return this.instructorsService.findByUserDetailed(req.user._id);
  }

  @Patch('me')
  @Roles(UserRole.INSTRUCTOR)
  updateProfile(
    @Request() req,
    @Body() updateProfileDto: Partial<CompleteProfileDto>,
  ) {
    return this.instructorsService.updateProfile(
      req.user._id,
      updateProfileDto,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all instructors (Admin/Staff only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered instructors with pagination',
    type: GetAllInstructorsResponseDto,
  })
  @Get('admin')
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  findAll(
    @Query() filterDto: InstructorFilterDto,
  ): Promise<GetAllInstructorsResponseDto> {
    return this.instructorsService.findAllMinimal(filterDto);
  }

  @Get('admin/:id')
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  findOne(@Param('id') id: string): Promise<InstructorDetailDto> {
    return this.instructorsService.findOneDetailed(id);
  }

  @ApiOperation({
    summary:
      'Get courses created by instructor with enrollment counts (Admin/Staff only)',
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns courses created by instructor with their enrollment counts',
    type: GetInstructorCoursesResponseDto,
  })
  @Get('admin/:id/courses')
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  getInstructorCourses(
    @Param('id') id: string,
  ): Promise<GetInstructorCoursesResponseDto> {
    return this.instructorsService.getInstructorCoursesWithEnrollments(id);
  }

  @ApiOperation({
    summary:
      'Get templates created by instructor with enrollment counts (Admin/Staff only)',
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns templates created by instructor with their enrollment counts',
    type: GetInstructorTemplatesResponseDto,
  })
  @Get('admin/:id/templates')
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  getInstructorTemplates(
    @Param('id') id: string,
  ): Promise<GetInstructorTemplatesResponseDto> {
    return this.instructorsService.getInstructorTemplatesWithEnrollments(id);
  }
}
