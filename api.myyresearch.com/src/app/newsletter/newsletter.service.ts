import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Newsletter, NewsletterDocument } from './schemas/newsletter.schema';
import {
  SubscribeNewsletterDto,
  UnsubscribeNewsletterDto,
} from './dto/newsletter.dto';
import { NewsletterFilterDto } from './dto/newsletter-filter.dto';
import { FilterOperator, FilterType, JoinOperator } from './dto/filter.dto';
import { DocumentStatus } from './schemas/newsletter.schema';

@Injectable()
export class NewsletterService {
  constructor(
    @InjectModel(Newsletter.name)
    private newsletterModel: Model<NewsletterDocument>,
  ) {}

  async subscribe(dto: SubscribeNewsletterDto) {
    const { email } = dto;

    let subscriber = await this.newsletterModel.findOne({ email });

    if (subscriber) {
      if (subscriber.isSubscribed) {
        throw new BadRequestException('Email is already subscribed');
      }

      // Resubscribe
      subscriber.isSubscribed = true;
      subscriber.subscribedAt = new Date();
      subscriber.unsubscribedAt = undefined;
      await subscriber.save();
    } else {
      // Create new subscription
      subscriber = await this.newsletterModel.create({
        email,
        isSubscribed: true,
        subscribedAt: new Date(),
      });
    }

    return {
      message: 'Successfully subscribed to newsletter',
      email: subscriber.email,
    };
  }

  async unsubscribe(dto: UnsubscribeNewsletterDto) {
    const { email } = dto;

    const subscriber = await this.newsletterModel.findOne({ email });

    if (!subscriber || !subscriber.isSubscribed) {
      throw new BadRequestException('Email is not subscribed');
    }

    subscriber.isSubscribed = false;
    subscriber.unsubscribedAt = new Date();
    await subscriber.save();

    return {
      message: 'Successfully unsubscribed from newsletter',
      email: subscriber.email,
    };
  }

  async getAllSubscribers(filterDto: NewsletterFilterDto) {
    const {
      search,
      isSubscribed,
      page = 1,
      limit = 10,
      sort,
      filters,
      joinOperator = JoinOperator.AND,
      from,
      to,
    } = filterDto;

    const query: any = {
      documentStatus: DocumentStatus.ACTIVE,
    };

    // Add date range filtering
    if (from || to) {
      query.subscribedAt = {};
      if (from) {
        const fromDate = new Date(from);
        fromDate.setHours(0, 0, 0, 0);
        query.subscribedAt.$gte = fromDate;
      }
      if (to) {
        const toDate = new Date(to);
        toDate.setHours(23, 59, 59, 999);
        query.subscribedAt.$lte = toDate;
      }
    }

    // Handle basic filters
    if (search) {
      query.email = { $regex: search, $options: 'i' };
    }

    if (typeof isSubscribed === 'boolean') {
      query.isSubscribed = isSubscribed;
    }

    // Handle advanced filters
    if (filters && filters.length > 0) {
      const filterConditions = filters.map((filter) => {
        const condition: any = {};

        switch (filter.operator) {
          case FilterOperator.ILIKE:
            condition[filter.id] = { $regex: filter.value, $options: 'i' };
            break;
          case FilterOperator.NOT_ILIKE:
            condition[filter.id] = {
              $not: { $regex: filter.value, $options: 'i' },
            };
            break;
          case FilterOperator.EQ:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $in: filter.value };
            } else {
              condition[filter.id] = filter.value;
            }
            break;
          case FilterOperator.NE:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $nin: filter.value };
            } else {
              condition[filter.id] = { $ne: filter.value };
            }
            break;
          case FilterOperator.IS_EMPTY:
            condition[filter.id] = { $exists: false };
            break;
          case FilterOperator.IS_NOT_EMPTY:
            condition[filter.id] = { $exists: true };
            break;
        }
        return condition;
      });

      if (filterConditions.length > 0) {
        const operator = joinOperator === JoinOperator.AND ? '$and' : '$or';
        query[operator] = filterConditions;
      }
    }

    const skip = (page - 1) * limit;

    // Create sort object for mongoose
    let sortCriteria = {};
    if (sort && Array.isArray(sort)) {
      sort.forEach((sortItem) => {
        sortCriteria[sortItem.id] = sortItem.desc ? -1 : 1;
      });
    } else {
      // Default sort by subscribedAt desc if no sort provided
      sortCriteria = { subscribedAt: -1 };
    }

    const [subscribers, total] = await Promise.all([
      this.newsletterModel
        .find(query)
        .sort(sortCriteria)
        .skip(skip)
        .limit(limit)
        .lean(),
      this.newsletterModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      subscribers,
      total,
      page,
      totalPages,
    };
  }

  async delete(email: string) {
    const subscriber = await this.newsletterModel.findOne({ email });

    if (!subscriber) {
      throw new NotFoundException('Newsletter subscription not found');
    }

    subscriber.documentStatus = DocumentStatus.ARCHIVED;
    await subscriber.save();

    return {
      message: 'Successfully deleted newsletter subscription',
      email: subscriber.email,
    };
  }

  private buildFilterQuery(filterDto: NewsletterFilterDto): any {
    const {
      search,
      filters,
      joinOperator = JoinOperator.AND,
      from,
      to,
    } = filterDto;

    const query: any = {
      documentStatus: DocumentStatus.ACTIVE,
    };

    // Add date range filtering
    if (from || to) {
      query.subscribedAt = {};
      if (from) {
        // Set time to start of day in UTC
        const fromDate = new Date(from);
        fromDate.setUTCHours(0, 0, 0, 0);
        query.subscribedAt.$gte = fromDate;
      }
      if (to) {
        // Set time to end of day in UTC
        const toDate = new Date(to);
        toDate.setUTCHours(23, 59, 59, 999);
        query.subscribedAt.$lte = toDate;
      }
    }

    // Handle basic filters
    if (search) {
      query.email = { $regex: search, $options: 'i' };
    }

    // Handle advanced filters
    if (filters && filters.length > 0) {
      const filterConditions = filters.map((filter) => {
        const condition: any = {};

        switch (filter.operator) {
          case FilterOperator.ILIKE:
            condition[filter.id] = { $regex: filter.value, $options: 'i' };
            break;
          case FilterOperator.NOT_ILIKE:
            condition[filter.id] = {
              $not: { $regex: filter.value, $options: 'i' },
            };
            break;
          case FilterOperator.EQ:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $in: filter.value };
            } else {
              condition[filter.id] = filter.value;
            }
            break;
          case FilterOperator.NE:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $nin: filter.value };
            } else {
              condition[filter.id] = { $ne: filter.value };
            }
            break;
          case FilterOperator.IS_EMPTY:
            condition[filter.id] = { $exists: false };
            break;
          case FilterOperator.IS_NOT_EMPTY:
            condition[filter.id] = { $exists: true };
            break;
        }
        return condition;
      });

      if (filterConditions.length > 0) {
        const operator = joinOperator === JoinOperator.AND ? '$and' : '$or';
        query[operator] = filterConditions;
      }
    }

    return query;
  }

  async getSubscribedCount(filterDto: NewsletterFilterDto) {
    const baseQuery = this.buildFilterQuery(filterDto);
    const query = {
      ...baseQuery,
      isSubscribed: true,
    };

    const count = await this.newsletterModel.countDocuments(query);
    return { count };
  }

  async getUnsubscribedCount(filterDto: NewsletterFilterDto) {
    const baseQuery = this.buildFilterQuery(filterDto);
    const query = {
      ...baseQuery,
      isSubscribed: false,
    };

    const count = await this.newsletterModel.countDocuments(query);
    return { count };
  }
}
