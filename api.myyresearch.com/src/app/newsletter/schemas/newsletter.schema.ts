import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum DocumentStatus {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
}

export type NewsletterDocument = Newsletter & Document;

@Schema({ timestamps: true })
export class Newsletter {
  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ default: true })
  isSubscribed: boolean;

  @Prop({ type: Date, default: Date.now })
  subscribedAt: Date;

  @Prop({ type: Date })
  unsubscribedAt?: Date;

  @Prop({ type: String, enum: DocumentStatus, default: DocumentStatus.ACTIVE })
  documentStatus: DocumentStatus;
}

export const NewsletterSchema = SchemaFactory.createForClass(Newsletter);
