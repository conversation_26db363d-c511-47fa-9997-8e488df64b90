import {
  Controller,
  Post,
  Body,
  Get,
  UseGuards,
  Query,
  Param,
  Delete,
} from '@nestjs/common';
import { NewsletterService } from './newsletter.service';
import {
  SubscribeNewsletterDto,
  UnsubscribeNewsletterDto,
} from './dto/newsletter.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBody,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { NewsletterFilterDto } from './dto/newsletter-filter.dto';

@ApiTags('Newsletters')
@Controller('newsletters')
export class NewsletterController {
  constructor(private readonly newsletterService: NewsletterService) {}

  @Post('subscribe')
  @ApiOperation({ summary: 'Subscribe to newsletter' })
  @ApiBody({
    type: SubscribeNewsletterDto,
    description: 'Email subscription details',
    examples: {
      example1: {
        value: { email: '<EMAIL>' },
        summary: 'Basic subscription request',
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Successfully subscribed to newsletter',
    schema: {
      example: {
        message: 'Successfully subscribed to newsletter',
        email: '<EMAIL>',
      },
    },
  })
  async subscribe(@Body() dto: SubscribeNewsletterDto) {
    return this.newsletterService.subscribe(dto);
  }

  @Post('unsubscribe')
  @ApiOperation({ summary: 'Unsubscribe from newsletter' })
  @ApiBody({
    type: UnsubscribeNewsletterDto,
    description: 'Email unsubscription details',
    examples: {
      example1: {
        value: { email: '<EMAIL>' },
        summary: 'Basic unsubscription request',
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully unsubscribed from newsletter',
    schema: {
      example: {
        message: 'Successfully unsubscribed from newsletter',
        email: '<EMAIL>',
      },
    },
  })
  async unsubscribe(@Body() dto: UnsubscribeNewsletterDto) {
    return this.newsletterService.unsubscribe(dto);
  }

  @Get()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all newsletter subscribers' })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search by email',
    example: 'example.com',
  })
  @ApiQuery({
    name: 'isSubscribed',
    required: false,
    type: Boolean,
    description: 'Filter by subscription status',
    example: true,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Return list of all subscribers',
    schema: {
      example: {
        subscribers: [
          {
            email: '<EMAIL>',
            isSubscribed: true,
            subscribedAt: '2024-03-15T10:00:00.000Z',
          },
        ],
        total: 1,
        page: 1,
        totalPages: 1,
      },
    },
  })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  async getAllSubscribers(@Query() filterDto: NewsletterFilterDto) {
    return this.newsletterService.getAllSubscribers(filterDto);
  }

  @Delete('delete/:email')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete newsletter subscription' })
  @ApiParam({
    name: 'email',
    type: String,
    description: 'Email address to delete',
    example: '<EMAIL>',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully deleted newsletter subscription',
    schema: {
      example: {
        message: 'Successfully deleted newsletter subscription',
        email: '<EMAIL>',
      },
    },
  })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  async delete(@Param('email') email: string) {
    return this.newsletterService.delete(email);
  }

  @Get('count/subscribed')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get count of subscribed newsletters' })
  @ApiResponse({
    status: 200,
    description: 'Returns count of subscribed newsletters',
    schema: {
      example: {
        count: 42,
      },
    },
  })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  async getSubscribedCount(@Query() filterDto: NewsletterFilterDto) {
    return this.newsletterService.getSubscribedCount(filterDto);
  }

  @Get('count/unsubscribed')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get count of unsubscribed newsletters' })
  @ApiResponse({
    status: 200,
    description: 'Returns count of unsubscribed newsletters',
    schema: {
      example: {
        count: 15,
      },
    },
  })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  async getUnsubscribedCount(@Query() filterDto: NewsletterFilterDto) {
    return this.newsletterService.getUnsubscribedCount(filterDto);
  }
}
