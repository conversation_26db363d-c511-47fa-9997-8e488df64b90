import { IsEmail, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SubscribeNewsletterDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address to subscribe to the newsletter',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;
}

export class UnsubscribeNewsletterDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address to unsubscribe from the newsletter',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;
}
