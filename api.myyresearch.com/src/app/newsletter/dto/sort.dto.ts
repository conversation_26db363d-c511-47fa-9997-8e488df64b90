import { IsString, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SortItemDto {
  @ApiProperty({
    description: 'Field name to sort by',
    example: 'subscribedAt',
    enum: ['email', 'subscribedAt', 'unsubscribedAt', 'isSubscribed'],
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Sort direction (true for descending, false for ascending)',
    example: true,
    type: Boolean,
  })
  @IsBoolean()
  desc: boolean;
}
