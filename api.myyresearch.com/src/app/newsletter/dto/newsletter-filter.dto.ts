import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsBoolean,
  IsDateString,
  IsEnum,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { SortItemDto } from './sort.dto';
import { FilterItemDto, Join<PERSON>perator } from './filter.dto';

export class NewsletterFilterDto {
  @ApiPropertyOptional({ description: 'Search by email' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Filter by subscription status' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  isSubscribed?: boolean;

  @ApiPropertyOptional({ description: 'Number of items per page' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Page number' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  page?: number = 1;

  @ApiPropertyOptional({
    type: [SortItemDto],
    description:
      'Sort criteria array. Example: [{"id":"subscribedAt","desc":true}]',
  })
  @IsOptional()
  @Type(() => SortItemDto)
  sort?: SortItemDto[];

  @ApiPropertyOptional({
    type: [FilterItemDto],
    description: 'Advanced filter criteria array',
  })
  @IsOptional()
  @Type(() => FilterItemDto)
  filters?: FilterItemDto[];

  @ApiPropertyOptional({
    enum: JoinOperator,
    description: 'Operator to join multiple filters (and/or)',
    default: JoinOperator.AND,
  })
  @IsOptional()
  @IsEnum(JoinOperator)
  joinOperator?: JoinOperator = JoinOperator.AND;

  @ApiPropertyOptional({
    description: 'Start date for filtering (format: YYYY-MM-DD)',
    example: '2024-12-13',
  })
  @IsOptional()
  @IsDateString()
  from?: string;

  @ApiPropertyOptional({
    description: 'End date for filtering (format: YYYY-MM-DD)',
    example: '2024-12-13',
  })
  @IsOptional()
  @IsDateString()
  to?: string;
}
