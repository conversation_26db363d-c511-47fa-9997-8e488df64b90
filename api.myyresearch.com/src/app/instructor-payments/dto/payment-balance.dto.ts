import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsN<PERSON>ber,
  IsMongoId,
  IsDateString,
} from 'class-validator';
import { Type } from 'class-transformer';

// DTO for unpaid order items
export class UnpaidOrderItemDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  _id: string;

  @ApiProperty({ example: 'course' })
  itemType: string;

  @ApiProperty({ example: 'Advanced React Course' })
  itemTitle: string;

  @ApiProperty({ example: 99.99 })
  totalPrice: number;

  @ApiProperty({ example: 59.99 })
  instructorEarning: number;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  orderDate: Date;

  @ApiProperty({ example: 'ABC123DEF456' })
  publicId: string;
}

// DTO for payment history
export class PaymentHistoryDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  _id: string;

  @ApiProperty({ example: 599.99 })
  amount: number;

  @ApiProperty({ example: 'usd' })
  currency: string;

  @ApiProperty({ example: 'paypal' })
  paymentMethod: string;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  paymentDate: Date;

  @ApiPropertyOptional({ example: 'TXN123456789' })
  transactionId?: string;

  @ApiPropertyOptional({ example: 'Payment for December 2023 earnings' })
  notes?: string;

  @ApiProperty({ example: 10 })
  orderItemsCount: number;
}

// DTO for instructor payment balance information
export class InstructorPaymentBalanceDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  instructorId: string;

  @ApiProperty({ example: 'John Doe' })
  instructorName: string;

  @ApiProperty({ example: '<EMAIL>' })
  instructorEmail: string;

  @ApiProperty({ example: 1299.99 })
  totalEarnings: number;

  @ApiProperty({ example: 700.0 })
  totalPaid: number;

  @ApiProperty({ example: 599.99 })
  currentBalance: number;

  @ApiProperty({ example: 'usd' })
  currency: string;

  @ApiProperty({ example: 15 })
  unpaidOrderItemsCount: number;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  lastPaymentDate?: Date;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  firstEarningDate: Date;
}

// DTO for admin to get all instructor balances
export class GetInstructorBalancesResponseDto {
  @ApiProperty({ type: [InstructorPaymentBalanceDto] })
  instructors: InstructorPaymentBalanceDto[];

  @ApiProperty({ example: 25 })
  totalInstructors: number;

  @ApiProperty({ example: 15999.99 })
  totalUnpaidAmount: number;

  @ApiProperty({ example: 'usd' })
  currency: string;
}

// DTO for individual instructor balance details
export class InstructorBalanceDetailsDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  instructorId: string;

  @ApiProperty({ example: 1299.99 })
  totalEarnings: number;

  @ApiProperty({ example: 700.0 })
  totalPaid: number;

  @ApiProperty({ example: 599.99 })
  currentBalance: number;

  @ApiProperty({ example: 'usd' })
  currency: string;

  @ApiProperty({ type: [UnpaidOrderItemDto] })
  unpaidOrderItems: UnpaidOrderItemDto[];

  @ApiProperty({ type: [PaymentHistoryDto] })
  paymentHistory: PaymentHistoryDto[];
}

// Filter DTO for getting instructor balances
export class InstructorBalanceFilterDto {
  @ApiPropertyOptional({ example: 'John' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ example: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  minBalance?: number;

  @ApiPropertyOptional({ example: 1000 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  maxBalance?: number;

  @ApiPropertyOptional({ example: '2024-01-01' })
  @IsOptional()
  @IsDateString()
  fromDate?: string;

  @ApiPropertyOptional({ example: '2024-12-31' })
  @IsOptional()
  @IsDateString()
  toDate?: string;

  @ApiPropertyOptional({ example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number;

  @ApiPropertyOptional({ example: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number;
}

// DTO for payment summary statistics
export class PaymentSummaryStatsDto {
  @ApiProperty({ example: 25999.99 })
  totalEarningsAllTime: number;

  @ApiProperty({ example: 15999.99 })
  totalPaidAllTime: number;

  @ApiProperty({ example: 10000.0 })
  totalUnpaidAmount: number;

  @ApiProperty({ example: 45 })
  totalInstructorsWithEarnings: number;

  @ApiProperty({ example: 25 })
  totalInstructorsWithUnpaidBalance: number;

  @ApiProperty({ example: 'usd' })
  currency: string;

  @ApiProperty({ example: 2599.99 })
  averageUnpaidBalance: number;

  @ApiProperty({ example: 1299.99 })
  highestUnpaidBalance: number;
}
