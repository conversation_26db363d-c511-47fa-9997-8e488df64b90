import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsString,
  IsNumber,
  IsDateString,
  IsMongoId,
  IsArray,
  Min,
} from 'class-validator';
import {
  PaymentMethod,
  PaymentRequestStatus,
} from '../schemas/instructor-payment.schema';

// DTOs for InstructorPaymentDetails
export class CreateInstructorPaymentDetailsDto {
  @ApiProperty({ enum: PaymentMethod, example: PaymentMethod.PAYPAL })
  @IsEnum(PaymentMethod)
  preferredPaymentMethod: PaymentMethod;

  @ApiPropertyOptional({ example: '<EMAIL>' })
  @IsOptional()
  @IsString()
  paypalEmail?: string;

  @ApiPropertyOptional({ example: '**********' })
  @IsOptional()
  @IsString()
  bankAccountNumber?: string;

  @ApiPropertyOptional({ example: '*********' })
  @IsOptional()
  @IsString()
  bankRoutingNumber?: string;

  @ApiPropertyOptional({ example: 'Chase Bank' })
  @IsOptional()
  @IsString()
  bankName?: string;

  @ApiPropertyOptional({ example: 'John Doe' })
  @IsOptional()
  @IsString()
  accountHolderName?: string;

  @ApiPropertyOptional({ example: 'CHASUS33' })
  @IsOptional()
  @IsString()
  swiftCode?: string;

  @ApiPropertyOptional({ example: 'acct_**********' })
  @IsOptional()
  @IsString()
  stripeAccountId?: string;
}

export class UpdateInstructorPaymentDetailsDto {
  @ApiPropertyOptional({ enum: PaymentMethod, example: PaymentMethod.PAYPAL })
  @IsOptional()
  @IsEnum(PaymentMethod)
  preferredPaymentMethod?: PaymentMethod;

  @ApiPropertyOptional({ example: '<EMAIL>' })
  @IsOptional()
  @IsString()
  paypalEmail?: string;

  @ApiPropertyOptional({ example: '**********' })
  @IsOptional()
  @IsString()
  bankAccountNumber?: string;

  @ApiPropertyOptional({ example: '*********' })
  @IsOptional()
  @IsString()
  bankRoutingNumber?: string;

  @ApiPropertyOptional({ example: 'Chase Bank' })
  @IsOptional()
  @IsString()
  bankName?: string;

  @ApiPropertyOptional({ example: 'John Doe' })
  @IsOptional()
  @IsString()
  accountHolderName?: string;

  @ApiPropertyOptional({ example: 'CHASUS33' })
  @IsOptional()
  @IsString()
  swiftCode?: string;

  @ApiPropertyOptional({ example: 'acct_**********' })
  @IsOptional()
  @IsString()
  stripeAccountId?: string;
}

// DTOs for PaymentRecord
export class CreatePaymentRecordDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  @IsMongoId()
  instructorId: string;

  @ApiProperty({ example: 599.99 })
  @IsNumber()
  @Min(0)
  amount: number;

  @ApiProperty({ example: 'usd' })
  @IsString()
  currency: string;

  @ApiProperty({ enum: PaymentMethod, example: PaymentMethod.PAYPAL })
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  @IsDateString()
  paymentDate: string;

  @ApiPropertyOptional({ example: 'TXN123456789' })
  @IsOptional()
  @IsString()
  transactionId?: string;

  @ApiPropertyOptional({ example: 'Payment for December 2023 earnings' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    type: [String],
    example: ['507f1f77bcf86cd799439012', '507f1f77bcf86cd799439013'],
  })
  @IsArray()
  @IsMongoId({ each: true })
  orderItemDetailsIds: string[];
}

// DTOs for PaymentRequest
export class CreatePaymentRequestDto {
  @ApiProperty({ example: 599.99 })
  @IsNumber()
  @Min(0)
  requestedAmount: number;

  @ApiProperty({ example: 'usd' })
  @IsString()
  currency: string;

  @ApiPropertyOptional({ example: 'Need payment for December earnings' })
  @IsOptional()
  @IsString()
  requestNotes?: string;
}

export class UpdatePaymentRequestDto {
  @ApiProperty({
    enum: PaymentRequestStatus,
    example: PaymentRequestStatus.APPROVED,
  })
  @IsEnum(PaymentRequestStatus)
  status: PaymentRequestStatus;

  @ApiPropertyOptional({ example: 'Approved for payment processing' })
  @IsOptional()
  @IsString()
  adminNotes?: string;
}

// Response DTOs
export class InstructorPaymentDetailsResponseDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  _id: string;

  @ApiProperty({ example: '507f1f77bcf86cd799439012' })
  instructorId: string;

  @ApiProperty({ enum: PaymentMethod, example: PaymentMethod.PAYPAL })
  preferredPaymentMethod: PaymentMethod;

  @ApiPropertyOptional({ example: '<EMAIL>' })
  paypalEmail?: string;

  @ApiPropertyOptional({ example: '****7890' })
  bankAccountNumber?: string;

  @ApiPropertyOptional({ example: 'Chase Bank' })
  bankName?: string;

  @ApiPropertyOptional({ example: 'John Doe' })
  accountHolderName?: string;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  createdAt: Date;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  updatedAt: Date;
}

export class PaymentRecordResponseDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  _id: string;

  @ApiProperty({ example: '507f1f77bcf86cd799439012' })
  instructorId: string;

  @ApiProperty({ example: 599.99 })
  amount: number;

  @ApiProperty({ example: 'usd' })
  currency: string;

  @ApiProperty({ enum: PaymentMethod, example: PaymentMethod.PAYPAL })
  paymentMethod: PaymentMethod;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  paymentDate: Date;

  @ApiPropertyOptional({ example: 'TXN123456789' })
  transactionId?: string;

  @ApiPropertyOptional({ example: 'Payment for December 2023 earnings' })
  notes?: string;

  @ApiProperty({
    type: [String],
    example: ['507f1f77bcf86cd799439012', '507f1f77bcf86cd799439013'],
  })
  orderItemDetailsIds: string[];

  @ApiProperty({
    example: '507f1f77bcf86cd799439013',
    description: 'The latest OrderItemDetails _id included in this payment',
  })
  lastPaidOrderItemDetailsId: string;

  @ApiProperty({ example: '507f1f77bcf86cd799439014' })
  processedBy: string;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  createdAt: Date;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  updatedAt: Date;
}

export class PaymentRequestResponseDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  _id: string;

  @ApiProperty({ example: '507f1f77bcf86cd799439012' })
  instructorId: string;

  @ApiProperty({ example: 599.99 })
  requestedAmount: number;

  @ApiProperty({ example: 'usd' })
  currency: string;

  @ApiProperty({
    enum: PaymentRequestStatus,
    example: PaymentRequestStatus.PENDING,
  })
  status: PaymentRequestStatus;

  @ApiPropertyOptional({ example: 'Need payment for December earnings' })
  requestNotes?: string;

  @ApiPropertyOptional({ example: 'Approved for payment processing' })
  adminNotes?: string;

  @ApiPropertyOptional({ example: '2024-01-15T10:30:00Z' })
  approvedAt?: Date;

  @ApiPropertyOptional({ example: '2024-01-15T10:30:00Z' })
  rejectedAt?: Date;

  @ApiPropertyOptional({ example: '2024-01-15T10:30:00Z' })
  paidAt?: Date;

  @ApiPropertyOptional({ example: '507f1f77bcf86cd799439014' })
  processedBy?: string;

  @ApiPropertyOptional({ example: '507f1f77bcf86cd799439015' })
  paymentRecordId?: string;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  createdAt: Date;

  @ApiProperty({ example: '2024-01-01T00:00:00Z' })
  updatedAt: Date;
}
