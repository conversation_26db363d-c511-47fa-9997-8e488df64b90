import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { InstructorPaymentsService } from './instructor-payments.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import {
  CreateInstructorPaymentDetailsDto,
  UpdateInstructorPaymentDetailsDto,
  CreatePaymentRecordDto,
  CreatePaymentRequestDto,
  UpdatePaymentRequestDto,
  InstructorPaymentDetailsResponseDto,
  PaymentRecordResponseDto,
  PaymentRequestResponseDto,
} from './dto/instructor-payment.dto';
import {
  GetInstructorBalancesResponseDto,
  InstructorBalanceDetailsDto,
  InstructorBalanceFilterDto,
  PaymentSummaryStatsDto,
} from './dto/payment-balance.dto';

@ApiTags('Instructor Payments')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('instructor-payments')
export class InstructorPaymentsController {
  constructor(
    private readonly instructorPaymentsService: InstructorPaymentsService,
  ) {}

  // Instructor Payment Details Endpoints
  @Post('payment-details')
  @UseGuards(RolesGuard)
  @Roles(UserRole.INSTRUCTOR)
  @ApiOperation({ summary: 'Create instructor payment details' })
  @ApiResponse({
    status: 201,
    description: 'Payment details created successfully',
    type: InstructorPaymentDetailsResponseDto,
  })
  async createPaymentDetails(
    @Request() req: any,
    @Body() createDto: CreateInstructorPaymentDetailsDto,
  ): Promise<InstructorPaymentDetailsResponseDto> {
    const result = await this.instructorPaymentsService.createPaymentDetails(
      req.user._id,
      createDto,
    );
    return {
      _id: result._id.toString(),
      instructorId: result.instructorId.toString(),
      preferredPaymentMethod: result.preferredPaymentMethod,
      paypalEmail: result.paypalEmail,
      bankAccountNumber: result.bankAccountNumber,
      bankName: result.bankName,
      accountHolderName: result.accountHolderName,
      createdAt: (result as any).createdAt,
      updatedAt: (result as any).updatedAt,
    };
  }

  @Put('payment-details')
  @UseGuards(RolesGuard)
  @Roles(UserRole.INSTRUCTOR)
  @ApiOperation({ summary: 'Update instructor payment details' })
  @ApiResponse({
    status: 200,
    description: 'Payment details updated successfully',
    type: InstructorPaymentDetailsResponseDto,
  })
  async updatePaymentDetails(
    @Request() req: any,
    @Body() updateDto: UpdateInstructorPaymentDetailsDto,
  ): Promise<InstructorPaymentDetailsResponseDto> {
    const result = await this.instructorPaymentsService.updatePaymentDetails(
      req.user._id,
      updateDto,
    );
    return {
      _id: result._id.toString(),
      instructorId: result.instructorId.toString(),
      preferredPaymentMethod: result.preferredPaymentMethod,
      paypalEmail: result.paypalEmail,
      bankAccountNumber: result.bankAccountNumber,
      bankName: result.bankName,
      accountHolderName: result.accountHolderName,
      createdAt: (result as any).createdAt,
      updatedAt: (result as any).updatedAt,
    };
  }

  @Get('payment-details')
  @UseGuards(RolesGuard)
  @Roles(UserRole.INSTRUCTOR)
  @ApiOperation({ summary: 'Get instructor payment details' })
  @ApiResponse({
    status: 200,
    description: 'Payment details retrieved successfully',
    type: InstructorPaymentDetailsResponseDto,
  })
  async getPaymentDetails(
    @Request() req: any,
  ): Promise<InstructorPaymentDetailsResponseDto | null> {
    const result = await this.instructorPaymentsService.getPaymentDetails(
      req.user._id,
    );
    if (!result) {
      return null;
    }
    return {
      _id: result._id.toString(),
      instructorId: result.instructorId.toString(),
      preferredPaymentMethod: result.preferredPaymentMethod,
      paypalEmail: result.paypalEmail,
      bankAccountNumber: result.bankAccountNumber,
      bankName: result.bankName,
      accountHolderName: result.accountHolderName,
      createdAt: (result as any).createdAt,
      updatedAt: (result as any).updatedAt,
    };
  }

  // Instructor Balance and History Endpoints
  @Get('balance')
  @UseGuards(RolesGuard)
  @Roles(UserRole.INSTRUCTOR)
  @ApiOperation({ summary: 'Get instructor payment balance and details' })
  @ApiResponse({
    status: 200,
    description: 'Balance details retrieved successfully',
    type: InstructorBalanceDetailsDto,
  })
  async getInstructorBalance(
    @Request() req: any,
  ): Promise<InstructorBalanceDetailsDto> {
    return this.instructorPaymentsService.calculateInstructorBalance(
      req.user._id,
    );
  }

  @Get('payment-history')
  @UseGuards(RolesGuard)
  @Roles(UserRole.INSTRUCTOR)
  @ApiOperation({ summary: 'Get instructor payment history' })
  @ApiResponse({
    status: 200,
    description: 'Payment history retrieved successfully',
    type: [PaymentRecordResponseDto],
  })
  async getInstructorPaymentHistory(
    @Request() req: any,
  ): Promise<PaymentRecordResponseDto[]> {
    const results = await this.instructorPaymentsService.getPaymentHistory(
      req.user._id,
    );
    return results.map((result) => ({
      _id: result._id.toString(),
      instructorId: result.instructorId.toString(),
      amount: result.amount,
      currency: result.currency,
      paymentMethod: result.paymentMethod,
      paymentDate: result.paymentDate,
      transactionId: result.transactionId,
      notes: result.notes,
      orderItemDetailsIds: result.orderItemDetailsIds.map((id) =>
        id.toString(),
      ),
      lastPaidOrderItemDetailsId: result.lastPaidOrderItemDetailsId.toString(),
      processedBy: result.processedBy.toString(),
      createdAt: (result as any).createdAt,
      updatedAt: (result as any).updatedAt,
    }));
  }

  // Payment Request Endpoints
  @Post('payment-request')
  @UseGuards(RolesGuard)
  @Roles(UserRole.INSTRUCTOR)
  @ApiOperation({ summary: 'Create payment request' })
  @ApiResponse({
    status: 201,
    description: 'Payment request created successfully',
    type: PaymentRequestResponseDto,
  })
  async createPaymentRequest(
    @Request() req: any,
    @Body() createDto: CreatePaymentRequestDto,
  ): Promise<PaymentRequestResponseDto> {
    const result = await this.instructorPaymentsService.createPaymentRequest(
      req.user._id,
      createDto,
    );
    return {
      _id: result._id.toString(),
      instructorId: result.instructorId.toString(),
      requestedAmount: result.requestedAmount,
      currency: result.currency,
      status: result.status,
      requestNotes: result.requestNotes,
      adminNotes: result.adminNotes,
      approvedAt: result.approvedAt,
      rejectedAt: result.rejectedAt,
      paidAt: result.paidAt,
      processedBy: result.processedBy?.toString(),
      paymentRecordId: result.paymentRecordId?.toString(),
      createdAt: (result as any).createdAt,
      updatedAt: (result as any).updatedAt,
    };
  }

  @Get('payment-requests')
  @UseGuards(RolesGuard)
  @Roles(UserRole.INSTRUCTOR)
  @ApiOperation({ summary: 'Get instructor payment requests' })
  @ApiResponse({
    status: 200,
    description: 'Payment requests retrieved successfully',
    type: [PaymentRequestResponseDto],
  })
  async getInstructorPaymentRequests(
    @Request() req: any,
  ): Promise<PaymentRequestResponseDto[]> {
    const results = await this.instructorPaymentsService.getPaymentRequests(
      req.user._id,
    );
    return results.map((result) => ({
      _id: result._id.toString(),
      instructorId: result.instructorId.toString(),
      requestedAmount: result.requestedAmount,
      currency: result.currency,
      status: result.status,
      requestNotes: result.requestNotes,
      adminNotes: result.adminNotes,
      approvedAt: result.approvedAt,
      rejectedAt: result.rejectedAt,
      paidAt: result.paidAt,
      processedBy: result.processedBy?.toString(),
      paymentRecordId: result.paymentRecordId?.toString(),
      createdAt: (result as any).createdAt,
      updatedAt: (result as any).updatedAt,
    }));
  }

  // Admin Endpoints
  @Get('admin/balances')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all instructor payment balances (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Instructor balances retrieved successfully',
    type: GetInstructorBalancesResponseDto,
  })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'minBalance', required: false, type: Number })
  @ApiQuery({ name: 'maxBalance', required: false, type: Number })
  @ApiQuery({ name: 'fromDate', required: false, type: String })
  @ApiQuery({ name: 'toDate', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getAllInstructorBalances(
    @Query() filterDto: InstructorBalanceFilterDto,
  ): Promise<GetInstructorBalancesResponseDto> {
    return this.instructorPaymentsService.getAllInstructorBalances(filterDto);
  }

  @Get('admin/instructor/:instructorId/balance')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({
    summary: 'Get specific instructor balance details (Admin only)',
  })
  @ApiResponse({
    status: 200,
    description: 'Instructor balance details retrieved successfully',
    type: InstructorBalanceDetailsDto,
  })
  @ApiParam({ name: 'instructorId', type: String })
  async getInstructorBalanceById(
    @Param('instructorId') instructorId: string,
  ): Promise<InstructorBalanceDetailsDto> {
    return this.instructorPaymentsService.calculateInstructorBalance(
      instructorId,
    );
  }

  @Post('admin/payment-record')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create payment record (Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'Payment record created successfully',
    type: PaymentRecordResponseDto,
  })
  async createPaymentRecord(
    @Request() req: any,
    @Body() createDto: CreatePaymentRecordDto,
  ): Promise<PaymentRecordResponseDto> {
    const result = await this.instructorPaymentsService.createPaymentRecord(
      req.user._id,
      createDto,
    );
    return {
      _id: result._id.toString(),
      instructorId: result.instructorId.toString(),
      amount: result.amount,
      currency: result.currency,
      paymentMethod: result.paymentMethod,
      paymentDate: result.paymentDate,
      transactionId: result.transactionId,
      notes: result.notes,
      orderItemDetailsIds: result.orderItemDetailsIds.map((id) =>
        id.toString(),
      ),
      lastPaidOrderItemDetailsId: result.lastPaidOrderItemDetailsId.toString(),
      processedBy: result.processedBy.toString(),
      createdAt: (result as any).createdAt,
      updatedAt: (result as any).updatedAt,
    };
  }

  @Get('admin/payment-history')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all payment history (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Payment history retrieved successfully',
    type: [PaymentRecordResponseDto],
  })
  async getAllPaymentHistory(): Promise<PaymentRecordResponseDto[]> {
    const results = await this.instructorPaymentsService.getPaymentHistory();
    return results.map((result) => ({
      _id: result._id.toString(),
      instructorId: result.instructorId.toString(),
      amount: result.amount,
      currency: result.currency,
      paymentMethod: result.paymentMethod,
      paymentDate: result.paymentDate,
      transactionId: result.transactionId,
      notes: result.notes,
      orderItemDetailsIds: result.orderItemDetailsIds.map((id) =>
        id.toString(),
      ),
      lastPaidOrderItemDetailsId: result.lastPaidOrderItemDetailsId.toString(),
      processedBy: result.processedBy.toString(),
      createdAt: (result as any).createdAt,
      updatedAt: (result as any).updatedAt,
    }));
  }

  @Get('admin/payment-requests')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all payment requests (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Payment requests retrieved successfully',
    type: [PaymentRequestResponseDto],
  })
  async getAllPaymentRequests(): Promise<PaymentRequestResponseDto[]> {
    const results = await this.instructorPaymentsService.getPaymentRequests();
    return results.map((result) => ({
      _id: result._id.toString(),
      instructorId: result.instructorId.toString(),
      requestedAmount: result.requestedAmount,
      currency: result.currency,
      status: result.status,
      requestNotes: result.requestNotes,
      adminNotes: result.adminNotes,
      approvedAt: result.approvedAt,
      rejectedAt: result.rejectedAt,
      paidAt: result.paidAt,
      processedBy: result.processedBy?.toString(),
      paymentRecordId: result.paymentRecordId?.toString(),
      createdAt: (result as any).createdAt,
      updatedAt: (result as any).updatedAt,
    }));
  }

  @Put('admin/payment-request/:requestId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update payment request status (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Payment request updated successfully',
    type: PaymentRequestResponseDto,
  })
  @ApiParam({ name: 'requestId', type: String })
  async updatePaymentRequest(
    @Request() req: any,
    @Param('requestId') requestId: string,
    @Body() updateDto: UpdatePaymentRequestDto,
  ): Promise<PaymentRequestResponseDto> {
    const result = await this.instructorPaymentsService.updatePaymentRequest(
      requestId,
      req.user._id,
      updateDto,
    );
    return {
      _id: result._id.toString(),
      instructorId: result.instructorId.toString(),
      requestedAmount: result.requestedAmount,
      currency: result.currency,
      status: result.status,
      requestNotes: result.requestNotes,
      adminNotes: result.adminNotes,
      approvedAt: result.approvedAt,
      rejectedAt: result.rejectedAt,
      paidAt: result.paidAt,
      processedBy: result.processedBy?.toString(),
      paymentRecordId: result.paymentRecordId?.toString(),
      createdAt: (result as any).createdAt,
      updatedAt: (result as any).updatedAt,
    };
  }

  @Get('admin/stats')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get payment summary statistics (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Payment statistics retrieved successfully',
    type: PaymentSummaryStatsDto,
  })
  async getPaymentSummaryStats(): Promise<PaymentSummaryStatsDto> {
    return this.instructorPaymentsService.getPaymentSummaryStats();
  }
}
