import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  InstructorPaymentDetails,
  InstructorPaymentDetailsDocument,
  PaymentRecord,
  PaymentRecordDocument,
  PaymentRequest,
  PaymentRequestDocument,
  DocumentStatus,
  PaymentRequestStatus,
} from './schemas/instructor-payment.schema';
import {
  OrderItemDetails,
  OrderItemDetailsDocument,
} from '../orders/schemas/order.schema';
import { User, UserDocument, UserRole } from '../users/schemas/user.schema';
import {
  CreateInstructorPaymentDetailsDto,
  UpdateInstructorPaymentDetailsDto,
  CreatePaymentRecordDto,
  CreatePaymentRequestDto,
  UpdatePaymentRequestDto,
} from './dto/instructor-payment.dto';
import {
  InstructorPaymentBalanceDto,
  GetInstructorBalancesResponseDto,
  InstructorBalanceDetailsDto,
  UnpaidOrderItemDto,
  PaymentHistoryDto,
  InstructorBalanceFilterDto,
  PaymentSummaryStatsDto,
} from './dto/payment-balance.dto';

@Injectable()
export class InstructorPaymentsService {
  private readonly INSTRUCTOR_COMMISSION_RATE = 0.6; // 60% commission

  constructor(
    @InjectModel(InstructorPaymentDetails.name)
    private instructorPaymentDetailsModel: Model<InstructorPaymentDetailsDocument>,
    @InjectModel(PaymentRecord.name)
    private paymentRecordModel: Model<PaymentRecordDocument>,
    @InjectModel(PaymentRequest.name)
    private paymentRequestModel: Model<PaymentRequestDocument>,
    @InjectModel(OrderItemDetails.name)
    private orderItemDetailsModel: Model<OrderItemDetailsDocument>,
    @InjectModel(User.name)
    private userModel: Model<UserDocument>,
  ) {}

  // Payment Details Management
  async createPaymentDetails(
    instructorId: string,
    createDto: CreateInstructorPaymentDetailsDto,
  ): Promise<InstructorPaymentDetailsDocument> {
    // Verify instructor exists and has instructor role
    const instructor = await this.userModel.findById(instructorId);
    if (!instructor || instructor.role !== UserRole.INSTRUCTOR) {
      throw new NotFoundException('Instructor not found');
    }

    // Check if payment details already exist
    const existingDetails = await this.instructorPaymentDetailsModel.findOne({
      instructorId,
      documentStatus: DocumentStatus.ACTIVE,
    });

    if (existingDetails) {
      throw new BadRequestException(
        'Payment details already exist for this instructor',
      );
    }

    const paymentDetails = new this.instructorPaymentDetailsModel({
      instructorId,
      ...createDto,
    });

    return paymentDetails.save();
  }

  async updatePaymentDetails(
    instructorId: string,
    updateDto: UpdateInstructorPaymentDetailsDto,
  ): Promise<InstructorPaymentDetailsDocument> {
    const paymentDetails =
      await this.instructorPaymentDetailsModel.findOneAndUpdate(
        { instructorId, documentStatus: DocumentStatus.ACTIVE },
        updateDto,
        { new: true },
      );

    if (!paymentDetails) {
      throw new NotFoundException('Payment details not found');
    }

    return paymentDetails;
  }

  async getPaymentDetails(
    instructorId: string,
  ): Promise<InstructorPaymentDetailsDocument | null> {
    return this.instructorPaymentDetailsModel.findOne({
      instructorId,
    });
  }

  // Payment Calculation Logic
  async calculateInstructorBalance(
    instructorId: string,
  ): Promise<InstructorBalanceDetailsDto> {
    // Get all order items created by this instructor, sorted by orderDate ascending
    const allOrderItems = await this.orderItemDetailsModel
      .find({
        createdBy: instructorId,
      })
      .sort({ orderDate: 1 }); // Sort ascending by orderDate

    // Get the most recent paid payment request for this instructor
    const lastPaidPaymentRequest = await this.paymentRequestModel
      .findOne({
        instructorId,
        status: PaymentRequestStatus.PAID,
      })
      .sort({ createdAt: -1 }); // Get the most recent paid payment request

    console.log('lastPaidPaymentRequest', lastPaidPaymentRequest);

    // Filter order items based on the last paid payment request
    // let unpaidOrderItems = allOrderItems;
    // if (lastPaidPaymentRequest && lastPaidPaymentRequest.paidAt) {
    //   // Only consider order items created after the last paid payment request
    //   unpaidOrderItems = allOrderItems.filter(
    //     (item) => item.orderDate > lastPaidPaymentRequest.createdAt,
    //   );
    // }

    const unpaidOrderItems = await this.orderItemDetailsModel
      .find({
        createdBy: instructorId,
        // @ts-ignore
        orderDate: { $gt: lastPaidPaymentRequest?.createdAt },
      })
      .sort({ orderDate: 1 });

    // Calculate current balance from unpaid items only (60% commission)
    let currentBalance = 0;
    const unpaidOrderItemsDto: UnpaidOrderItemDto[] = [];

    unpaidOrderItems.forEach((item) => {
      const instructorEarning =
        item.totalPrice * this.INSTRUCTOR_COMMISSION_RATE;
      currentBalance += instructorEarning;

      unpaidOrderItemsDto.push({
        _id: item._id.toString(),
        itemType: item.itemType,
        itemTitle: item.itemTitle,
        totalPrice: item.totalPrice,
        instructorEarning,
        orderDate: item.orderDate,
        publicId: item.publicId,
      });
    });

    // Calculate total earnings from all order items (60% commission)
    let totalEarnings = 0;
    allOrderItems.forEach((item) => {
      totalEarnings += item.totalPrice * this.INSTRUCTOR_COMMISSION_RATE;
    });

    // Calculate total paid (total earnings minus current balance)
    const totalPaid = totalEarnings - currentBalance;

    // Get all payment records for payment history
    const paymentRecords = await this.paymentRecordModel
      .find({
        instructorId,
        documentStatus: DocumentStatus.ACTIVE,
      })
      .sort({ paymentDate: -1 });

    // Format payment history
    const paymentHistory: PaymentHistoryDto[] = paymentRecords.map(
      (record) => ({
        _id: record._id.toString(),
        amount: record.amount,
        currency: record.currency,
        paymentMethod: record.paymentMethod,
        paymentDate: record.paymentDate,
        transactionId: record.transactionId,
        notes: record.notes,
        orderItemsCount: record.orderItemDetailsIds.length,
      }),
    );

    return {
      instructorId,
      totalEarnings,
      totalPaid,
      currentBalance,
      currency: 'usd',
      unpaidOrderItems: unpaidOrderItemsDto,
      paymentHistory,
    };
  }

  // Admin Functions
  async getAllInstructorBalances(
    filterDto: InstructorBalanceFilterDto = {},
  ): Promise<GetInstructorBalancesResponseDto> {
    // Get all instructors with earnings
    const instructorsWithEarnings = await this.orderItemDetailsModel.aggregate([
      {
        $match: {
          documentStatus: DocumentStatus.ACTIVE,
        },
      },
      {
        $group: {
          _id: '$createdBy',
          totalRevenue: { $sum: '$totalPrice' },
          orderCount: { $sum: 1 },
          firstEarningDate: { $min: '$orderDate' },
        },
      },
    ]);

    const instructorBalances: InstructorPaymentBalanceDto[] = [];
    let totalUnpaidAmount = 0;

    for (const instructorData of instructorsWithEarnings) {
      const instructorId = instructorData._id.toString();

      // Get instructor details
      const instructor = await this.userModel.findById(instructorId);
      if (!instructor || instructor.role !== UserRole.INSTRUCTOR) {
        continue;
      }

      // Calculate balance for this instructor
      const balanceDetails =
        await this.calculateInstructorBalance(instructorId);

      // Apply filters
      if (
        filterDto.minBalance &&
        balanceDetails.currentBalance < filterDto.minBalance
      ) {
        continue;
      }
      if (
        filterDto.maxBalance &&
        balanceDetails.currentBalance > filterDto.maxBalance
      ) {
        continue;
      }
      if (filterDto.search) {
        const searchTerm = filterDto.search.toLowerCase();
        if (
          !instructor.username.toLowerCase().includes(searchTerm) &&
          !instructor.email.toLowerCase().includes(searchTerm)
        ) {
          continue;
        }
      }

      // Get last payment date
      const lastPayment = await this.paymentRecordModel
        .findOne({ instructorId, documentStatus: DocumentStatus.ACTIVE })
        .sort({ paymentDate: -1 });

      const instructorBalance: InstructorPaymentBalanceDto = {
        instructorId,
        instructorName: instructor.username,
        instructorEmail: instructor.email,
        totalEarnings: balanceDetails.totalEarnings,
        totalPaid: balanceDetails.totalPaid,
        currentBalance: balanceDetails.currentBalance,
        currency: 'usd',
        unpaidOrderItemsCount: balanceDetails.unpaidOrderItems.length,
        lastPaymentDate: lastPayment?.paymentDate,
        firstEarningDate: instructorData.firstEarningDate,
      };

      instructorBalances.push(instructorBalance);
      totalUnpaidAmount += balanceDetails.currentBalance;
    }

    // Apply pagination
    const page = filterDto.page || 1;
    const limit = filterDto.limit || 20;
    const startIndex = (page - 1) * limit;
    const paginatedInstructors = instructorBalances.slice(
      startIndex,
      startIndex + limit,
    );

    return {
      instructors: paginatedInstructors,
      totalInstructors: instructorBalances.length,
      totalUnpaidAmount,
      currency: 'usd',
    };
  }

  async createPaymentRecord(
    adminId: string,
    createDto: CreatePaymentRecordDto,
  ): Promise<PaymentRecordDocument> {
    // Verify admin permissions
    const admin = await this.userModel.findById(adminId);
    if (!admin || admin.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can create payment records');
    }

    // Verify instructor exists
    const instructor = await this.userModel.findById(createDto.instructorId);
    if (!instructor || instructor.role !== UserRole.INSTRUCTOR) {
      throw new NotFoundException('Instructor not found');
    }

    // Verify order items exist and belong to the instructor
    const orderItems = await this.orderItemDetailsModel
      .find({
        _id: { $in: createDto.orderItemDetailsIds },
        createdBy: createDto.instructorId,
        documentStatus: DocumentStatus.ACTIVE,
      })
      .sort({ orderDate: 1 }); // Sort by orderDate ascending to find the latest

    if (orderItems.length !== createDto.orderItemDetailsIds.length) {
      throw new BadRequestException(
        'Some order items not found or do not belong to this instructor',
      );
    }

    // Check if any order items have already been paid
    const existingPayments = await this.paymentRecordModel.find({
      orderItemDetailsIds: { $in: createDto.orderItemDetailsIds },
      documentStatus: DocumentStatus.ACTIVE,
    });

    if (existingPayments.length > 0) {
      throw new BadRequestException('Some order items have already been paid');
    }

    // Find the latest OrderItemDetails by orderDate to set as lastPaidOrderItemDetailsId
    const latestOrderItem = orderItems[orderItems.length - 1];

    const paymentRecord = new this.paymentRecordModel({
      ...createDto,
      lastPaidOrderItemDetailsId: latestOrderItem._id,
      processedBy: adminId,
      paymentDate: new Date(createDto.paymentDate),
    });

    return paymentRecord.save();
  }

  // Payment Request Management
  async createPaymentRequest(
    instructorId: string,
    createDto: CreatePaymentRequestDto,
  ): Promise<PaymentRequestDocument> {
    // Verify instructor exists and has instructor role
    const instructor = await this.userModel.findById(instructorId);
    if (!instructor || instructor.role !== UserRole.INSTRUCTOR) {
      throw new NotFoundException('Instructor not found');
    }

    // Check if instructor has sufficient balance
    const balanceDetails = await this.calculateInstructorBalance(instructorId);
    if (createDto.requestedAmount > balanceDetails.currentBalance) {
      throw new BadRequestException('Requested amount exceeds current balance');
    }

    // Check if there's already a pending request
    const existingRequest = await this.paymentRequestModel.findOne({
      instructorId,
      status: PaymentRequestStatus.PENDING,
      documentStatus: DocumentStatus.ACTIVE,
    });

    if (existingRequest) {
      throw new BadRequestException(
        'You already have a pending payment request',
      );
    }

    const paymentRequest = new this.paymentRequestModel({
      instructorId,
      ...createDto,
    });

    return paymentRequest.save();
  }

  async updatePaymentRequest(
    requestId: string,
    adminId: string,
    updateDto: UpdatePaymentRequestDto,
  ): Promise<PaymentRequestDocument> {
    // Verify admin permissions
    const admin = await this.userModel.findById(adminId);
    if (!admin || admin.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can update payment requests');
    }

    const paymentRequest = await this.paymentRequestModel.findById(requestId);
    if (!paymentRequest) {
      throw new NotFoundException('Payment request not found');
    }

    // Update status and timestamps
    const updateData: any = {
      status: updateDto.status,
      adminNotes: updateDto.adminNotes,
      processedBy: adminId,
    };

    if (updateDto.status === PaymentRequestStatus.APPROVED) {
      updateData.approvedAt = new Date();
    } else if (updateDto.status === PaymentRequestStatus.REJECTED) {
      updateData.rejectedAt = new Date();
    }

    return this.paymentRequestModel.findByIdAndUpdate(requestId, updateData, {
      new: true,
    });
  }

  async getPaymentRequests(
    instructorId?: string,
  ): Promise<PaymentRequestDocument[]> {
    const filter: any = { documentStatus: DocumentStatus.ACTIVE };
    if (instructorId) {
      filter.instructorId = instructorId;
    }

    return this.paymentRequestModel
      .find(filter)
      .populate('instructorId', 'username email')
      .populate('processedBy', 'username email')
      .sort({ createdAt: -1 });
  }

  async getPaymentHistory(
    instructorId?: string,
  ): Promise<PaymentRecordDocument[]> {
    const filter: any = { documentStatus: DocumentStatus.ACTIVE };
    if (instructorId) {
      filter.instructorId = instructorId;
    }

    return this.paymentRecordModel
      .find(filter)
      .populate('instructorId', 'username email')
      .populate('processedBy', 'username email')
      .sort({ paymentDate: -1 });
  }

  async getPaymentSummaryStats(): Promise<PaymentSummaryStatsDto> {
    // Get all instructors with earnings
    const instructorsWithEarnings = await this.orderItemDetailsModel.aggregate([
      {
        $match: {
          documentStatus: DocumentStatus.ACTIVE,
        },
      },
      {
        $group: {
          _id: '$createdBy',
          totalRevenue: { $sum: '$totalPrice' },
        },
      },
    ]);

    let totalEarningsAllTime = 0;
    let totalInstructorsWithEarnings = 0;
    let totalInstructorsWithUnpaidBalance = 0;
    let totalUnpaidAmount = 0;
    let highestUnpaidBalance = 0;
    const unpaidBalances: number[] = [];

    for (const instructorData of instructorsWithEarnings) {
      const instructorId = instructorData._id.toString();

      // Verify this is an instructor
      const instructor = await this.userModel.findById(instructorId);
      if (!instructor || instructor.role !== UserRole.INSTRUCTOR) {
        continue;
      }

      totalInstructorsWithEarnings++;
      const instructorEarnings =
        instructorData.totalRevenue * this.INSTRUCTOR_COMMISSION_RATE;
      totalEarningsAllTime += instructorEarnings;

      // Calculate balance for this instructor
      const balanceDetails =
        await this.calculateInstructorBalance(instructorId);

      if (balanceDetails.currentBalance > 0) {
        totalInstructorsWithUnpaidBalance++;
        totalUnpaidAmount += balanceDetails.currentBalance;
        unpaidBalances.push(balanceDetails.currentBalance);

        if (balanceDetails.currentBalance > highestUnpaidBalance) {
          highestUnpaidBalance = balanceDetails.currentBalance;
        }
      }
    }

    // Get total paid amount
    const totalPaidResult = await this.paymentRecordModel.aggregate([
      {
        $match: {
          documentStatus: DocumentStatus.ACTIVE,
        },
      },
      {
        $group: {
          _id: null,
          totalPaid: { $sum: '$amount' },
        },
      },
    ]);

    const totalPaidAllTime =
      totalPaidResult.length > 0 ? totalPaidResult[0].totalPaid : 0;
    const averageUnpaidBalance =
      totalInstructorsWithUnpaidBalance > 0
        ? totalUnpaidAmount / totalInstructorsWithUnpaidBalance
        : 0;

    return {
      totalEarningsAllTime,
      totalPaidAllTime,
      totalUnpaidAmount,
      totalInstructorsWithEarnings,
      totalInstructorsWithUnpaidBalance,
      currency: 'usd',
      averageUnpaidBalance,
      highestUnpaidBalance,
    };
  }
}
