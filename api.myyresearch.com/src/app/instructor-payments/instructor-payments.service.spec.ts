import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { InstructorPaymentsService } from './instructor-payments.service';
import {
  InstructorPaymentDetails,
  PaymentRecord,
  PaymentRequest,
} from './schemas/instructor-payment.schema';
import { OrderItemDetails } from '../orders/schemas/order.schema';
import { User } from '../users/schemas/user.schema';
import { DocumentStatus } from '../orders/schemas/order.schema';

describe('InstructorPaymentsService - Balance Calculation', () => {
  let service: InstructorPaymentsService;
  let orderItemDetailsModel: Model<OrderItemDetails>;
  let paymentRecordModel: Model<PaymentRecord>;

  const mockInstructorId = '507f1f77bcf86cd799439011';
  const mockOrderItems = [
    {
      _id: '507f1f77bcf86cd799439021',
      createdBy: mockInstructorId,
      totalPrice: 100,
      orderDate: new Date('2024-01-01'),
      itemType: 'course',
      itemTitle: 'Course 1',
      publicId: 'course1',
      documentStatus: DocumentStatus.ACTIVE,
    },
    {
      _id: '507f1f77bcf86cd799439022',
      createdBy: mockInstructorId,
      totalPrice: 200,
      orderDate: new Date('2024-01-02'),
      itemType: 'course',
      itemTitle: 'Course 2',
      publicId: 'course2',
      documentStatus: DocumentStatus.ACTIVE,
    },
    {
      _id: '507f1f77bcf86cd799439023',
      createdBy: mockInstructorId,
      totalPrice: 150,
      orderDate: new Date('2024-01-03'),
      itemType: 'template',
      itemTitle: 'Template 1',
      publicId: 'template1',
      documentStatus: DocumentStatus.ACTIVE,
    },
  ];

  const mockPaymentRecord = {
    _id: '507f1f77bcf86cd799439031',
    instructorId: mockInstructorId,
    amount: 180, // 60% of (100 + 200)
    lastPaidOrderItemDetailsId: '507f1f77bcf86cd799439022', // Paid up to Course 2
    paymentDate: new Date('2024-01-05'),
    documentStatus: DocumentStatus.ACTIVE,
    orderItemDetailsIds: [
      '507f1f77bcf86cd799439021',
      '507f1f77bcf86cd799439022',
    ],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InstructorPaymentsService,
        {
          provide: getModelToken(InstructorPaymentDetails.name),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getModelToken(PaymentRecord.name),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getModelToken(PaymentRequest.name),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getModelToken(OrderItemDetails.name),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getModelToken(User.name),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<InstructorPaymentsService>(InstructorPaymentsService);
    orderItemDetailsModel = module.get<Model<OrderItemDetails>>(
      getModelToken(OrderItemDetails.name),
    );
    paymentRecordModel = module.get<Model<PaymentRecord>>(
      getModelToken(PaymentRecord.name),
    );
  });

  describe('calculateInstructorBalance', () => {
    it('should calculate correct balance with no payments', async () => {
      // Mock order items
      const mockFind = jest.fn().mockReturnValue({
        sort: jest.fn().mockResolvedValue(mockOrderItems),
      });
      orderItemDetailsModel.find = mockFind;

      // Mock no payment records
      const mockPaymentFindOne = jest.fn().mockReturnValue({
        sort: jest.fn().mockResolvedValue(null),
      });
      const mockPaymentFind = jest.fn().mockReturnValue({
        sort: jest.fn().mockResolvedValue([]),
      });
      paymentRecordModel.findOne = mockPaymentFindOne;
      paymentRecordModel.find = mockPaymentFind;

      const result = await service.calculateInstructorBalance(mockInstructorId);

      // Total earnings: (100 + 200 + 150) * 0.6 = 270
      // No payments made, so current balance = total earnings = 270
      expect(result.totalEarnings).toBe(270);
      expect(result.totalPaid).toBe(0);
      expect(result.currentBalance).toBe(270);
      expect(result.unpaidOrderItems).toHaveLength(3);
    });

    it('should calculate correct balance with partial payment', async () => {
      // Mock order items
      const mockFind = jest.fn().mockReturnValue({
        sort: jest.fn().mockResolvedValue(mockOrderItems),
      });
      orderItemDetailsModel.find = mockFind;

      // Mock payment record (paid up to Course 2)
      const mockPaymentFindOne = jest.fn().mockReturnValue({
        sort: jest.fn().mockResolvedValue(mockPaymentRecord),
      });
      const mockPaymentFind = jest.fn().mockReturnValue({
        sort: jest.fn().mockResolvedValue([mockPaymentRecord]),
      });
      paymentRecordModel.findOne = mockPaymentFindOne;
      paymentRecordModel.find = mockPaymentFind;

      const result = await service.calculateInstructorBalance(mockInstructorId);

      // Total earnings: (100 + 200 + 150) * 0.6 = 270
      // Paid up to Course 2, so only Template 1 (150 * 0.6 = 90) is unpaid
      // Current balance should be 90
      expect(result.totalEarnings).toBe(270);
      expect(result.totalPaid).toBe(180); // 270 - 90
      expect(result.currentBalance).toBe(90);
      expect(result.unpaidOrderItems).toHaveLength(1);
      expect(result.unpaidOrderItems[0].itemTitle).toBe('Template 1');
      expect(result.unpaidOrderItems[0].instructorEarning).toBe(90);
    });

    it('should calculate correct balance with all payments made', async () => {
      // Mock order items
      const mockFind = jest.fn().mockReturnValue({
        sort: jest.fn().mockResolvedValue(mockOrderItems),
      });
      orderItemDetailsModel.find = mockFind;

      // Mock payment record that covers all orders
      const fullPaymentRecord = {
        ...mockPaymentRecord,
        lastPaidOrderItemDetailsId: '507f1f77bcf86cd799439023', // Paid up to Template 1
        amount: 270,
      };
      const mockPaymentFindOne = jest.fn().mockReturnValue({
        sort: jest.fn().mockResolvedValue(fullPaymentRecord),
      });
      const mockPaymentFind = jest.fn().mockReturnValue({
        sort: jest.fn().mockResolvedValue([fullPaymentRecord]),
      });
      paymentRecordModel.findOne = mockPaymentFindOne;
      paymentRecordModel.find = mockPaymentFind;

      const result = await service.calculateInstructorBalance(mockInstructorId);

      // All orders paid, so current balance should be 0
      expect(result.totalEarnings).toBe(270);
      expect(result.totalPaid).toBe(270);
      expect(result.currentBalance).toBe(0);
      expect(result.unpaidOrderItems).toHaveLength(0);
    });
  });
});
