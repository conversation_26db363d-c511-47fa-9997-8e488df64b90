import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { User } from '../../users/schemas/user.schema';
import { OrderItemDetails } from '../../orders/schemas/order.schema';

export type InstructorPaymentDetailsDocument = InstructorPaymentDetails &
  Document;
export type PaymentRecordDocument = PaymentRecord & Document;
export type PaymentRequestDocument = PaymentRequest & Document;

export enum PaymentMethod {
  PAYPAL = 'paypal',
  BANK_TRANSFER = 'bank_transfer',
  STRIPE = 'stripe',
}

export enum PaymentRequestStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PAID = 'paid',
}

export enum DocumentStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

// Schema for storing instructor payment preferences
@Schema({ timestamps: true })
export class InstructorPaymentDetails {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
  })
  instructorId: MongooseSchema.Types.ObjectId;

  @Prop({ type: String, enum: PaymentMethod, required: true })
  preferredPaymentMethod: PaymentMethod;

  // PayPal details
  @Prop()
  paypalEmail?: string;

  // Bank transfer details
  @Prop()
  bankAccountNumber?: string;

  @Prop()
  bankRoutingNumber?: string;

  @Prop()
  bankName?: string;

  @Prop()
  accountHolderName?: string;

  @Prop()
  swiftCode?: string;

  // Stripe details
  @Prop()
  stripeAccountId?: string;

  @Prop({ type: String, enum: DocumentStatus, default: DocumentStatus.ACTIVE })
  documentStatus: DocumentStatus;

  @Prop({ type: Object })
  metadata?: Record<string, any>;
}

// Schema for tracking payment records when admin processes payments
@Schema({ timestamps: true })
export class PaymentRecord {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  instructorId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  amount: number;

  @Prop({ required: true, default: 'usd' })
  currency: string;

  @Prop({ type: String, enum: PaymentMethod, required: true })
  paymentMethod: PaymentMethod;

  @Prop({ required: true })
  paymentDate: Date;

  @Prop()
  transactionId?: string;

  @Prop()
  notes?: string;

  // Reference to the OrderItemDetails included in this payment
  @Prop({
    type: [MongooseSchema.Types.ObjectId],
    ref: 'OrderItemDetails',
    required: true,
  })
  orderItemDetailsIds: MongooseSchema.Types.ObjectId[];

  // Track the latest OrderItemDetails _id included in this payment for balance calculation
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'OrderItemDetails',
    required: true,
  })
  lastPaidOrderItemDetailsId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  processedBy: MongooseSchema.Types.ObjectId;

  @Prop({ type: String, enum: DocumentStatus, default: DocumentStatus.ACTIVE })
  documentStatus: DocumentStatus;

  @Prop({ type: Object })
  metadata?: Record<string, any>;
}

// Schema for instructor payment requests
@Schema({ timestamps: true })
export class PaymentRequest {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  instructorId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  requestedAmount: number;

  @Prop({ required: true, default: 'usd' })
  currency: string;

  @Prop({
    type: String,
    enum: PaymentRequestStatus,
    default: PaymentRequestStatus.PENDING,
  })
  status: PaymentRequestStatus;

  @Prop()
  requestNotes?: string;

  @Prop()
  adminNotes?: string;

  @Prop({ type: Date })
  approvedAt?: Date;

  @Prop({ type: Date })
  rejectedAt?: Date;

  @Prop({ type: Date })
  paidAt?: Date;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User' })
  processedBy?: MongooseSchema.Types.ObjectId;

  // Reference to the payment record when paid
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'PaymentRecord' })
  paymentRecordId?: MongooseSchema.Types.ObjectId;

  @Prop({ type: String, enum: DocumentStatus, default: DocumentStatus.ACTIVE })
  documentStatus: DocumentStatus;

  @Prop({ type: Object })
  metadata?: Record<string, any>;
}

export const InstructorPaymentDetailsSchema = SchemaFactory.createForClass(
  InstructorPaymentDetails,
);
export const PaymentRecordSchema = SchemaFactory.createForClass(PaymentRecord);
export const PaymentRequestSchema =
  SchemaFactory.createForClass(PaymentRequest);

// Create indexes for InstructorPaymentDetails
InstructorPaymentDetailsSchema.index({ instructorId: 1 }, { unique: true });
InstructorPaymentDetailsSchema.index({ documentStatus: 1 });

// Create indexes for PaymentRecord
PaymentRecordSchema.index({ instructorId: 1 });
PaymentRecordSchema.index({ paymentDate: -1 });
PaymentRecordSchema.index({ documentStatus: 1 });
PaymentRecordSchema.index({ processedBy: 1 });
PaymentRecordSchema.index({ instructorId: 1, lastPaidOrderItemDetailsId: 1 });

// Create indexes for PaymentRequest
PaymentRequestSchema.index({ instructorId: 1 });
PaymentRequestSchema.index({ status: 1 });
PaymentRequestSchema.index({ createdAt: -1 });
PaymentRequestSchema.index({ documentStatus: 1 });
