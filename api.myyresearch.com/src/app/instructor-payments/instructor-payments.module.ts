import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import {
  InstructorPaymentDetails,
  InstructorPaymentDetailsSchema,
  PaymentRecord,
  PaymentRecordSchema,
  PaymentRequest,
  PaymentRequestSchema,
} from './schemas/instructor-payment.schema';
import { OrderItemDetails, OrderItemDetailsSchema } from '../orders/schemas/order.schema';
import { User, UserSchema } from '../users/schemas/user.schema';
import { InstructorPaymentsController } from './instructor-payments.controller';
import { InstructorPaymentsService } from './instructor-payments.service';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([
      { name: InstructorPaymentDetails.name, schema: InstructorPaymentDetailsSchema },
      { name: PaymentRecord.name, schema: PaymentRecordSchema },
      { name: PaymentRequest.name, schema: PaymentRequestSchema },
      { name: OrderItemDetails.name, schema: OrderItemDetailsSchema },
      { name: User.name, schema: UserSchema },
    ]),
  ],
  controllers: [InstructorPaymentsController],
  providers: [InstructorPaymentsService],
  exports: [InstructorPaymentsService, MongooseModule],
})
export class InstructorPaymentsModule {}
