import {
  Controller,
  Get,
  Post,
  Body,
  Delete,
  Param,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { TemplateEnrollmentsService } from './template-enrollments.service';
import { CreateTemplateEnrollmentDto } from './dto/create-template-enrollment.dto';
import { RolesGuard } from '../users/guards/roles.guard';
import { Roles } from '../users/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { TemplateEnrollmentFilterDto } from './dto/template-enrollment-filter.dto';
import { GetAllTemplateEnrollmentsResponseDto } from './dto/get-all-template-enrollments-response.dto';

@ApiTags('Template Enrollments')
@Controller('template-enrollments')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class TemplateEnrollmentsController {
  constructor(
    private readonly templateEnrollmentsService: TemplateEnrollmentsService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new template enrollment' })
  @ApiResponse({
    status: 201,
    description: 'Template enrollment created successfully',
  })
  @Roles(UserRole.USER)
  create(
    @Body() createTemplateEnrollmentDto: CreateTemplateEnrollmentDto,
    @Request() req,
  ) {
    return this.templateEnrollmentsService.create(
      req.user.userId,
      createTemplateEnrollmentDto,
    );
  }

  @Get('admin')
  @ApiOperation({
    summary: 'Get all template enrollments (Admin, Staff, Instructor)',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered template enrollments with pagination',
    type: GetAllTemplateEnrollmentsResponseDto,
  })
  @Roles(UserRole.ADMIN, UserRole.STAFF, UserRole.INSTRUCTOR)
  findAllAdmin(
    @Query() filterDto: TemplateEnrollmentFilterDto,
    @Request() req,
  ): Promise<GetAllTemplateEnrollmentsResponseDto> {
    return this.templateEnrollmentsService.findAllWithAuth(
      {
        userId: req.user._id,
        role: req.user.role,
      },
      filterDto,
    );
  }

  @Get()
  @ApiOperation({
    summary: 'Get all template enrollments for the current user',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered template enrollments with pagination',
    type: GetAllTemplateEnrollmentsResponseDto,
  })
  @Roles(UserRole.USER)
  findAll(
    @Query() filterDto: TemplateEnrollmentFilterDto,
    @Request() req,
  ): Promise<GetAllTemplateEnrollmentsResponseDto> {
    return this.templateEnrollmentsService.findAll(req.user.userId, filterDto);
  }

  @Get(':templateId')
  @ApiOperation({ summary: 'Get template enrollment by template ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns template enrollment details',
  })
  @Roles(UserRole.USER)
  findOne(@Param('templateId') templateId: string, @Request() req) {
    return this.templateEnrollmentsService.findOne(req.user.userId, templateId);
  }

  @Delete(':templateId')
  @ApiOperation({ summary: 'Delete template enrollment' })
  @ApiResponse({
    status: 200,
    description: 'Template enrollment deleted successfully',
  })
  @Roles(UserRole.USER)
  remove(@Param('templateId') templateId: string, @Request() req) {
    return this.templateEnrollmentsService.remove(req.user.userId, templateId);
  }
}
