import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TemplateEnrollmentsService } from './template-enrollments.service';
import { TemplateEnrollmentsController } from './template-enrollments.controller';
import {
  TemplateEnrollment,
  TemplateEnrollmentSchema,
} from './schemas/template-enrollment.schema';
import { Template, TemplateSchema } from '../templates/schemas/template.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: TemplateEnrollment.name, schema: TemplateEnrollmentSchema },
      { name: Template.name, schema: TemplateSchema },
    ]),
  ],
  controllers: [TemplateEnrollmentsController],
  providers: [TemplateEnrollmentsService],
  exports: [TemplateEnrollmentsService],
})
export class TemplateEnrollmentsModule {}
