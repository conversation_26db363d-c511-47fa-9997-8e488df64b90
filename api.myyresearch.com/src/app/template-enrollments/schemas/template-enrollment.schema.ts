import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type TemplateEnrollmentDocument = TemplateEnrollment & Document;

@Schema({ timestamps: true })
export class TemplateEnrollment {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Template', required: true })
  templateId: Types.ObjectId;

  @Prop({ default: false })
  downloaded: boolean;

  @Prop({ type: Date })
  downloadedAt?: Date;

  @Prop({ type: Date })
  lastAccessedAt?: Date;

  @Prop({ default: 0 })
  downloadCount: number;
}

export const TemplateEnrollmentSchema =
  SchemaFactory.createForClass(TemplateEnrollment);
