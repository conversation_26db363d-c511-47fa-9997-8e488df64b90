import { ApiProperty } from '@nestjs/swagger';
import { MinimalTemplateEnrollmentDto } from './minimal-template-enrollment.dto';

export class GetAllTemplateEnrollmentsResponseDto {
  @ApiProperty({ type: [MinimalTemplateEnrollmentDto] })
  templateEnrollments: MinimalTemplateEnrollmentDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  totalPages: number;

  @ApiProperty()
  hasMore: boolean;
}
