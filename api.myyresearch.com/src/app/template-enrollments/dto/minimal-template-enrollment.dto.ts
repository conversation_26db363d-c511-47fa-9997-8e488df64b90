import { ApiProperty } from '@nestjs/swagger';

export class MinimalTemplateEnrollmentDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  user: string;

  @ApiProperty()
  templateId: string;

  @ApiProperty()
  templatePublicId: string;

  @ApiProperty()
  templateName: string;

  @ApiProperty()
  templateTitle: string;

  @ApiProperty()
  templateThumbnailUrl: string;

  @ApiProperty()
  downloadCount: number;

  @ApiProperty()
  lastAccessedAt: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
