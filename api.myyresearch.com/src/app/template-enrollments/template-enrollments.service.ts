import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  TemplateEnrollment,
  TemplateEnrollmentDocument,
} from './schemas/template-enrollment.schema';
import {
  Template,
  TemplateDocument,
} from '../templates/schemas/template.schema';
import { CreateTemplateEnrollmentDto } from './dto/create-template-enrollment.dto';
import { TemplateEnrollmentFilterDto } from './dto/template-enrollment-filter.dto';
import { GetAllTemplateEnrollmentsResponseDto } from './dto/get-all-template-enrollments-response.dto';
import { MinimalTemplateEnrollmentDto } from './dto/minimal-template-enrollment.dto';
import { UserRole } from '../users/schemas/user.schema';

@Injectable()
export class TemplateEnrollmentsService {
  constructor(
    @InjectModel(TemplateEnrollment.name)
    private readonly templateEnrollmentModel: Model<TemplateEnrollmentDocument>,
    @InjectModel(Template.name)
    private readonly templateModel: Model<TemplateDocument>,
  ) {}

  async create(userId: string, createDto: CreateTemplateEnrollmentDto) {
    const enrollment = new this.templateEnrollmentModel({
      userId: new Types.ObjectId(userId),
      templateId: new Types.ObjectId(createDto.templateId),
      lastAccessedAt: new Date(),
      downloadCount: 0,
    });
    return enrollment.save();
  }

  async findAll(
    userId: string,
    filterDto: TemplateEnrollmentFilterDto,
  ): Promise<GetAllTemplateEnrollmentsResponseDto> {
    const { page = 1, limit = 10, search } = filterDto;
    const skip = (page - 1) * limit;

    const query: any = { userId: new Types.ObjectId(userId) };

    if (search) {
      // We'll need to fetch template IDs that match the search first
      const templateIds = await this.templateModel
        .find({ name: { $regex: search, $options: 'i' } })
        .select('_id')
        .lean()
        .exec();

      query.templateId = { $in: templateIds.map((t) => t._id) };
    }

    const [enrollments, total] = await Promise.all([
      this.templateEnrollmentModel
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean()
        .exec(),
      this.templateEnrollmentModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(total / limit);
    const hasMore = page < totalPages;

    // Get all unique template IDs from enrollments
    const templateIds = enrollments.map((enrollment) => enrollment.templateId);

    // Fetch template details
    const templates = await this.templateModel
      .find({ _id: { $in: templateIds } })
      .select('_id name publicId title thumbnail')
      .lean()
      .exec();

    // Create a map for quick lookup
    const templateMap = new Map<string, any>(
      templates.map((template: any): [string, any] => [
        template._id.toString(),
        {
          name: template.name as string,
          publicId: template.publicId as string,
          title: template.title as string,
          thumbnailUrl: template.thumbnail.url as string,
        },
      ]),
    );

    const minimalEnrollments: MinimalTemplateEnrollmentDto[] = enrollments.map(
      (enrollment: any) => {
        const templateId = enrollment.templateId.toString();
        const templateDetails = templateMap.get(templateId) || {
          name: 'Unknown Template',
          publicId: '',
          title: 'Unknown Template',
          thumbnailUrl: '',
        };

        return {
          _id: enrollment._id.toString(),
          user: enrollment.userId.toString(),
          templateId,
          templatePublicId: templateDetails.publicId,
          templateName: templateDetails.name,
          templateTitle: templateDetails.title,
          templateThumbnailUrl: templateDetails.thumbnailUrl,
          downloadCount: enrollment.downloadCount,
          lastAccessedAt: enrollment.lastAccessedAt,
          createdAt: enrollment.createdAt,
          updatedAt: enrollment.updatedAt,
        };
      },
    );

    return {
      templateEnrollments: minimalEnrollments,
      total,
      page,
      totalPages,
      hasMore,
    };
  }

  async findAllWithAuth(
    auth: { userId: string; role: string },
    filterDto: TemplateEnrollmentFilterDto,
  ): Promise<GetAllTemplateEnrollmentsResponseDto> {
    const { page = 1, limit = 10, search, from, to } = filterDto;
    const skip = (page - 1) * limit;

    const query: any = {};

    // Add date range filtering
    if (from || to) {
      query.createdAt = {};
      if (from) {
        // Set time to start of day (00:00:00)
        const fromDate = new Date(from);
        fromDate.setHours(0, 0, 0, 0);
        query.createdAt.$gte = fromDate;
      }
      if (to) {
        // Set time to end of day (23:59:59.999)
        const toDate = new Date(to);
        toDate.setHours(23, 59, 59, 999);
        query.createdAt.$lte = toDate;
      }
    }

    // If instructor, only show enrollments for their templates
    if (auth.role === UserRole.INSTRUCTOR) {
      const instructorTemplates = await this.templateModel
        .find({ instructor: auth.userId })
        .select('_id')
        .lean()
        .exec();

      query.templateId = { $in: instructorTemplates.map((t) => t._id) };
    }

    if (search) {
      // We'll need to fetch template IDs that match the search first
      const templateIds = await this.templateModel
        .find({ name: { $regex: search, $options: 'i' } })
        .select('_id')
        .lean()
        .exec();

      if (query.templateId) {
        // If instructor, only search within their templates
        query.templateId = {
          $in: templateIds
            .map((t) => t._id)
            .filter((id) =>
              query.templateId.$in.some(
                (tid) => tid.toString() === id.toString(),
              ),
            ),
        };
      } else {
        query.templateId = { $in: templateIds.map((t) => t._id) };
      }
    }

    const [enrollments, total] = await Promise.all([
      this.templateEnrollmentModel
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean()
        .exec(),
      this.templateEnrollmentModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(total / limit);
    const hasMore = page < totalPages;

    // Get all unique template IDs from enrollments
    const templateIds = enrollments.map((enrollment) => enrollment.templateId);

    // Fetch template details
    const templates = await this.templateModel
      .find({ _id: { $in: templateIds } })
      .select('_id name publicId title thumbnail')
      .lean()
      .exec();

    // Create a map for quick lookup
    const templateMap = new Map<string, any>(
      templates.map((template: any): [string, any] => [
        template._id.toString(),
        {
          name: template.name as string,
          publicId: template.publicId as string,
          title: template.title as string,
          thumbnailUrl: template.thumbnail.url as string,
        },
      ]),
    );

    const minimalEnrollments: MinimalTemplateEnrollmentDto[] = enrollments.map(
      (enrollment: any) => {
        const templateId = enrollment.templateId.toString();
        const templateDetails = templateMap.get(templateId) || {
          name: 'Unknown Template',
          publicId: '',
          title: 'Unknown Template',
          thumbnailUrl: '',
        };

        return {
          _id: enrollment._id.toString(),
          user: enrollment.userId.toString(),
          templateId,
          templatePublicId: templateDetails.publicId,
          templateName: templateDetails.name,
          templateTitle: templateDetails.title,
          templateThumbnailUrl: templateDetails.thumbnailUrl,
          downloadCount: enrollment.downloadCount,
          lastAccessedAt: enrollment.lastAccessedAt,
          createdAt: enrollment.createdAt,
          updatedAt: enrollment.updatedAt,
        };
      },
    );

    return {
      templateEnrollments: minimalEnrollments,
      total,
      page,
      totalPages,
      hasMore,
    };
  }

  async findOne(userId: string, templateId: string) {
    const enrollment = await this.templateEnrollmentModel
      .findOne({
        userId: new Types.ObjectId(userId),
        templateId: new Types.ObjectId(templateId),
      })
      .exec();

    if (!enrollment) {
      throw new NotFoundException('Template enrollment not found');
    }

    return enrollment;
  }

  async remove(userId: string, templateId: string) {
    const enrollment = await this.templateEnrollmentModel
      .findOneAndDelete({
        userId: new Types.ObjectId(userId),
        templateId: new Types.ObjectId(templateId),
      })
      .exec();

    if (!enrollment) {
      throw new NotFoundException('Template enrollment not found');
    }

    return enrollment;
  }

  async recordDownload(userId: string, templateId: string) {
    const enrollment = await this.templateEnrollmentModel
      .findOneAndUpdate(
        {
          userId: new Types.ObjectId(userId),
          templateId: new Types.ObjectId(templateId),
        },
        {
          $inc: { downloadCount: 1 },
          lastAccessedAt: new Date(),
        },
        { new: true },
      )
      .exec();

    if (!enrollment) {
      throw new NotFoundException('Template enrollment not found');
    }

    return enrollment;
  }

  async updateLastAccessed(userId: string, templateId: string) {
    const enrollment = await this.templateEnrollmentModel
      .findOneAndUpdate(
        {
          userId: new Types.ObjectId(userId),
          templateId: new Types.ObjectId(templateId),
        },
        {
          lastAccessedAt: new Date(),
        },
        { new: true },
      )
      .exec();

    if (!enrollment) {
      throw new NotFoundException('Template enrollment not found');
    }

    return enrollment;
  }
}
