import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { AuthService } from '../auth.service';
import * as passport from 'passport';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(private authService: AuthService) {
    super({
      clientID: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      callbackURL: `${process.env.API_URL}/api/v1/auth/google/callback`,
      scope: ['email', 'profile'],
    });

    // Serialize user for the session
    passport.serializeUser((user: any, done) => {
      done(null, user);
    });

    // Deserialize user from the session
    passport.deserializeUser((user: any, done) => {
      done(null, user);
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    console.log('Google strategy validate function called');
    console.log('Profile:', profile);
    const { id, emails, name, photos } = profile;
    const user = {
      id,
      email: emails[0].value,
      username:
        name.givenName?.toLowerCase().replace(/\s+/g, '_') +
        ' ' +
        name.familyName?.toLowerCase().replace(/\s+/g, '_'),
      avatar: photos[0].value,
    };

    const userData = await this.authService.validateGoogleUser(user);
    done(null, userData);
  }
}
