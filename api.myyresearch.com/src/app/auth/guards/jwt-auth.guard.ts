import {
  Injectable,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import * as jose from 'jose';
import * as crypto from 'crypto';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private encryptionKey: Uint8Array;

  constructor() {
    super();
    const rawKey = process.env.TOKEN_ENCRYPTION_KEY || 'your-encryption-key';
    this.encryptionKey = new Uint8Array(
      crypto.createHash('sha256').update(rawKey).digest(),
    );
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader) {
      throw new UnauthorizedException('No authorization header');
    }

    try {
      const [bearer, token] = authHeader.split(' ');

      if (bearer !== 'Bearer') {
        throw new UnauthorizedException('Invalid authorization header format');
      }

      // Decode the jose-encrypted token
      const decoder = new TextDecoder();
      const { plaintext } = await jose.compactDecrypt(
        token,
        this.encryptionKey,
      );
      const decodedToken = decoder.decode(plaintext);

      // Replace the encrypted token with the decoded JWT
      request.headers.authorization = `Bearer ${decodedToken}`;

      // Continue with regular JWT authentication
      return super.canActivate(context) as Promise<boolean>;
    } catch {
      throw new UnauthorizedException('Invalid token');
    }
  }
}
