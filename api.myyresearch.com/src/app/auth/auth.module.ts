import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { User, UserSchema } from '../users/schemas/user.schema';
import {
  RefreshToken,
  RefreshTokenSchema,
} from './schemas/refresh-token.schema';
import {
  InstructorProfile,
  InstructorProfileSchema,
} from '../instructor-profiles/schemas/instructor-profile.schema';
import { JwtStrategy } from './strategies/jwt.strategy';
import { GoogleStrategy } from './strategies/google.strategy';
import { RolesGuard } from './guards/roles.guard';
import { ConfigService } from '@nestjs/config';
import { ConfigModule } from '@core/config/config.module';
import { FacebookStrategy } from './strategies/facebook.strategy';
import { PassportModule } from '@nestjs/passport';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: RefreshToken.name, schema: RefreshTokenSchema },
      { name: InstructorProfile.name, schema: InstructorProfileSchema },
    ]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: () => ({
        secret: process.env.JWT_ACCESS_SECRET || 'your-access-secret-key',
        signOptions: { expiresIn: '1d' },
      }),
      inject: [ConfigService],
    }),
    PassportModule.register({ session: true }),
    ConfigModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    GoogleStrategy,
    FacebookStrategy,
    RolesGuard,
    {
      provide: 'JWT_REFRESH_TOKEN_SERVICE',
      useFactory: () => {
        return new JwtService({
          secret: process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key',
          signOptions: { expiresIn: '7d' },
        });
      },
    },
  ],
  exports: [AuthService, RolesGuard],
})
export class AuthModule {}
