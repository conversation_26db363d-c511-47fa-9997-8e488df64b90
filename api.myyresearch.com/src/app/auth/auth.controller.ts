import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Request,
  Req,
  Response,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { AuthGuard } from '@nestjs/passport';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { Enable2FADto } from './dto/two-factor.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { Throttle } from '@nestjs/throttler';
import { Roles } from './decorators/roles.decorator';
import { RolesGuard } from './guards/roles.guard';
import { UserRole } from '../users/schemas/user.schema';
import { Verify2FADto, Verify2FAResponseDto } from './dto/verify-2fa.dto';
import { LoginResponseDto, Requires2FAResponseDto } from './dto/login.dto';
import { RegisterResponseDto } from './dto/register.dto';
import { CreateUserDto, CreateUserResponseDto } from './dto/create-user.dto';
import { GoogleAuthGuard } from './guards/google-auth.guard';
import { ConfigService } from '@nestjs/config';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {}

  @ApiOperation({ summary: 'Register new user' })
  @ApiResponse({
    status: 201,
    description: 'User successfully registered',
    type: RegisterResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid registration data or user already exists',
  })
  @Post('register')
  register(@Body() registerDto: RegisterDto): Promise<RegisterResponseDto> {
    return this.authService.register(registerDto);
  }

  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({
    status: 200,
    description: 'User successfully logged in',
    type: LoginResponseDto,
  })
  @ApiResponse({
    status: 200,
    description: '2FA verification required',
    type: Requires2FAResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  @Post('login')
  login(
    @Body() loginDto: LoginDto,
  ): Promise<LoginResponseDto | Requires2FAResponseDto> {
    return this.authService.login(loginDto);
  }

  @ApiOperation({ summary: 'Google OAuth login' })
  @ApiResponse({ status: 302, description: 'Redirects to Google login' })
  @Get('google')
  @UseGuards(GoogleAuthGuard)
  async googleAuth() {
    // This route initiates Google OAuth flow
  }

  @ApiOperation({ summary: 'Google OAuth callback' })
  @ApiResponse({
    status: 302,
    description:
      'Redirects to frontend with tokens and user data after successful Google login',
  })
  @Get('google/callback')
  @UseGuards(GoogleAuthGuard)
  async googleAuthRedirect(@Req() req, @Response() res) {
    try {
      const { tokens, user } = await this.authService.googleLogin(req);
      const frontendUrl = this.configService.get<string>('FRONTEND_URL');
      const redirectUrl = new URL('/auth/callback', frontendUrl);

      // Add tokens to URL parameters
      redirectUrl.searchParams.append('accessToken', tokens.accessToken);
      redirectUrl.searchParams.append('refreshToken', tokens.refreshToken);

      // Add user data to URL parameters
      const userData = {
        _id: user._id,
        username: user.username,
        email: user.email,
        emailVerified: user.emailVerified,
        accountType: user.accountType,
        role: user.role,
        twoFactorMethod: user.twoFactorMethod,
        twoFactorEnabled: user.twoFactorEnabled,
        avatar: user.avatar,
      };

      redirectUrl.searchParams.append(
        'userData',
        Buffer.from(JSON.stringify(userData)).toString('base64'),
      );

      // Add a flag to indicate if this was a new account or linked to existing
      redirectUrl.searchParams.append(
        'isLinked',
        req.user.isLinked ? 'true' : 'false',
      );

      return res.redirect(redirectUrl.toString());
    } catch {
      const frontendUrl = this.configService.get<string>('FRONTEND_URL');
      const errorUrl = new URL('/auth/error', frontendUrl);
      errorUrl.searchParams.append('error', 'Authentication failed');

      return res.redirect(errorUrl.toString());
    }
  }

  @ApiOperation({ summary: 'Refresh access token' })
  @ApiResponse({
    status: 200,
    description: 'Tokens successfully refreshed',
    schema: {
      properties: {
        accessToken: { type: 'string', description: 'New JWT access token' },
        refreshToken: { type: 'string', description: 'New JWT refresh token' },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  @Post('refresh')
  refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.refreshToken(refreshTokenDto.refreshToken);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout user' })
  @ApiResponse({
    status: 200,
    description: 'User successfully logged out',
    schema: {
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @UseGuards(JwtAuthGuard)
  @Post('logout')
  logout(@Request() req) {
    return this.authService.logout(req.user._id);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Generate 2FA secret and QR code' })
  @ApiResponse({
    status: 200,
    description: 'Returns 2FA secret and QR code',
    schema: {
      properties: {
        secret: { type: 'string', description: '2FA secret key' },
        qrCode: { type: 'string', description: 'QR code data URL' },
      },
    },
  })
  @UseGuards(JwtAuthGuard)
  @Post('2fa/generate')
  generate2FA(@Request() req) {
    return this.authService.generate2FASecret(req.user._id);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Enable 2FA' })
  @UseGuards(JwtAuthGuard)
  @Post('2fa/enable')
  enable2FA(@Request() req, @Body() enable2FADto: Enable2FADto) {
    return this.authService.enable2FA(req.user, enable2FADto);
  }

  @ApiOperation({ summary: 'Verify 2FA and complete login' })
  @ApiResponse({
    status: 200,
    description:
      'Returns access and refresh tokens after successful 2FA verification',
    type: Verify2FAResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid verification code or user ID',
  })
  @Post('2fa/verify')
  verify2FA(@Body() verify2FADto: Verify2FADto): Promise<Verify2FAResponseDto> {
    return this.authService.verify2FA(verify2FADto.userId, verify2FADto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Verify 2FA code without enabling it' })
  @ApiResponse({ status: 200, description: 'Returns verification status' })
  @UseGuards(JwtAuthGuard)
  @Post('2fa/verify-setup')
  verify2FASetup(@Request() req, @Body() verify2FADto: Verify2FADto) {
    return this.authService.verify2FASetup(req.user, verify2FADto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Disable 2FA' })
  @ApiResponse({ status: 200, description: '2FA successfully disabled' })
  @UseGuards(JwtAuthGuard)
  @Post('2fa/disable')
  disable2FA(@Request() req) {
    return this.authService.disable2FA(req.user);
  }

  @Throttle({ default: { limit: 3, ttl: 60000 } })
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  @Post('forgot-password')
  forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @ApiOperation({ summary: 'Reset password using token' })
  @ApiResponse({ status: 200, description: 'Password successfully reset' })
  @Post('reset-password')
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create new user (Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: CreateUserResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or user already exists',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access required',
  })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Post('create-user')
  createUser(
    @Body() createUserDto: CreateUserDto,
  ): Promise<CreateUserResponseDto> {
    return this.authService.createUser(createUserDto);
  }

  @ApiOperation({ summary: 'Facebook OAuth login' })
  @ApiResponse({ status: 302, description: 'Redirects to Facebook login' })
  @Get('facebook')
  @UseGuards(AuthGuard('facebook'))
  async facebookAuth() {
    // Initiates Facebook OAuth flow
  }

  @ApiOperation({ summary: 'Facebook OAuth callback' })
  @ApiResponse({
    status: 200,
    description: 'Returns user data and token after successful Facebook login',
    schema: {
      properties: {
        token: { type: 'string' },
        user: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            email: { type: 'string' },
            username: { type: 'string' },
          },
        },
      },
    },
  })
  @Get('facebook/callback')
  @UseGuards(AuthGuard('facebook'))
  facebookAuthRedirect(@Request() req) {
    return this.authService.facebookLogin(req);
  }
}
