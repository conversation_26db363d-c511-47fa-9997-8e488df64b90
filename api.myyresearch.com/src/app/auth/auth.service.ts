import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcrypt';
import * as jose from 'jose';
import {
  User,
  UserDocument,
  AccountType,
  UserRole,
  TwoFactorMethod,
  UserStatus,
  DocumentStatus,
} from '../users/schemas/user.schema';
import { RegisterDto, UserType } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { Inject } from '@nestjs/common';
import {
  RefreshToken,
  RefreshTokenDocument,
} from './schemas/refresh-token.schema';
import {
  InstructorProfile,
  InstructorProfileDocument,
} from '../instructor-profiles/schemas/instructor-profile.schema';
import * as crypto from 'crypto';
import * as speakeasy from 'speakeasy';
import * as qrcode from 'qrcode';
import { Enable2FADto, Verify2FADto } from './dto/two-factor.dto';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { BadRequestException } from '@nestjs/common';
import { LoginResponseDto, Requires2FAResponseDto } from './dto/login.dto';
import { RegisterResponseDto } from './dto/register.dto';
import { CreateUserDto, CreateUserResponseDto } from './dto/create-user.dto';
import { Resend } from 'resend';
import { UserProfileDto } from '../users/dto/user-profile.dto';
@Injectable()
export class AuthService {
  private encryptionKey: Uint8Array;
  private resend: Resend;

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(RefreshToken.name)
    private refreshTokenModel: Model<RefreshTokenDocument>,
    @InjectModel(InstructorProfile.name)
    private instructorProfileModel: Model<InstructorProfileDocument>,
    private jwtService: JwtService,
    @Inject('JWT_REFRESH_TOKEN_SERVICE')
    private jwtRefreshService: JwtService,
  ) {
    const rawKey = process.env.TOKEN_ENCRYPTION_KEY || 'your-encryption-key';
    this.encryptionKey = new Uint8Array(
      crypto.createHash('sha256').update(rawKey).digest(),
    );
    this.resend = new Resend(process.env.RESEND_API_KEY);
  }

  private async encodeToken(token: string): Promise<string> {
    const encoder = new TextEncoder();
    const jwe = await new jose.CompactEncrypt(encoder.encode(token))
      .setProtectedHeader({ alg: 'dir', enc: 'A256GCM' })
      .encrypt(this.encryptionKey);

    return jwe;
  }

  private async generateTokens(user: UserDocument) {
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.sign({
        userId: user._id,
        email: user.email,
        username: user.username,
        role: user.role,
      }),
      this.jwtRefreshService.sign({
        userId: user._id,
        email: user.email,
        username: user.username,
        role: user.role,
      }),
    ]);

    // Store refresh token in separate collection
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days from now

    await this.refreshTokenModel.create({
      userId: user._id,
      token: await bcrypt.hash(refreshToken, 10),
      expiresAt,
    });

    // Encode tokens before returning
    const [encodedAccessToken, encodedRefreshToken] = await Promise.all([
      this.encodeToken(accessToken),
      this.encodeToken(refreshToken),
    ]);

    return {
      accessToken: encodedAccessToken,
      refreshToken: encodedRefreshToken,
    };
  }

  async register(registerDto: RegisterDto): Promise<RegisterResponseDto> {
    const {
      username,
      email,
      password,
      userType,
      instructorData,
      accountType,
      role,
      twoFactorMethod,
      status,
    } = registerDto;

    // Check if user exists with either username or email
    const existingUser = await this.userModel.findOne({
      $or: [
        { username: username.toLowerCase() },
        { email: email.toLowerCase() },
      ],
    });

    if (existingUser) {
      if (existingUser.username.toLowerCase() === username.toLowerCase()) {
        throw new BadRequestException('Username is already taken');
      }
      if (existingUser.email.toLowerCase() === email.toLowerCase()) {
        throw new BadRequestException('Email is already registered');
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Determine user role based on userType
    let userRole = role || UserRole.USER;
    if (userType === UserType.INSTRUCTOR) {
      userRole = UserRole.INSTRUCTOR;
    }

    // Create new user with lowercase username and email
    const user = await this.userModel.create({
      username: username.toLowerCase(),
      email: email.toLowerCase(),
      password: hashedPassword,
      emailVerified: null,
      accountType: accountType || AccountType.EMAIL,
      role: userRole,
      twoFactorMethod: twoFactorMethod || TwoFactorMethod.NONE,
      twoFactorSecret: null,
      status: status || UserStatus.ACTIVE,
      documentStatus: DocumentStatus.ACTIVE,
    });

    // Create instructor profile if user is registering as instructor
    if (userType === UserType.INSTRUCTOR && instructorData) {
      await this.instructorProfileModel.create({
        user: user._id,
        teachingExperience: instructorData.teachingExperience,
        teachingExperienceOther: instructorData.teachingExperienceOther,
        experienceLevel: instructorData.experienceLevel,
        audienceReach: instructorData.audienceReach,
      });
    }

    const tokens = await this.generateTokens(user);

    // Create user profile object without sensitive information
    const userProfile: UserProfileDto = {
      _id: user._id,
      username: user.username,
      email: user.email,
      emailVerified: user.emailVerified,
      accountType: user.accountType,
      role: user.role,
      twoFactorMethod: user.twoFactorMethod,
      twoFactorEnabled: user.twoFactorEnabled,
      avatar: user.avatar,
    };

    return {
      tokens,
      user: userProfile,
    };
  }

  async login(
    loginDto: LoginDto,
  ): Promise<LoginResponseDto | Requires2FAResponseDto> {
    const { identifier, password } = loginDto;

    // First find the user without status checks to provide appropriate error messages
    const user = await this.userModel.findOne({
      $or: [{ email: identifier }, { username: identifier }],
    });

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (user.status !== UserStatus.ACTIVE) {
      throw new UnauthorizedException('Account is not active');
    }

    if (user.documentStatus !== DocumentStatus.ACTIVE) {
      throw new UnauthorizedException('Account has been deleted');
    }

    if (!(await bcrypt.compare(password, user.password))) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if 2FA is enabled
    if (user.twoFactorEnabled) {
      return {
        requires2FA: true,
        userId: user._id,
      };
    }

    const tokens = await this.generateTokens(user);

    // Create user profile object without sensitive information
    const userProfile: UserProfileDto = {
      _id: user._id,
      username: user.username,
      email: user.email,
      emailVerified: user.emailVerified,
      accountType: user.accountType,
      role: user.role,
      twoFactorMethod: user.twoFactorMethod,
      twoFactorEnabled: user.twoFactorEnabled,
      avatar: user.avatar,
    };

    return {
      tokens,
      user: userProfile,
    };
  }

  async refreshToken(
    encodedRefreshToken: string,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      const decoder = new TextDecoder();

      const { plaintext } = await jose.compactDecrypt(
        encodedRefreshToken,
        this.encryptionKey,
      );
      const refreshToken = decoder.decode(plaintext);

      const decoded = this.jwtRefreshService.verify(refreshToken);
      const user = await this.userModel.findById(decoded.userId);

      // Find and validate refresh token
      const tokenDoc = await this.refreshTokenModel.findOne({
        userId: user._id,
        isRevoked: false,
        expiresAt: { $gt: new Date() },
      });

      if (!tokenDoc || !(await bcrypt.compare(refreshToken, tokenDoc.token))) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Revoke the used refresh token
      await tokenDoc.updateOne({ isRevoked: true });

      return this.generateTokens(user);
    } catch {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(userId: string) {
    // Revoke all refresh tokens for the user
    await this.refreshTokenModel.updateMany(
      { userId, isRevoked: false },
      { isRevoked: true },
    );
    return { success: true };
  }

  async checkUsername(username: string) {
    const user = await this.userModel.findOne({ username }).lean();
    return {
      username,
      exists: !!user,
      available: !user,
    };
  }

  async validateGoogleUser(profile: any) {
    console.log('Starting Google user validation...');
    console.log('Received profile:', profile);

    const { email, id: googleId, username, avatar } = profile;
    console.log('Extracted email:', email, 'googleId:', googleId);

    // First try to find user by Google ID in linked accounts
    console.log('Searching for user by Google ID...');
    let user = await this.userModel.findOne({
      linkedAccounts: {
        $elemMatch: {
          provider: AccountType.GOOGLE,
          providerId: googleId,
        },
      },
    });
    console.log('User found by Google ID:', !!user);

    // If no user found by Google ID, try to find by email
    if (!user) {
      console.log('No user found by Google ID, searching by email...');
      user = await this.userModel.findOne({ email });
      console.log('User found by email:', !!user);
    }

    if (user) {
      // Check if this Google account is already linked
      const hasGoogleAccount =
        user.accountType === AccountType.GOOGLE ||
        user.linkedAccounts?.some(
          (account) =>
            account.provider === AccountType.GOOGLE &&
            account.providerId === googleId,
        );
      console.log('Has Google account already linked:', hasGoogleAccount);

      if (!hasGoogleAccount) {
        console.log('Linking new Google account to existing user...');
        // Link the Google account
        await this.userModel.updateOne(
          { _id: user._id },
          {
            $push: {
              linkedAccounts: {
                provider: AccountType.GOOGLE,
                providerId: googleId,
                email: email,
              },
            },
          },
        );
        console.log('Google account linked successfully');
      }
    } else {
      console.log('No existing user found, creating new user...');
      // Create new user if doesn't exist
      user = await this.userModel.create({
        email,
        username: username,
        password: null,
        emailVerified: new Date(),
        accountType: AccountType.GOOGLE,
        role: UserRole.USER,
        twoFactorMethod: TwoFactorMethod.NONE,
        twoFactorSecret: null,
        status: UserStatus.ACTIVE,
        documentStatus: DocumentStatus.ACTIVE,
        avatar: avatar,
      });
      console.log('New user created:', user._id);
    }

    console.log('Creating user profile object...');
    // Create user profile object
    const userProfile: UserProfileDto = {
      _id: user._id,
      username: user.username,
      email: user.email,
      emailVerified: user.emailVerified,
      accountType: user.accountType,
      role: user.role,
      twoFactorMethod: user.twoFactorMethod,
      twoFactorEnabled: user.twoFactorEnabled,
      avatar: user.avatar,
    };
    console.log('User profile created:', userProfile);

    console.log('Google user validation completed');
    return { user, userProfile };
  }

  async googleLogin(req) {
    if (!req.user) {
      throw new UnauthorizedException('No user from google');
    }

    const { user, userProfile } = req.user;
    const tokens = await this.generateTokens(user);

    return {
      tokens,
      user: userProfile,
    };
  }

  async generate2FASecret(userId: string) {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Generate secret
    const secret = speakeasy.generateSecret({
      name: `Hapify : ${user.username}`,
    });

    // Save secret temporarily (not verified yet)
    await this.userModel.updateOne(
      { _id: userId },
      {
        twoFactorSecret: secret.base32,
        twoFactorMethod: TwoFactorMethod.AUTHENTICATOR,
      },
    );

    // Generate QR code
    const otpauthUrl = secret.otpauth_url;
    const qrCodeDataUrl = await qrcode.toDataURL(otpauthUrl);

    return {
      secret: secret.base32,
      qrCode: qrCodeDataUrl,
    };
  }

  async enable2FA(user: UserDocument, dto: Enable2FADto) {
    if (!user.twoFactorSecret) {
      throw new BadRequestException('Invalid request');
    }

    console.log(dto);
    // Verify the code
    const isValid = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token: dto.code,
    });

    if (!isValid) {
      throw new BadRequestException('Invalid verification code');
    }

    // Enable 2FA
    await this.userModel.updateOne(
      { _id: user._id },
      { twoFactorEnabled: true },
    );

    return { success: true };
  }

  async verify2FA(userId: string, dto: Verify2FADto) {
    const user = await this.userModel.findById(userId);

    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (!user.twoFactorSecret) {
      throw new BadRequestException('2FA secret not set');
    }

    if (!user.twoFactorEnabled) {
      throw new BadRequestException('2FA is not enabled');
    }

    const isValid = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token: dto.code,
    });

    if (!isValid) {
      throw new BadRequestException('Invalid verification code');
    }

    const tokens = await this.generateTokens(user);

    // Create user profile object without sensitive information
    const userProfile: UserProfileDto = {
      _id: user._id,
      username: user.username,
      email: user.email,
      emailVerified: user.emailVerified,
      accountType: user.accountType,
      role: user.role,
      twoFactorMethod: user.twoFactorMethod,
      twoFactorEnabled: user.twoFactorEnabled,
      avatar: user.avatar,
    };

    return {
      tokens,
      user: userProfile,
    };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    const { email } = forgotPasswordDto;
    const user = await this.userModel.findOne({ email });

    if (!user) {
      // For security reasons, we'll return success even if the email doesn't exist
      return {
        message: 'If the email exists, a password reset link will be sent',
      };
    }

    // Generate reset token (valid for 1 hour)
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now

    // Hash the token before saving
    const hashedToken = await bcrypt.hash(resetToken, 10);

    // Save the reset token and expiry
    await this.userModel.updateOne(
      { _id: user._id },
      {
        resetPasswordToken: hashedToken,
        resetPasswordExpires: resetTokenExpiry,
      },
    );

    // Create reset password URL
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

    try {
      await this.resend.emails.send({
        from: 'Your App <<EMAIL>>',
        to: email,
        subject: 'Password Reset Request',
        html: `
          <h1>Password Reset Request</h1>
          <p>Hello ${user.username},</p>
          <p>You requested to reset your password. Click the link below to reset it:</p>
          <p><a href="${resetUrl}">Reset Password</a></p>
          <p>This link will expire in 1 hour.</p>
          <p>If you didn't request this, please ignore this email.</p>
          <p>Best regards,<br>Your App Team</p>
        `,
      });

      return {
        message: 'If the email exists, a password reset link will be sent',
      };
    } catch (error) {
      // Log the error but don't expose it to the user
      console.error('Error sending reset password email:', error);
      throw new BadRequestException('Failed to send reset password email');
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { token, newPassword } = resetPasswordDto;

    // Find user with valid reset token
    const user = await this.userModel.findOne({
      resetPasswordToken: { $exists: true },
      resetPasswordExpires: { $gt: new Date() },
    });

    if (!user) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Verify token
    const isValidToken = await bcrypt.compare(token, user.resetPasswordToken);
    if (!isValidToken) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password and clear reset token fields
    await this.userModel.updateOne(
      { _id: user._id },
      {
        password: hashedPassword,
        resetPasswordToken: null,
        resetPasswordExpires: null,
      },
    );

    // Revoke all refresh tokens for the user
    await this.refreshTokenModel.updateMany(
      { userId: user._id, isRevoked: false },
      { isRevoked: true },
    );

    return { message: 'Password has been reset successfully' };
  }

  async verify2FASetup(user: UserDocument, dto: Verify2FADto) {
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (!user.twoFactorSecret) {
      throw new BadRequestException('2FA secret not set');
    }

    if (user.twoFactorEnabled) {
      throw new BadRequestException('2FA is already enabled');
    }

    const isValid = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token: dto.code,
    });

    if (isValid) {
      // Update user to enable 2FA when verification is successful
      await this.userModel.updateOne(
        { _id: user._id },
        { twoFactorEnabled: true },
      );
    }

    return {
      isValid,
      message: isValid
        ? '2FA has been successfully enabled'
        : 'Invalid verification code',
    };
  }

  async disable2FA(user: UserDocument) {
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (!user.twoFactorEnabled) {
      throw new BadRequestException('2FA is not enabled');
    }

    // Disable 2FA and clear the secret
    await this.userModel.updateOne(
      { _id: user._id },
      {
        twoFactorEnabled: false,
        twoFactorSecret: null,
        twoFactorMethod: TwoFactorMethod.NONE,
      },
    );

    return {
      success: true,
      message: '2FA has been successfully disabled',
    };
  }

  async createUser(
    createUserDto: CreateUserDto,
  ): Promise<CreateUserResponseDto> {
    const { username, email, password } = createUserDto;

    // Check if user exists
    const existingUser = await this.userModel.findOne({
      $or: [{ email }, { username }],
    });

    if (existingUser) {
      throw new BadRequestException(
        'User with this email or username already exists',
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const user = await this.userModel.create({
      ...createUserDto,
      password: hashedPassword,
      emailVerified: null,
      accountType: createUserDto.accountType || AccountType.EMAIL,
      role: createUserDto.role || UserRole.USER,
      twoFactorMethod: createUserDto.twoFactorMethod || TwoFactorMethod.NONE,
      twoFactorSecret: null,
      status: createUserDto.status || UserStatus.ACTIVE,
      documentStatus: DocumentStatus.ACTIVE,
    });

    // Create UserProfileDto from created user
    const userProfile: UserProfileDto = {
      _id: user._id,
      username: user.username,
      email: user.email,
      emailVerified: user.emailVerified,
      accountType: user.accountType,
      role: user.role,
      twoFactorMethod: user.twoFactorMethod,
      twoFactorEnabled: user.twoFactorEnabled,
      avatar: user.avatar,
    };

    return {
      success: true,
      message: 'User created successfully',
      user: userProfile,
    };
  }

  async validateFacebookUser(profile: any) {
    const { email, id: facebookId } = profile;

    // First try to find user by Facebook ID in linked accounts
    let user = await this.userModel.findOne({
      linkedAccounts: {
        $elemMatch: {
          provider: AccountType.FACEBOOK,
          providerId: facebookId,
        },
      },
    });

    // If no user found by Facebook ID, try to find by email
    if (!user) {
      user = await this.userModel.findOne({ email });
    }

    if (user) {
      // Check if this Facebook account is already linked
      const hasFacebookAccount = user.linkedAccounts?.some(
        (account) =>
          account.provider === AccountType.FACEBOOK &&
          account.providerId === facebookId,
      );

      if (!hasFacebookAccount) {
        // Link the Facebook account
        await this.userModel.updateOne(
          { _id: user._id },
          {
            $push: {
              linkedAccounts: {
                provider: AccountType.FACEBOOK,
                providerId: facebookId,
                email: email,
              },
            },
          },
        );
      }
    } else {
      // Create new user if doesn't exist
      user = await this.userModel.create({
        email,
        username: profile.username || email.split('@')[0],
        password: null,
        emailVerified: new Date(),
        accountType: AccountType.FACEBOOK,
        role: UserRole.USER,
        twoFactorMethod: TwoFactorMethod.NONE,
        twoFactorSecret: null,
        status: UserStatus.ACTIVE,
        documentStatus: DocumentStatus.ACTIVE,
        linkedAccounts: [
          {
            provider: AccountType.FACEBOOK,
            providerId: facebookId,
            email: email,
          },
        ],
      });
    }

    const token = this.jwtService.sign({
      userId: user._id,
      email: user.email,
      username: user.username,
      role: user.role,
    });

    return { user, token };
  }

  async facebookLogin(req) {
    if (!req.user) {
      throw new UnauthorizedException('No user from facebook');
    }

    return {
      token: req.user.token,
      user: req.user.user,
    };
  }
}
