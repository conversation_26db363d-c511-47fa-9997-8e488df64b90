import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, MinLength, IsEmail } from 'class-validator';
import { UserProfileDto } from '../../users/dto/user-profile.dto';

export class UpdateUserDto {
  @ApiPropertyOptional({
    example: 'john_doe_updated',
    description: 'New username',
  })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'New email address',
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({
    example: 'CurrentPassword123',
    description: 'Current password (required for password change)',
  })
  @IsString()
  @IsOptional()
  currentPassword?: string;

  @ApiPropertyOptional({
    example: 'NewPassword123',
    description: 'New password',
    minimum: 6,
    // pattern: '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{6,}$/',
  })
  @IsString()
  @IsOptional()
  @MinLength(6)
  // @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,}$/, {
  //   message:
  //     'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  // })
  newPassword?: string;
}

export class UpdateUserResponseDto {
  @ApiProperty({
    example: true,
    description: 'Whether the update was successful',
  })
  success: boolean;

  @ApiProperty({
    example: 'User details updated successfully',
    description: 'Success message',
  })
  message: string;

  @ApiProperty({
    description: 'Updated user profile information',
    type: UserProfileDto,
  })
  user: UserProfileDto;
}
