import { ApiProperty } from '@nestjs/swagger';

export class GoogleAuthResponseDto {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT token',
  })
  token: string;

  @ApiProperty({
    description: 'User information',
    example: {
      _id: '507f1f77bcf86cd799439011',
      email: '<EMAIL>',
      username: 'john_doe',
    },
  })
  user: {
    _id: string;
    email: string;
    username: string;
  };
}
