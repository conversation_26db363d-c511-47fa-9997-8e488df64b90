import { IsNotEmpty, IsString, Min<PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ResetPasswordDto {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'Password reset token',
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    example: 'Password123',
    description: 'New password',
    minimum: 6,
    // pattern: '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{6,}$/',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  // @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{6,}$/, {
  //   message:
  //     'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  // })
  newPassword: string;
}
