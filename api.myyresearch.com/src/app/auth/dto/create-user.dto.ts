import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsString,
  MinLength,
  IsEnum,
  IsOptional,
} from 'class-validator';
import {
  AccountType,
  UserRole,
  TwoFactorMethod,
  UserStatus,
} from '../../users/schemas/user.schema';
import { UserProfileDto } from '../../users/dto/user-profile.dto';

export class CreateUserDto {
  @ApiProperty({ example: 'john_doe', description: 'Username' })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email address' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ example: 'password123', description: 'Password' })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiPropertyOptional({ enum: AccountType, description: 'Account type' })
  @IsEnum(AccountType)
  @IsOptional()
  accountType?: AccountType;

  @ApiPropertyOptional({ enum: UserRole, description: 'User role' })
  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole;

  @ApiPropertyOptional({ enum: TwoFactorMethod, description: '2FA method' })
  @IsEnum(TwoFactorMethod)
  @IsOptional()
  twoFactorMethod?: TwoFactorMethod;

  @ApiPropertyOptional({ enum: UserStatus, description: 'User status' })
  @IsEnum(UserStatus)
  @IsOptional()
  status?: UserStatus;
}

export class CreateUserResponseDto {
  @ApiProperty({ example: true, description: 'Operation success status' })
  success: boolean;

  @ApiProperty({
    example: 'User created successfully',
    description: 'Response message',
  })
  message: string;

  @ApiProperty({
    description: 'Created user profile information',
    type: UserProfileDto,
  })
  user: UserProfileDto;
}
