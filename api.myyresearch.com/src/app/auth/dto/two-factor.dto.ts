import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class Enable2FADto {
  @ApiProperty({
    example: '123456',
    description: 'Verification code from authenticator app',
  })
  @IsString()
  @IsNotEmpty()
  code: string;
}

export class Verify2FADto {
  @ApiProperty({
    example: '123456',
    description: 'Verification code from authenticator app',
  })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({
    example: '507f1f77bcf86cd799439011',
    description: 'User ID',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;
}
