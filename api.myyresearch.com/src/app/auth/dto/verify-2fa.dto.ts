import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Length } from 'class-validator';
import { UserProfileDto } from '../../users/dto/user-profile.dto';

export class Verify2FADto {
  @ApiProperty({
    example: '123456',
    description: '6-digit verification code from authenticator app',
    minLength: 6,
    maxLength: 6,
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'Verification code must be exactly 6 digits' })
  code: string;

  @ApiProperty({
    example: '507f1f77bcf86cd799439011',
    description: 'User ID for verification',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;
}

export class TokensDto {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT access token after successful 2FA verification',
  })
  accessToken: string;

  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT refresh token after successful 2FA verification',
  })
  refreshToken: string;
}

export class Verify2FAResponseDto {
  @ApiProperty({
    description: 'Authentication tokens',
    type: TokensDto,
  })
  tokens: TokensDto;

  @ApiProperty({
    description: 'User profile information',
    type: UserProfileDto,
  })
  user: UserProfileDto;
}
