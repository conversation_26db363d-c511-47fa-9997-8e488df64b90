import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { TokensDto } from './verify-2fa.dto';
import { UserProfileDto } from '../../users/dto/user-profile.dto';

export class LoginDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email or username for login',
  })
  @IsString()
  @IsNotEmpty()
  identifier: string;

  @ApiProperty({
    example: 'password123',
    description: 'User password',
  })
  @IsString()
  @IsNotEmpty()
  password: string;
}

export class LoginResponseDto {
  @ApiProperty({
    description: 'Authentication tokens',
    type: TokensDto,
  })
  tokens: TokensDto;

  @ApiProperty({
    description: 'User profile information',
    type: UserProfileDto,
  })
  user: UserProfileDto;
}

export class Requires2FAResponseDto {
  @ApiProperty({
    example: true,
    description: 'Indicates that 2FA verification is required',
  })
  requires2FA: boolean;

  @ApiProperty({
    example: '507f1f77bcf86cd799439011',
    description: 'User ID needed for 2FA verification',
  })
  userId: string;
}
