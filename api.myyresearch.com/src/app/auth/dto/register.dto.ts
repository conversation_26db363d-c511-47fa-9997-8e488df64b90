import {
  Is<PERSON><PERSON>,
  IsNotEmpty,
  IsString,
  <PERSON><PERSON>ength,
  IsEnum,
  IsOptional,
  ValidateNested,
  ValidateIf,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  AccountType,
  UserRole,
  TwoFactorMethod,
  UserStatus,
} from '../../users/schemas/user.schema';
import { TokensDto } from './verify-2fa.dto';
import { UserProfileDto } from '../../users/dto/user-profile.dto';
import { InstructorProfileDto } from '../../instructor-profiles/dto/instructor-profile.dto';

export enum UserType {
  STUDENT = 'student',
  INSTRUCTOR = 'instructor',
}

export class RegisterDto {
  @ApiProperty({ example: 'john_doe', description: 'Username' })
  @IsString({ message: 'Username must be a string' })
  @IsNotEmpty({ message: 'Username is required' })
  username: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email address' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @ApiProperty({ example: 'password123', description: 'Password' })
  @IsString({ message: 'Password must be a string' })
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  password: string;

  @ApiPropertyOptional({
    enum: UserType,
    description: 'User type - student or instructor',
    example: UserType.STUDENT,
  })
  @IsEnum(UserType)
  @IsOptional()
  userType?: UserType;

  @ApiPropertyOptional({
    description:
      'Instructor profile data (required when userType is instructor)',
    type: InstructorProfileDto,
  })
  @ValidateNested()
  @Type(() => InstructorProfileDto)
  @ValidateIf((o) => o.userType === UserType.INSTRUCTOR)
  @IsOptional()
  instructorData?: InstructorProfileDto;

  @ApiPropertyOptional({ enum: AccountType, description: 'Account type' })
  @IsEnum(AccountType)
  @IsOptional()
  accountType?: AccountType;

  @ApiPropertyOptional({ enum: UserRole, description: 'User role' })
  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole;

  @ApiPropertyOptional({ enum: TwoFactorMethod, description: '2FA method' })
  @IsEnum(TwoFactorMethod)
  @IsOptional()
  twoFactorMethod?: TwoFactorMethod;

  @ApiPropertyOptional({ enum: UserStatus, description: 'User status' })
  @IsEnum(UserStatus)
  @IsOptional()
  status?: UserStatus;
}

export class RegisterResponseDto {
  @ApiProperty({
    description: 'Authentication tokens',
    type: TokensDto,
  })
  tokens: TokensDto;

  @ApiProperty({
    description: 'User profile information',
    type: UserProfileDto,
  })
  user: UserProfileDto;
}
