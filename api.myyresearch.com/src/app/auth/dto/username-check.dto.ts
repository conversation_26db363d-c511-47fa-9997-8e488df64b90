import { ApiProperty } from '@nestjs/swagger';

export class UsernameCheckDto {
  @ApiProperty({
    example: 'john_doe',
    description: 'Username that was checked',
  })
  username: string;

  @ApiProperty({
    example: true,
    description: 'Whether the username exists',
  })
  exists: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether the username is available',
  })
  available: boolean;
}
