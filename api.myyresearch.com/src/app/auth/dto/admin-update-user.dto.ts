import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsString,
  MinLength,
  IsEnum,
  IsOptional,
  IsMongoId,
} from 'class-validator';
import {
  AccountType,
  UserRole,
  TwoFactorMethod,
  UserStatus,
} from '../../users/schemas/user.schema';
import { UserProfileDto } from '../../users/dto/user-profile.dto';

export class AdminUpdateUserDto {
  @ApiProperty({
    example: '507f1f77bcf86cd799439011',
    description: 'User ID to update',
  })
  @IsMongoId()
  @IsNotEmpty()
  userId: string;

  @ApiPropertyOptional({ example: 'john_doe', description: 'Username' })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Email address',
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({
    example: 'newPassword123',
    description: 'New password',
  })
  @IsString()
  @MinLength(6)
  @IsOptional()
  password?: string;

  @ApiPropertyOptional({ enum: AccountType, description: 'Account type' })
  @IsEnum(AccountType)
  @IsOptional()
  accountType?: AccountType;

  @ApiPropertyOptional({ enum: UserRole, description: 'User role' })
  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole;

  @ApiPropertyOptional({ enum: TwoFactorMethod, description: '2FA method' })
  @IsEnum(TwoFactorMethod)
  @IsOptional()
  twoFactorMethod?: TwoFactorMethod;

  @ApiPropertyOptional({ enum: UserStatus, description: 'User status' })
  @IsEnum(UserStatus)
  @IsOptional()
  status?: UserStatus;
}

export class AdminUpdateUserResponseDto {
  @ApiProperty({ example: true, description: 'Operation success status' })
  success: boolean;

  @ApiProperty({
    example: 'User updated successfully',
    description: 'Response message',
  })
  message: string;

  @ApiProperty({
    description: 'Updated user profile information',
    type: UserProfileDto,
  })
  user: UserProfileDto;
}
