import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Enrollment, EnrollmentDocument } from './schemas/enrollment.schema';
import { Course, CourseDocument } from '../courses/schemas/course.schema';
import { CreateEnrollmentDto } from './dto/create-enrollment.dto';
import { UpdateLessonProgressDto } from './dto/update-lesson-progress.dto';
import { EnrollmentFilterDto } from './dto/enrollment-filter.dto';
import { GetAllEnrollmentsResponseDto } from './dto/get-all-enrollments-response.dto';
import { MinimalEnrollmentDto } from './dto/minimal-enrollment.dto';
import { UserRole } from '../users/schemas/user.schema';

@Injectable()
export class EnrollmentsService {
  constructor(
    @InjectModel(Enrollment.name)
    private readonly enrollmentModel: Model<EnrollmentDocument>,
    @InjectModel(Course.name)
    private readonly courseModel: Model<CourseDocument>,
  ) {}

  async create(userId: string, createEnrollmentDto: CreateEnrollmentDto) {
    const enrollment = new this.enrollmentModel({
      userId: new Types.ObjectId(userId),
      courseId: new Types.ObjectId(createEnrollmentDto.courseId),
      lastAccessedAt: new Date(),
    });
    return enrollment.save();
  }

  async findAll(
    userId: string,
    filterDto: EnrollmentFilterDto,
  ): Promise<GetAllEnrollmentsResponseDto> {
    const { page = 1, limit = 10, search } = filterDto;
    const skip = (page - 1) * limit;

    const query: any = { userId };

    if (search) {
      // We'll need to fetch course IDs that match the search first
      const courseIds = await this.courseModel
        .find({ name: { $regex: search, $options: 'i' } })
        .select('_id')
        .lean()
        .exec();

      query.courseId = { $in: courseIds.map((c) => c._id) };
    }

    const [enrollments, total] = await Promise.all([
      this.enrollmentModel
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean()
        .exec(),
      this.enrollmentModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(total / limit);
    const hasMore = page < totalPages;

    // Get all unique course IDs from enrollments
    const courseIds = enrollments.map((enrollment) => enrollment.courseId);

    // Fetch course details
    const courses = await this.courseModel
      .find({ _id: { $in: courseIds } })
      .select('_id name publicId title thumbnail')
      .lean()
      .exec();

    // Create a map for quick lookup
    const courseMap = new Map<string, any>(
      courses.map((course: any): [string, any] => [
        course._id.toString(),
        {
          name: course.name as string,
          publicId: course.publicId as string,
          title: course.title as string,
          thumbnailUrl: course.thumbnail.url as string,
        },
      ]),
    );

    const minimalEnrollments: MinimalEnrollmentDto[] = enrollments.map(
      (enrollment: any) => {
        const courseId = enrollment.courseId.toString();
        const courseDetails = courseMap.get(courseId) || {
          name: 'Unknown Course',
          publicId: '',
          title: 'Unknown Course',
          thumbnailUrl: '',
        };

        return {
          _id: enrollment._id.toString(),
          user: enrollment.userId.toString(),
          courseId,
          coursePublicId: courseDetails.publicId,
          courseName: courseDetails.name,
          courseTitle: courseDetails.title,
          courseThumbnailUrl: courseDetails.thumbnailUrl,
          progress: enrollment.progress,
          lastAccessedAt: enrollment.lastAccessedAt,
          createdAt: enrollment.createdAt,
          updatedAt: enrollment.updatedAt,
        };
      },
    );

    return {
      enrollments: minimalEnrollments,
      total,
      page,
      totalPages,
      hasMore,
    };
  }

  async findOne(userId: string, courseId: string) {
    const enrollment = await this.enrollmentModel
      .findOne({
        userId: new Types.ObjectId(userId),
        courseId: new Types.ObjectId(courseId),
      })
      .populate('courseId')
      .exec();

    if (!enrollment) {
      throw new NotFoundException('Enrollment not found');
    }

    return enrollment;
  }

  async updateLessonProgress(
    userId: string,
    courseId: string,
    updateLessonProgressDto: UpdateLessonProgressDto,
  ) {
    const enrollment = await this.enrollmentModel.findOne({
      userId: new Types.ObjectId(userId),
      courseId: new Types.ObjectId(courseId),
    });

    if (!enrollment) {
      throw new NotFoundException('Enrollment not found');
    }

    // Find if lesson progress already exists
    const lessonProgressIndex = enrollment.lessonsProgress.findIndex(
      (progress) =>
        progress.lessonId.toString() === updateLessonProgressDto.lessonId &&
        progress.sectionId.toString() === updateLessonProgressDto.sectionId,
    );

    if (lessonProgressIndex === -1) {
      // Add new lesson progress
      enrollment.lessonsProgress.push({
        lessonId: new Types.ObjectId(updateLessonProgressDto.lessonId),
        sectionId: new Types.ObjectId(updateLessonProgressDto.sectionId),
        completed: true,
        completedAt: new Date(),
      });
    } else {
      // Update existing lesson progress
      enrollment.lessonsProgress[lessonProgressIndex].completed = true;
      enrollment.lessonsProgress[lessonProgressIndex].completedAt = new Date();
    }

    // Update last accessed time
    enrollment.lastAccessedAt = new Date();

    // Calculate total progress (we'll need to get the total lesson count from the course)
    // For now, we'll use the completed lessons count as a percentage of lessons tracked
    const totalLessonsTracked = enrollment.lessonsProgress.length;
    const completedLessons = enrollment.lessonsProgress.filter(
      (progress) => progress.completed,
    ).length;

    enrollment.progress =
      Math.round((completedLessons / totalLessonsTracked) * 100) || 0;

    // Update completion status if all tracked lessons are completed
    if (totalLessonsTracked > 0 && completedLessons === totalLessonsTracked) {
      enrollment.completed = true;
      enrollment.completedAt = new Date();
    }

    return enrollment.save();
  }

  async remove(userId: string, courseId: string) {
    const enrollment = await this.enrollmentModel
      .findOneAndDelete({
        userId: new Types.ObjectId(userId),
        courseId: new Types.ObjectId(courseId),
      })
      .exec();

    if (!enrollment) {
      throw new NotFoundException('Enrollment not found');
    }

    return enrollment;
  }

  async findAllWithAuth(
    auth: { userId: string; role: string },
    filterDto: EnrollmentFilterDto,
  ): Promise<GetAllEnrollmentsResponseDto> {
    const { page = 1, limit = 10, search, from, to } = filterDto;
    const skip = (page - 1) * limit;

    const query: any = {};

    // Add date range filtering
    if (from || to) {
      query.createdAt = {};
      if (from) {
        // Set time to start of day (00:00:00)
        const fromDate = new Date(from);
        fromDate.setHours(0, 0, 0, 0);
        query.createdAt.$gte = fromDate;
      }
      if (to) {
        // Set time to end of day (23:59:59.999)
        const toDate = new Date(to);
        toDate.setHours(23, 59, 59, 999);
        query.createdAt.$lte = toDate;
      }
    }

    // If instructor, only show enrollments for their courses
    if (auth.role === UserRole.INSTRUCTOR) {
      const instructorCourses = await this.courseModel
        .find({ instructor: auth.userId })
        .select('_id')
        .lean()
        .exec();

      query.courseId = { $in: instructorCourses.map((c) => c._id) };
    }

    if (search) {
      // We'll need to fetch course IDs that match the search first
      const courseIds = await this.courseModel
        .find({ name: { $regex: search, $options: 'i' } })
        .select('_id')
        .lean()
        .exec();

      if (query.courseId) {
        // If instructor, only search within their courses
        query.courseId = {
          $in: courseIds
            .map((c) => c._id)
            .filter((id) =>
              query.courseId.$in.some(
                (cid) => cid.toString() === id.toString(),
              ),
            ),
        };
      } else {
        query.courseId = { $in: courseIds.map((c) => c._id) };
      }
    }

    const [enrollments, total] = await Promise.all([
      this.enrollmentModel
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean()
        .exec(),
      this.enrollmentModel.countDocuments(query),
    ]);

    const totalPages = Math.ceil(total / limit);
    const hasMore = page < totalPages;

    // Get all unique course IDs from enrollments
    const courseIds = enrollments.map((enrollment) => enrollment.courseId);

    // Fetch course details
    const courses = await this.courseModel
      .find({ _id: { $in: courseIds } })
      .select('_id name publicId title thumbnail')
      .lean()
      .exec();

    // Create a map for quick lookup
    const courseMap = new Map<string, any>(
      courses.map((course: any): [string, any] => [
        course._id.toString(),
        {
          name: course.name as string,
          publicId: course.publicId as string,
          title: course.title as string,
          thumbnailUrl: course.thumbnail.url as string,
        },
      ]),
    );

    const minimalEnrollments: MinimalEnrollmentDto[] = enrollments.map(
      (enrollment: any) => {
        const courseId = enrollment.courseId.toString();
        const courseDetails = courseMap.get(courseId) || {
          name: 'Unknown Course',
          publicId: '',
          title: 'Unknown Course',
          thumbnailUrl: '',
        };

        return {
          _id: enrollment._id.toString(),
          user: enrollment.userId.toString(),
          courseId,
          coursePublicId: courseDetails.publicId,
          courseName: courseDetails.name,
          courseTitle: courseDetails.title,
          courseThumbnailUrl: courseDetails.thumbnailUrl,
          progress: enrollment.progress,
          lastAccessedAt: enrollment.lastAccessedAt,
          createdAt: enrollment.createdAt,
          updatedAt: enrollment.updatedAt,
        };
      },
    );

    return {
      enrollments: minimalEnrollments,
      total,
      page,
      totalPages,
      hasMore,
    };
  }
}
