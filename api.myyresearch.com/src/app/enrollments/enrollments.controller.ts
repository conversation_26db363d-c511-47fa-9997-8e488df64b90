import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { EnrollmentsService } from './enrollments.service';
import { CreateEnrollmentDto } from './dto/create-enrollment.dto';
import { UpdateLessonProgressDto } from './dto/update-lesson-progress.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../users/guards/roles.guard';
import { Roles } from '../users/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { EnrollmentFilterDto } from './dto/enrollment-filter.dto';
import { GetAllEnrollmentsResponseDto } from './dto/get-all-enrollments-response.dto';

@ApiTags('Enrollments')
@Controller('enrollments')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class EnrollmentsController {
  constructor(private readonly enrollmentsService: EnrollmentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new enrollment' })
  @ApiResponse({ status: 201, description: 'Enrollment created successfully' })
  @Roles(UserRole.USER)
  create(@Body() createEnrollmentDto: CreateEnrollmentDto, @Request() req) {
    return this.enrollmentsService.create(req.user.userId, createEnrollmentDto);
  }

  @Get('admin')
  @ApiOperation({ summary: 'Get all enrollments (Admin, Staff, Instructor)' })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered enrollments with pagination',
    type: GetAllEnrollmentsResponseDto,
  })
  @Roles(UserRole.ADMIN, UserRole.STAFF, UserRole.INSTRUCTOR)
  findAllAdmin(
    @Query() filterDto: EnrollmentFilterDto,
    @Request() req,
  ): Promise<GetAllEnrollmentsResponseDto> {
    return this.enrollmentsService.findAllWithAuth(
      {
        userId: req.user._id,
        role: req.user.role,
      },
      filterDto,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all enrollments for the current user' })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered enrollments with pagination',
    type: GetAllEnrollmentsResponseDto,
  })
  @Roles(UserRole.USER)
  findAll(
    @Query() filterDto: EnrollmentFilterDto,
    @Request() req,
  ): Promise<GetAllEnrollmentsResponseDto> {
    return this.enrollmentsService.findAll(req.user.userId, filterDto);
  }

  @Get(':courseId')
  @ApiOperation({ summary: 'Get enrollment by course ID' })
  @ApiResponse({ status: 200, description: 'Returns enrollment details' })
  @Roles(UserRole.USER)
  findOne(@Param('courseId') courseId: string, @Request() req) {
    return this.enrollmentsService.findOne(req.user.userId, courseId);
  }

  @Patch(':courseId/lesson-progress')
  @ApiOperation({ summary: 'Update lesson progress' })
  @ApiResponse({
    status: 200,
    description: 'Lesson progress updated successfully',
  })
  @Roles(UserRole.USER)
  updateLessonProgress(
    @Param('courseId') courseId: string,
    @Body() updateLessonProgressDto: UpdateLessonProgressDto,
    @Request() req,
  ) {
    return this.enrollmentsService.updateLessonProgress(
      req.user.userId,
      courseId,
      updateLessonProgressDto,
    );
  }

  @Delete(':courseId')
  @ApiOperation({ summary: 'Delete enrollment' })
  @ApiResponse({ status: 200, description: 'Enrollment deleted successfully' })
  @Roles(UserRole.USER)
  remove(@Param('courseId') courseId: string, @Request() req) {
    return this.enrollmentsService.remove(req.user.userId, courseId);
  }
}
