import { ApiProperty } from '@nestjs/swagger';

export class MinimalEnrollmentDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  user: string;

  @ApiProperty()
  courseId: string;

  @ApiProperty()
  coursePublicId: string;

  @ApiProperty()
  courseName: string;

  @ApiProperty()
  courseTitle: string;

  @ApiProperty()
  courseThumbnailUrl: string;

  @ApiProperty()
  progress: number;

  @ApiProperty()
  lastAccessedAt: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
