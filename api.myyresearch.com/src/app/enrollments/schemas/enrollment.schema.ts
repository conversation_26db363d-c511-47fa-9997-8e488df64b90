import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type EnrollmentDocument = Enrollment & Document;

@Schema({ _id: false })
class LessonProgress {
  @Prop({ type: Types.ObjectId, required: true })
  lessonId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, required: true })
  sectionId: Types.ObjectId;

  @Prop({ default: false })
  completed: boolean;

  @Prop({ type: Date })
  completedAt?: Date;
}

@Schema({ timestamps: true })
export class Enrollment {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Course', required: true })
  courseId: Types.ObjectId;

  @Prop({ default: 0 })
  progress: number;

  @Prop({ default: false })
  completed: boolean;

  @Prop({ type: Date })
  completedAt?: Date;

  @Prop({ type: Date })
  lastAccessedAt?: Date;

  @Prop({ type: [{ type: LessonProgress }], default: [] })
  lessonsProgress: LessonProgress[];
}

export const EnrollmentSchema = SchemaFactory.createForClass(Enrollment);
