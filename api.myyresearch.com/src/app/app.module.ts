import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthModule } from './auth/auth.module';
import { ThrottlerModule } from '@nestjs/throttler';
import { NewsletterModule } from './newsletter/newsletter.module';
import { UsersModule } from './users/users.module';
import { StudentsModule } from './students/students.module';
import { StatsModule } from './stats/stats.module';
import { CoursesModule } from './courses/courses.module';
import { TemplatesModule } from './templates/templates.module';
import { InstructorApplicationsModule } from './instructor-applications/instructor-applications.module';
import { CartModule } from './cart/cart.module';
import { OrdersModule } from './orders/orders.module';
import { PaymentsModule } from './payments/payments.module';
import { InstructorPaymentsModule } from './instructor-payments/instructor-payments.module';
import { EnrollmentsModule } from './enrollments/enrollments.module';
import { TemplateEnrollmentsModule } from './template-enrollments/template-enrollments.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRoot(process.env.MONGODB_URI),
    AuthModule,
    ThrottlerModule.forRoot([
      {
        ttl: 60,
        limit: 10,
      },
    ]),
    NewsletterModule,
    UsersModule,
    StudentsModule,
    CoursesModule,
    TemplatesModule,
    StatsModule,
    InstructorApplicationsModule,
    CartModule,
    OrdersModule,
    PaymentsModule,
    InstructorPaymentsModule,
    EnrollmentsModule,
    TemplateEnrollmentsModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
