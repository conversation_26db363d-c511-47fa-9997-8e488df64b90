import {
  Is<PERSON><PERSON><PERSON>,
  <PERSON>E<PERSON>,
  <PERSON><PERSON>ptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  TeachingExperience,
  ExperienceLevel,
  AudienceReach,
} from '../schemas/instructor-profile.schema';

export class InstructorProfileDto {
  @ApiProperty({
    description: 'Teaching experience types',
    enum: TeachingExperience,
    isArray: true,
    example: [TeachingExperience.ONLINE, TeachingExperience.IN_PERSON_INFORMAL],
  })
  @IsArray()
  @IsEnum(TeachingExperience, { each: true })
  teachingExperience: TeachingExperience[];

  @ApiPropertyOptional({
    description: 'Description of other teaching experience',
    example: 'I have taught at community workshops and local meetups',
  })
  @IsOptional()
  @IsString()
  @ValidateIf((o) => o.teachingExperience?.includes(TeachingExperience.OTHER))
  teachingExperienceOther?: string;

  @ApiProperty({
    description: 'Experience level',
    enum: ExperienceLevel,
    example: ExperienceLevel.SOME_KNOWLEDGE,
  })
  @IsEnum(ExperienceLevel)
  experienceLevel: ExperienceLevel;

  @ApiProperty({
    description: 'Audience reach',
    enum: AudienceReach,
    example: AudienceReach.SMALL_FOLLOWING,
  })
  @IsEnum(AudienceReach)
  audienceReach: AudienceReach;
}
