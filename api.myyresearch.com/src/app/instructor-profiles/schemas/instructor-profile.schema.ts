import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { User } from '../../users/schemas/user.schema';

export type InstructorProfileDocument = InstructorProfile & Document;

export enum TeachingExperience {
  IN_PERSON_INFORMAL = 'in-person-informal',
  IN_PERSON_PROFESSIONAL = 'in-person-professional',
  ONLINE = 'online',
  OTHER = 'other',
}

export enum ExperienceLevel {
  BEGINNER = 'beginner',
  SOME_KNOWLEDGE = 'some-knowledge',
  EXPERIENCED = 'experienced',
}

export enum AudienceReach {
  NOT_AT_MOMENT = 'not-at-moment',
  SMALL_FOLLOWING = 'small-following',
  SIZEABLE_FOLLOWING = 'sizeable-following',
}

export enum DocumentStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

@Schema({ timestamps: true })
export class InstructorProfile {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
  })
  user: User;

  @Prop({
    type: [String],
    enum: Object.values(TeachingExperience),
    required: true,
  })
  teachingExperience: TeachingExperience[];

  @Prop({ type: String })
  teachingExperienceOther?: string;

  @Prop({
    type: String,
    enum: Object.values(ExperienceLevel),
    required: true,
  })
  experienceLevel: ExperienceLevel;

  @Prop({
    type: String,
    enum: Object.values(AudienceReach),
    required: true,
  })
  audienceReach: AudienceReach;

  @Prop({
    type: String,
    enum: Object.values(DocumentStatus),
    default: DocumentStatus.ACTIVE,
  })
  documentStatus: DocumentStatus;

  createdAt: Date;
  updatedAt: Date;
}

export const InstructorProfileSchema = SchemaFactory.createForClass(InstructorProfile);

// Add index for user reference
InstructorProfileSchema.index({ user: 1 }, { unique: true });
