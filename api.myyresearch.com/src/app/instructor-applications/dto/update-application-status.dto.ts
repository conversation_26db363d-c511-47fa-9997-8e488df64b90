import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { ApplicationStatus } from '../enums/application-status.enum';

export class UpdateApplicationStatusDto {
  @ApiProperty({
    enum: ApplicationStatus,
    example: ApplicationStatus.APPROVED,
    description: 'The new status of the application',
  })
  @IsEnum(ApplicationStatus)
  status: ApplicationStatus;

  @ApiProperty({
    example: 'Application approved. Candidate has excellent qualifications.',
    description: 'Notes about the status change decision',
  })
  @IsString()
  @IsNotEmpty()
  reviewNotes: string;
}
