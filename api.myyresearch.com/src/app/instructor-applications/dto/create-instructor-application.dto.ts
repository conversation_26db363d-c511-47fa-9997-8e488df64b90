import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, Is<PERSON><PERSON>, IsNotEmpty, IsString } from 'class-validator';

export class CreateInstructorApplicationDto {
  @ApiProperty({ example: 'John' })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({ example: 'Doe' })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ example: '+1234567890' })
  @IsNotEmpty()
  @IsString()
  phone: string;

  @ApiProperty({
    example: 'I have 5 years of experience teaching mathematics...',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({ example: ["Bachelor's Degree", "Master's Degree"] })
  @IsArray()
  @IsString({ each: true })
  educationalLevels: string[];

  @ApiProperty({ example: ['Mathematics Teacher', 'Online Tutor'] })
  @IsArray()
  @IsString({ each: true })
  occupations: string[];
}
