import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  InstructorApplication,
  InstructorApplicationDocument,
} from './schemas/instructor-application.schema';
import { CreateInstructorApplicationDto } from './dto/create-instructor-application.dto';
import { UpdateApplicationStatusDto } from './dto/update-application-status.dto';
import { UserRole } from '../users/schemas/user.schema';
import { ApplicationStatus } from './enums/application-status.enum';
import { DocumentStatus } from './enums/document-status.enum';

@Injectable()
export class InstructorApplicationsService {
  constructor(
    @InjectModel(InstructorApplication.name)
    private instructorApplicationModel: Model<InstructorApplicationDocument>,
  ) {}

  async create(createDto: CreateInstructorApplicationDto) {
    const application = new this.instructorApplicationModel({
      ...createDto,
      status: ApplicationStatus.PENDING,
      documentStatus: DocumentStatus.ACTIVE,
    });
    return application.save();
  }

  async findAll(userRole: UserRole) {
    if (![UserRole.ADMIN, UserRole.STAFF].includes(userRole)) {
      throw new NotFoundException('Not authorized to view applications');
    }
    return this.instructorApplicationModel
      .find({ documentStatus: DocumentStatus.ACTIVE })
      .sort({ createdAt: -1 });
  }

  async findOne(id: string, userRole: UserRole) {
    if (![UserRole.ADMIN, UserRole.STAFF].includes(userRole)) {
      throw new NotFoundException('Not authorized to view application');
    }
    const application = await this.instructorApplicationModel.findOne({
      _id: id,
      documentStatus: DocumentStatus.ACTIVE,
    });
    if (!application) {
      throw new NotFoundException('Application not found');
    }
    return application;
  }

  async updateStatus(
    id: string,
    updateStatusDto: UpdateApplicationStatusDto,
    reviewerId: string,
    userRole: UserRole,
  ) {
    if (![UserRole.ADMIN, UserRole.STAFF].includes(userRole)) {
      throw new NotFoundException(
        'Not authorized to update application status',
      );
    }

    const application = await this.instructorApplicationModel.findOne({
      _id: id,
      documentStatus: DocumentStatus.ACTIVE,
    });
    if (!application) {
      throw new NotFoundException('Application not found');
    }

    const now = new Date();
    const statusUpdate = {
      status: updateStatusDto.status,
      reviewNotes: updateStatusDto.reviewNotes,
      reviewedBy: reviewerId,
      reviewedAt: now,
      statusHistory: [
        ...(application.statusHistory || []),
        {
          status: updateStatusDto.status,
          changedBy: reviewerId,
          changedAt: now,
          notes: updateStatusDto.reviewNotes,
        },
      ],
    };

    return this.instructorApplicationModel.findByIdAndUpdate(id, statusUpdate, {
      new: true,
    });
  }

  async findByStatus(status: ApplicationStatus, userRole: UserRole) {
    if (![UserRole.ADMIN, UserRole.STAFF].includes(userRole)) {
      throw new NotFoundException('Not authorized to view applications');
    }
    return this.instructorApplicationModel
      .find({ status, documentStatus: DocumentStatus.ACTIVE })
      .sort({ createdAt: -1 });
  }

  async softDelete(id: string, userId: string, userRole: UserRole) {
    if (![UserRole.ADMIN, UserRole.STAFF].includes(userRole)) {
      throw new NotFoundException('Not authorized to delete applications');
    }

    const application = await this.instructorApplicationModel.findOne({
      _id: id,
      documentStatus: DocumentStatus.ACTIVE,
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    return this.instructorApplicationModel.findByIdAndUpdate(
      id,
      {
        documentStatus: DocumentStatus.ARCHIVED,
        deletedAt: new Date(),
        deletedBy: userId,
      },
      { new: true },
    );
  }

  async restore(id: string, userRole: UserRole) {
    if (![UserRole.ADMIN].includes(userRole)) {
      throw new NotFoundException('Not authorized to restore applications');
    }

    const application = await this.instructorApplicationModel.findOne({
      _id: id,
      documentStatus: DocumentStatus.ARCHIVED,
    });

    if (!application) {
      throw new NotFoundException('Application not found or not deleted');
    }

    return this.instructorApplicationModel.findByIdAndUpdate(
      id,
      {
        documentStatus: DocumentStatus.ACTIVE,
        deletedAt: null,
        deletedBy: null,
      },
      { new: true },
    );
  }
}
