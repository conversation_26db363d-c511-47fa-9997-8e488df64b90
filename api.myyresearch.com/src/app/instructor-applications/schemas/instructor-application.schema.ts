import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApplicationStatus } from '../enums/application-status.enum';
import { DocumentStatus } from '../enums/document-status.enum';

export type InstructorApplicationDocument = InstructorApplication & Document;

@Schema({ timestamps: true })
export class InstructorApplication {
  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  phone: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  educationalLevels: string[];

  @Prop({ required: true })
  occupations: string[];

  @Prop({
    default: ApplicationStatus.PENDING,
    enum: Object.values(ApplicationStatus),
    type: String,
  })
  status: ApplicationStatus;

  @Prop({
    type: String,
    enum: Object.values(DocumentStatus),
    default: DocumentStatus.ACTIVE,
  })
  documentStatus: DocumentStatus;

  @Prop()
  reviewedBy?: string;

  @Prop()
  reviewedAt?: Date;

  @Prop()
  reviewNotes?: string;

  @Prop()
  statusHistory?: Array<{
    status: ApplicationStatus;
    changedBy: string;
    changedAt: Date;
    notes: string;
  }>;
}

export const InstructorApplicationSchema = SchemaFactory.createForClass(
  InstructorApplication,
);
