import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { InstructorApplicationsService } from './instructor-applications.service';
import { InstructorApplicationsController } from './instructor-applications.controller';
import {
  InstructorApplication,
  InstructorApplicationSchema,
} from './schemas/instructor-application.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: InstructorApplication.name,
        schema: InstructorApplicationSchema,
      },
    ]),
  ],
  controllers: [InstructorApplicationsController],
  providers: [InstructorApplicationsService],
  exports: [InstructorApplicationsService],
})
export class InstructorApplicationsModule {}
