import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Request,
  Patch,
  Query,
  Delete,
} from '@nestjs/common';
import { InstructorApplicationsService } from './instructor-applications.service';
import { CreateInstructorApplicationDto } from './dto/create-instructor-application.dto';
import { UpdateApplicationStatusDto } from './dto/update-application-status.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/schemas/user.schema';
import { ApplicationStatus } from './enums/application-status.enum';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';

@ApiTags('instructor-applications')
@Controller('instructor-applications')
export class InstructorApplicationsController {
  constructor(
    private readonly instructorApplicationsService: InstructorApplicationsService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Submit a new instructor application (Public)' })
  @ApiResponse({
    status: 201,
    description: 'Application submitted successfully',
  })
  create(@Body() createDto: CreateInstructorApplicationDto) {
    return this.instructorApplicationsService.create(createDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all applications (Admin, Staff)' })
  @ApiQuery({
    name: 'status',
    enum: ApplicationStatus,
    required: false,
    description: 'Filter applications by status',
  })
  findAll(@Request() req, @Query('status') status?: ApplicationStatus) {
    if (status) {
      return this.instructorApplicationsService.findByStatus(
        status,
        req.user.role,
      );
    }
    return this.instructorApplicationsService.findAll(req.user.role);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get application by ID (Admin, Staff)' })
  findOne(@Param('id') id: string, @Request() req) {
    return this.instructorApplicationsService.findOne(id, req.user.role);
  }

  @Patch(':id/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update application status (Admin, Staff)' })
  @ApiResponse({
    status: 200,
    description: 'Application status updated successfully',
  })
  updateStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateApplicationStatusDto,
    @Request() req,
  ) {
    return this.instructorApplicationsService.updateStatus(
      id,
      updateStatusDto,
      req.user._id,
      req.user.role,
    );
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Soft delete application (Admin, Staff)' })
  @ApiResponse({
    status: 200,
    description: 'Application soft deleted successfully',
  })
  softDelete(@Param('id') id: string, @Request() req) {
    return this.instructorApplicationsService.softDelete(
      id,
      req.user._id,
      req.user.role,
    );
  }

  @Patch(':id/restore')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Restore soft deleted application (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Application restored successfully',
  })
  restore(@Param('id') id: string, @Request() req) {
    return this.instructorApplicationsService.restore(id, req.user.role);
  }
}
