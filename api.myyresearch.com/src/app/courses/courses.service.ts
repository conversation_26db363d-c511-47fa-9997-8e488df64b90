import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  Course,
  CourseDocument,
  DocumentStatus,
  CourseStatus,
  CreatorType,
  Section,
  Lesson,
  CourseLevel,
} from './schemas/course.schema';
import {
  CreateCourseDto,
  CreateSectionDto,
  CreateLessonDto,
} from './dto/create-course.dto';
import { UpdateCourseDto } from './dto/update-course.dto';
import { MinimalCourseDto, PublicCourseDto } from './dto/public-course.dto';
import { Review, ReviewStatus } from '../reviews/schemas/review.schema';
import { UserRole, User } from '../users/schemas/user.schema';
import { EnrolledCourseDto } from './dto/enrolled-course.dto';
import { EnrollmentDocument } from '../enrollments/schemas/enrollment.schema';
import {
  ApproveCourseDto,
  CourseApprovalStatus,
} from './dto/approve-course.dto';
import { CourseFilterDto } from './dto/course-filter.dto';
import { GetAllCoursesResponseDto } from './dto/get-all-courses-response.dto';
import { FilterOperator, FilterType, JoinOperator } from './dto/filter.dto';
import { CategoryFilterDto } from './dto/category-filter.dto';
import { CategoryCoursesResponseDto } from './dto/category-courses-response.dto';
import { CourseFiltersDto } from './dto/course-filters.dto';
import { FilteredCoursesResponseDto } from './dto/filtered-courses-response.dto';
import { PublicCourseResponseDto } from './dto/public-course-response.dto';
import { MinimalisticCourseDto } from './dto/minimalistic-course.dto';
import { PublicMinimalCourseDto } from './dto/public-minimal-course.dto';
import { OrdersService } from '../orders/orders.service';
import { OrderItemType } from '../orders/schemas/order.schema';

interface CreateCourseParams {
  createCourseDto: CreateCourseDto;
  userId: string;
  userRole: UserRole;
}

interface UserContext {
  userId: string;
  role: UserRole;
}

@Injectable()
export class CoursesService {
  constructor(
    @InjectModel(Course.name) private courseModel: Model<CourseDocument>,
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Review.name) private reviewModel: Model<Review>,

    @InjectModel('Enrollment')
    private enrollmentModel: Model<EnrollmentDocument>,

    private ordersService: OrdersService,
  ) {}

  private readonly minimalCourseProjection = {
    _id: 1,
    publicId: 1,
    title: 1,
    shortDescription: 1,
    thumbnail: 1,
    category: 1,
    subcategory: 1,
    level: 1,
    price: 1,
    discountPrice: 1,
    status: 1,
    creatorType: 1,
    approvalStatus: 1,
    createdAt: 1,
    updatedAt: 1,
  };

  private convertDtoToCourse(params: CreateCourseParams): Partial<Course> {
    const sections: Section[] = (params.createCourseDto.sections || []).map(
      (sectionDto: CreateSectionDto) => {
        const sectionId = new Types.ObjectId().toString();

        return {
          _id: sectionId,
          title: sectionDto.title,
          type: sectionDto.type,
          order: sectionDto.order || 0,
          lessons: (sectionDto.lessons || []).map(
            (lessonDto: CreateLessonDto) => ({
              _id: new Types.ObjectId().toString(),
              title: lessonDto.title,
              type: lessonDto.type,
              content: lessonDto.content || '',
              duration: lessonDto.duration || 0,
              video: lessonDto.video || null,
              media: lessonDto.media || null,
              order: lessonDto.order || 0,
              isFree: lessonDto.isFree || false,
            }),
          ),
        };
      },
    );

    const isAdminOrStaff =
      params.userRole === UserRole.ADMIN || params.userRole === UserRole.STAFF;

    return {
      ...params.createCourseDto,
      sections,
      createdBy: params.userId,
      status: params.createCourseDto.status || CourseStatus.DRAFT,
      documentStatus: DocumentStatus.ACTIVE,
      creatorType: isAdminOrStaff
        ? CreatorType.MYRESEARCH
        : CreatorType.INSTRUCTOR,
      approvalStatus: isAdminOrStaff
        ? CourseApprovalStatus.APPROVED
        : CourseApprovalStatus.PENDING,
      ...(isAdminOrStaff && {
        approvedBy: params.userId,
        approvalDate: new Date(),
      }),
    };
  }

  private handleSectionsAndLessonsUpdate(
    updateCourseDto: UpdateCourseDto,
  ): Partial<Course> {
    if (!updateCourseDto.sections) {
      return updateCourseDto as Partial<Course>;
    }

    const sections = updateCourseDto.sections.map((section) => {
      const processedSection: Section = {
        _id: section._id || new Types.ObjectId().toString(),
        title: section.title,
        type: section.type,
        order: section.order || 0,
        lessons: (section.lessons || []).map((lesson) => ({
          _id: lesson._id || new Types.ObjectId().toString(),
          title: lesson.title,
          type: lesson.type,
          content: lesson.content || '',
          duration: lesson.duration || 0,
          video: lesson.video || null,
          media: lesson.media || null,
          order: lesson.order || 0,
          isFree: lesson.isFree || false,
        })),
      };
      return processedSection;
    });

    return {
      ...updateCourseDto,
      sections,
    } as Partial<Course>;
  }

  async create(params: CreateCourseParams): Promise<Course> {
    const courseData = this.convertDtoToCourse(params);
    const createdCourse = new this.courseModel(courseData);
    return createdCourse.save();
  }

  async findAll(): Promise<MinimalCourseDto[]> {
    const courses = await this.courseModel
      .find({ documentStatus: DocumentStatus.ACTIVE })
      .select(this.minimalCourseProjection)
      .lean()
      .exec();

    return Promise.all(
      courses.map((course) => this.toMinimalCourseDto(course)),
    );
  }

  async findOne(id: string): Promise<Course> {
    return this.courseModel
      .findOne({
        _id: id,
        documentStatus: DocumentStatus.ACTIVE,
      })
      .exec();
  }

  async update(id: string, updateCourseDto: Partial<Course>): Promise<Course> {
    // First get the existing course
    const existingCourse = await this.courseModel
      .findOne({
        _id: id,
        documentStatus: DocumentStatus.ACTIVE,
      })
      .exec();

    if (!existingCourse) {
      throw new NotFoundException(`Course with ID ${id} not found`);
    }

    // Check if we're changing from published to draft
    if (
      existingCourse.status === CourseStatus.PUBLISHED &&
      updateCourseDto.status === CourseStatus.DRAFT
    ) {
      // Create an archived copy of the published version
      const archivedCourse = new this.courseModel({
        ...existingCourse.toObject(),
        _id: undefined, // Let MongoDB generate a new ID
        originalId: existingCourse._id,
        publicId: null,
      });
      await archivedCourse.save();
    }

    // Proceed with the update
    const updatedCourse = await this.courseModel
      .findOneAndUpdate(
        { _id: id, documentStatus: DocumentStatus.ACTIVE },
        updateCourseDto,
        { new: true },
      )
      .exec();

    if (!updatedCourse) {
      throw new NotFoundException(`Course with ID ${id} not found`);
    }

    return updatedCourse;
  }

  async remove(id: string): Promise<Course> {
    return this.courseModel
      .findByIdAndUpdate(
        id,
        { documentStatus: DocumentStatus.DELETED },
        { new: true },
      )
      .exec();
  }

  async findPublicCoursesByCategory(
    category?: string,
    subcategory?: string,
    minimal = true,
  ): Promise<MinimalCourseDto[] | PublicCourseDto[]> {
    const query = {
      documentStatus: DocumentStatus.ACTIVE,
      status: CourseStatus.PUBLISHED,
      $or: [
        // Always show items with creatorType 'myyresearch'
        { creatorType: CreatorType.MYRESEARCH },
        // For other creatorTypes, only show if approved
        {
          creatorType: { $ne: CreatorType.MYRESEARCH },
          approvalStatus: CourseApprovalStatus.APPROVED,
        },
      ],
      ...(category && { category }),
      ...(subcategory && { subcategory }),
    };

    if (minimal) {
      const courses = await this.courseModel
        .find(query)
        .select(this.minimalCourseProjection)
        .lean()
        .exec();

      return Promise.all(
        courses.map((course) => this.toMinimalCourseDto(course)),
      );
    }

    const courses = await this.courseModel
      .find(query)
      .select({ ...this.minimalCourseProjection, description: 1, sections: 1 })
      .lean()
      .exec();

    return Promise.all(
      courses.map(async (course) => {
        const minimalDto = await this.toMinimalCourseDto(course);
        return {
          ...minimalDto,
          description: course.description,
          sections: course.sections.map((section) => ({
            title: section.title,
            lessons: section.lessons.map((lesson) => ({
              title: lesson.title,
              type: lesson.type,
              duration: lesson.duration,
            })),
          })),
        };
      }),
    );
  }

  async findPublicCourseById(
    id: string,
    minimal = false,
  ): Promise<MinimalCourseDto | PublicCourseDto> {
    const projection = minimal
      ? this.minimalCourseProjection
      : { ...this.minimalCourseProjection, description: 1, sections: 1 };

    const course = await this.courseModel
      .findOne({
        _id: id,
        documentStatus: DocumentStatus.ACTIVE,
        status: CourseStatus.PUBLISHED,
        $or: [
          // Always show items with creatorType 'myyresearch'
          { creatorType: CreatorType.MYRESEARCH },
          // For other creatorTypes, only show if approved
          {
            creatorType: { $ne: CreatorType.MYRESEARCH },
            approvalStatus: CourseApprovalStatus.APPROVED,
          },
        ],
      })
      .select(projection)
      .lean()
      .exec();

    if (!course) {
      return null;
    }

    const minimalDto = await this.toMinimalCourseDto(course);

    if (minimal) {
      return minimalDto;
    }

    return {
      ...minimalDto,
      description: course.description,
      sections: course.sections.map((section) => ({
        title: section.title,
        lessons: section.lessons.map((lesson) => ({
          title: lesson.title,
          type: lesson.type,
          duration: lesson.duration,
        })),
      })),
    };
  }

  async findByInstructor(instructorId: string): Promise<MinimalCourseDto[]> {
    const courses = await this.courseModel
      .find({
        documentStatus: DocumentStatus.ACTIVE,
        createdBy: instructorId,
      })
      .select(this.minimalCourseProjection)
      .lean()
      .exec();

    return Promise.all(
      courses.map((course) => this.toMinimalCourseDto(course)),
    );
  }

  private async verifyPermission(
    courseId: string,
    userId: string,
  ): Promise<CourseDocument> {
    const course = await this.courseModel.findById(courseId);
    if (!course) {
      throw new NotFoundException('Course not found');
    }

    // Check if user is admin
    const user = await this.courseModel.findOne({
      _id: courseId,
      createdBy: userId,
      creatorType: CreatorType.MYRESEARCH,
    });

    // If user is not admin, verify they own the course
    if (!user && course.createdBy.toString() !== userId) {
      throw new ForbiddenException(
        'You do not have permission to modify this course',
      );
    }

    return course;
  }

  async addSection(
    courseId: string,
    createSectionDto: CreateSectionDto,
    userId: string,
  ): Promise<Course> {
    const course = await this.verifyPermission(courseId, userId);

    const order = createSectionDto.order ?? (course.sections?.length || 0) + 1;
    const newSection: Section = {
      _id: new Types.ObjectId('000000000000').toString(),
      title: createSectionDto.title,
      type: createSectionDto.type,
      order,
      lessons: [],
    };

    if (!course.sections) {
      course.sections = [];
    }
    course.sections.push(newSection);
    course.updatedBy = userId;
    return course.save();
  }

  async updateSection(
    courseId: string,
    sectionId: string,
    updateSectionDto: Partial<CreateSectionDto>,
    userId: string,
  ): Promise<Course> {
    const course = await this.verifyPermission(courseId, userId);

    const sectionIndex = course.sections?.findIndex(
      (section) => section._id.toString() === sectionId,
    );

    if (sectionIndex === -1) {
      throw new NotFoundException('Section not found');
    }

    const existingSection = course.sections[sectionIndex];
    course.sections[sectionIndex] = {
      ...existingSection,
      title: updateSectionDto.title || existingSection.title,
      order: updateSectionDto.order || existingSection.order,
    };

    course.updatedBy = userId;
    return course.save();
  }

  async removeSection(
    courseId: string,
    sectionId: string,
    userId: string,
  ): Promise<Course> {
    const course = await this.verifyPermission(courseId, userId);

    course.sections = course.sections?.filter(
      (section) => section._id.toString() !== sectionId,
    );
    course.updatedBy = userId;
    return course.save();
  }

  async addLesson(
    courseId: string,
    sectionId: string,
    createLessonDto: CreateLessonDto,
    userId: string,
  ): Promise<Course> {
    const course = await this.verifyPermission(courseId, userId);

    const section = course.sections.find((s) => s._id === sectionId);
    if (!section) {
      throw new NotFoundException('Section not found');
    }

    const order = createLessonDto.order ?? (section.lessons?.length || 0) + 1;
    const newLesson: Lesson = {
      _id: new Types.ObjectId('000000000000').toString(),
      title: createLessonDto.title,
      type: createLessonDto.type,
      content: createLessonDto.content || '',
      duration: createLessonDto.duration || 0,
      video: createLessonDto.video || null,
      order,
      isFree: createLessonDto.isFree || false,
    };

    if (!section.lessons) {
      section.lessons = [];
    }
    section.lessons.push(newLesson);
    course.updatedBy = userId;
    return course.save();
  }

  async updateLesson(
    courseId: string,
    sectionId: string,
    lessonId: string,
    updateLessonDto: Partial<CreateLessonDto>,
    userId: string,
  ): Promise<Course> {
    const course = await this.verifyPermission(courseId, userId);

    const section = course.sections?.find((s) => s._id === sectionId);
    if (!section) {
      throw new NotFoundException('Section not found');
    }

    const existingLesson = section.lessons?.find((l) => l._id === lessonId);
    if (!existingLesson) {
      throw new NotFoundException('Lesson not found');
    }

    Object.assign(existingLesson, {
      title: updateLessonDto.title || existingLesson.title,
      type: updateLessonDto.type || existingLesson.type,
      content: updateLessonDto.content || existingLesson.content,
      duration: updateLessonDto.duration || existingLesson.duration,
      video: updateLessonDto.video || existingLesson.video,
      order: updateLessonDto.order || existingLesson.order,
    });

    course.updatedBy = userId;
    return course.save();
  }

  async removeLesson(
    courseId: string,
    sectionId: string,
    lessonId: string,
    userId: string,
  ): Promise<Course> {
    const course = await this.verifyPermission(courseId, userId);

    const sectionIndex = course.sections?.findIndex(
      (section) => section._id.toString() === sectionId,
    );

    if (sectionIndex === -1) {
      throw new NotFoundException('Section not found');
    }

    course.sections[sectionIndex].lessons = course.sections[
      sectionIndex
    ].lessons?.filter((lesson) => lesson._id.toString() !== lessonId);
    course.updatedBy = userId;
    return course.save();
  }

  async findEnrolledCourses(userId: string): Promise<EnrolledCourseDto[]> {
    const enrollments = await this.enrollmentModel
      .find({ userId })
      .populate({
        path: 'courseId',
        select: this.minimalCourseProjection,
      })
      .exec();

    const enrolledCourses = await Promise.all(
      enrollments.map(async (enrollment) => {
        const course = enrollment.courseId as unknown as Course;
        const minimalDto = await this.toMinimalCourseDto(course);
        return {
          ...minimalDto,
          progress: enrollment.progress,
          completed: enrollment.completed,
          completedAt: enrollment.completedAt,
          lastAccessedAt: enrollment.lastAccessedAt,
        };
      }),
    );

    return enrolledCourses;
  }

  async findEnrolledCourseByPublicId(
    publicId: string,
    userId: string,
  ): Promise<EnrolledCourseDto> {
    const course = await this.courseModel
      .findOne({ publicId })
      .select(this.minimalCourseProjection)
      .exec();
    if (!course) {
      throw new Error('Course not found');
    }

    const enrollment = await this.enrollmentModel
      .findOne({ userId, courseId: course._id })
      .exec();
    if (!enrollment) {
      throw new Error('User is not enrolled in this course');
    }

    const minimalDto = await this.toMinimalCourseDto(course);
    return {
      ...minimalDto,
      progress: enrollment.progress,
      completed: enrollment.completed,
      completedAt: enrollment.completedAt,
      lastAccessedAt: enrollment.lastAccessedAt,
    };
  }

  private async getEnrollmentCount(courseId: string): Promise<number> {
    return this.enrollmentModel.countDocuments({ courseId }).exec();
  }

  private async toMinimalCourseDto(
    course: any,
    includeInstructor = false,
  ): Promise<MinimalCourseDto> {
    const enrollmentCount = await this.getEnrollmentCount(course._id);
    const baseDto = {
      id: course._id.toString(),
      publicId: course.publicId,
      title: course.title,
      shortDescription: course.shortDescription,
      thumbnail: course.thumbnail?.key || '',
      category: course.category,
      subcategory: course.subcategory,
      level: course.level || CourseLevel.BEGINNER,
      price: course.price,
      discountPrice: course.discountPrice,
      status: course.status,
      approvalStatus: course.approvalStatus,
      enrollmentCount,
      creatorType: course.creatorType,
      createdAt: course.createdAt,
      updatedAt: course.updatedAt,
    };

    if (includeInstructor) {
      const extendedDto: any = { ...baseDto };

      if (course.creatorType) {
        extendedDto.creatorType = course.creatorType;
      }

      if (course.createdBy) {
        const instructor = await this.userModel
          .findById(course.createdBy)
          .select('username')
          .lean();
        if (instructor) {
          extendedDto.instructorUsername = instructor.username;
        }
      }

      return extendedDto;
    }

    return baseDto;
  }

  private async checkCourseOwnership(
    courseId: string,
    userContext: UserContext,
  ): Promise<Course> {
    const course = await this.findOne(courseId);
    if (!course) {
      throw new NotFoundException('Course not found');
    }

    if (
      userContext.role === UserRole.ADMIN ||
      userContext.role === UserRole.STAFF
    ) {
      return course;
    }

    if (
      userContext.role === UserRole.INSTRUCTOR &&
      course.createdBy.toString() !== userContext.userId.toString()
    ) {
      throw new ForbiddenException('You can only access your own courses');
    }

    return course;
  }

  async findAllWithAuth(
    userContext: UserContext,
    filterDto: CourseFilterDto,
  ): Promise<GetAllCoursesResponseDto> {
    const {
      search,
      status,
      creatorType,
      category,
      subcategory,
      page = 1,
      limit = 10,
      sort,
      filters,
      joinOperator = JoinOperator.AND,
    } = filterDto;

    const query: any = {
      documentStatus: DocumentStatus.ACTIVE,
      publicId: { $ne: null },
    };

    // If user is instructor, only return their courses
    if (userContext.role === UserRole.INSTRUCTOR) {
      query['createdBy'] = userContext.userId;
    }

    // Handle basic filters
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { shortDescription: { $regex: search, $options: 'i' } },
      ];
    }

    if (status) query.status = status;
    if (creatorType) query.creatorType = creatorType;
    if (category) query.category = category;
    if (subcategory) query.subcategory = subcategory;

    // Handle advanced filters
    if (filters && filters.length > 0) {
      const filterConditions = filters.map((filter) => {
        const condition: any = {};

        switch (filter.operator) {
          case FilterOperator.ILIKE:
            condition[filter.id] = { $regex: filter.value, $options: 'i' };
            break;
          case FilterOperator.NOT_ILIKE:
            condition[filter.id] = {
              $not: { $regex: filter.value, $options: 'i' },
            };
            break;
          case FilterOperator.EQ:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $in: filter.value };
            } else {
              condition[filter.id] = filter.value;
            }
            break;
          case FilterOperator.NE:
            if (
              filter.type === FilterType.MULTI_SELECT &&
              Array.isArray(filter.value)
            ) {
              condition[filter.id] = { $nin: filter.value };
            } else {
              condition[filter.id] = { $ne: filter.value };
            }
            break;
          case FilterOperator.IS_EMPTY:
            condition[filter.id] = { $exists: false };
            break;
          case FilterOperator.IS_NOT_EMPTY:
            condition[filter.id] = { $exists: true };
            break;
        }
        return condition;
      });

      if (filterConditions.length > 0) {
        const operator = joinOperator === JoinOperator.AND ? '$and' : '$or';
        query[operator] = filterConditions;
      }
    }

    const skip = (page - 1) * limit;

    // Create sort object for mongoose
    let sortCriteria = {};
    if (sort && Array.isArray(sort)) {
      sort.forEach((sortItem) => {
        sortCriteria[sortItem.id] = sortItem.desc ? -1 : 1;
      });
    } else {
      // Default sort by createdAt desc if no sort provided
      sortCriteria = { createdAt: -1 };
    }

    const [courses, total] = await Promise.all([
      this.courseModel
        .find(query)
        .select({
          ...this.minimalCourseProjection,
          createdBy: 1,
          creatorType: 1,
          approvalStatus: 1,
          createdAt: 1,
          updatedAt: 1,
        })
        .sort(sortCriteria)
        .skip(skip)
        .limit(limit)
        .lean(),
      this.courseModel.countDocuments(query),
    ]);

    const includeInstructor =
      userContext.role === UserRole.ADMIN ||
      userContext.role === UserRole.STAFF;

    const mappedCourses = await Promise.all(
      courses.map((course) =>
        this.toMinimalCourseDto(course, includeInstructor),
      ),
    );

    const totalPages = Math.ceil(total / limit);

    return {
      courses: mappedCourses,
      total,
      page,
      totalPages,
    };
  }

  async findOneWithAuth(id: string, userContext: UserContext): Promise<Course> {
    const course = await this.checkCourseOwnership(id, userContext);
    return course;
  }

  async createWithAuth(params: CreateCourseParams): Promise<Course> {
    return this.create(params);
  }

  async updateWithAuth(
    courseId: string,
    updateCourseDto: UpdateCourseDto,
    userContext: UserContext,
  ): Promise<Course> {
    await this.checkCourseOwnership(courseId, userContext);

    const processedDto = this.handleSectionsAndLessonsUpdate(updateCourseDto);

    return this.update(courseId, {
      ...processedDto,
      updatedBy: userContext.userId,
    });
  }

  async removeWithAuth(id: string, userContext: UserContext): Promise<Course> {
    await this.checkCourseOwnership(id, userContext);

    return this.remove(id);
  }

  async addSectionWithAuth(
    courseId: string,
    createSectionDto: CreateSectionDto,
    userContext: UserContext,
  ): Promise<Course> {
    await this.checkCourseOwnership(courseId, userContext);

    return this.addSection(courseId, createSectionDto, userContext.userId);
  }

  async updateSectionWithAuth(
    courseId: string,
    sectionId: string,
    updateSectionDto: Partial<CreateSectionDto>,
    userContext: UserContext,
  ): Promise<Course> {
    await this.checkCourseOwnership(courseId, userContext);

    return this.updateSection(
      courseId,
      sectionId,
      updateSectionDto,
      userContext.userId,
    );
  }

  async removeSectionWithAuth(
    courseId: string,
    sectionId: string,
    userContext: UserContext,
  ): Promise<Course> {
    await this.checkCourseOwnership(courseId, userContext);

    return this.removeSection(courseId, sectionId, userContext.userId);
  }

  async addLessonWithAuth(
    courseId: string,
    sectionId: string,
    createLessonDto: CreateLessonDto,
    userContext: UserContext,
  ): Promise<Course> {
    await this.checkCourseOwnership(courseId, userContext);

    return this.addLesson(
      courseId,
      sectionId,
      createLessonDto,
      userContext.userId,
    );
  }

  async updateLessonWithAuth(
    courseId: string,
    sectionId: string,
    lessonId: string,
    updateLessonDto: Partial<CreateLessonDto>,
    userContext: UserContext,
  ): Promise<Course> {
    await this.checkCourseOwnership(courseId, userContext);

    return this.updateLesson(
      courseId,
      sectionId,
      lessonId,
      updateLessonDto,
      userContext.userId,
    );
  }

  async removeLessonWithAuth(
    courseId: string,
    sectionId: string,
    lessonId: string,
    userContext: UserContext,
  ): Promise<Course> {
    await this.checkCourseOwnership(courseId, userContext);

    return this.removeLesson(courseId, sectionId, lessonId, userContext.userId);
  }

  async approveCourse(
    id: string,
    approveCourseDto: ApproveCourseDto,
    userId: string,
  ): Promise<Course> {
    const course = await this.courseModel.findOne({
      _id: id,
      documentStatus: DocumentStatus.ACTIVE,
    });

    if (!course) {
      throw new NotFoundException('Course not found');
    }

    // Allow approval regardless of current status to match template behavior
    // if (course.status !== CourseStatus.PENDING) {
    //   throw new BadRequestException('Course is not in pending status');
    // }

    const updatedCourse = await this.courseModel.findByIdAndUpdate(
      id,
      {
        $set: {
          approvalStatus: approveCourseDto.status,
          approvalComment: approveCourseDto.comment,
          approvedBy: userId,
          approvalDate: new Date(),
          status:
            approveCourseDto.status === CourseApprovalStatus.APPROVED
              ? CourseStatus.PUBLISHED
              : CourseStatus.DRAFT,
        },
      },
      { new: true },
    );

    if (!updatedCourse) {
      throw new NotFoundException('Course not found');
    }

    return updatedCourse;
  }

  async updatePointsWithAuth(
    id: string,
    points: number,
    auth: { userId: string; role: UserRole },
  ) {
    if (auth.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can update course points');
    }

    const updatedCourse = await this.courseModel.findByIdAndUpdate(
      id,
      {
        points,
        updatedBy: auth.userId,
      },
      { new: true, select: 'points' },
    );

    if (!updatedCourse) {
      throw new NotFoundException(`Course with ID ${id} not found`);
    }

    return updatedCourse;
  }

  private mapToMinimalisticDto(
    course: CourseDocument & {
      _doc?: any;
      enrollmentCount?: number;
      instructorUsername?: string;
    },
  ): MinimalisticCourseDto {
    return {
      _id: course._id.toString(),
      title: course.title,
      publicId: course.publicId,
      shortDescription: course.shortDescription,
      thumbnail: course.thumbnail?.key || '',
      category: course.category,
      subcategory: course.subcategory,
      level: course.level || CourseLevel.BEGINNER,
      price: course.price,
      discountPrice: course.discountPrice,
      enrollmentCount: course.enrollmentCount || 0,
      free: course.isFree,
      status: course.status,
      createdAt: course._doc?.createdAt || new Date(),
      updatedAt: course._doc?.updatedAt || new Date(),
      creatorType: course.creatorType,
      instructorUsername: course.instructorUsername,
      averageReview: course.averageReview || 0,
      reviewCount: course.reviewCount || 0,
    };
  }

  private mapToPublicMinimalDto(
    course: CourseDocument,
    enrollmentCount = 0,
  ): PublicMinimalCourseDto {
    const courseObj = course.toObject();
    return {
      _id: courseObj._id.toString(),
      title: courseObj.title,
      publicId: courseObj.publicId,
      shortDescription: courseObj.shortDescription,
      thumbnail: courseObj.thumbnail?.key || '',
      category: courseObj.category,
      subcategory: courseObj.subcategory,
      level: courseObj.level || CourseLevel.BEGINNER,
      price: courseObj.price,
      discountPrice: courseObj.discountPrice,
      enrollmentCount,
      free: courseObj.isFree,
      averageReview: courseObj.averageReview || 0,
      reviewCount: courseObj.reviewCount || 0,
    };
  }

  async findByCategory(
    categoryFilterDto: CategoryFilterDto,
  ): Promise<CategoryCoursesResponseDto> {
    const { category, subcategory, page = 1, limit = 10 } = categoryFilterDto;
    const skip = (page - 1) * limit;

    // Build the base query with approval logic
    const baseQuery = {
      documentStatus: DocumentStatus.ACTIVE,
      category,
      status: CourseStatus.PUBLISHED,
      $or: [
        // Always show items with creatorType 'myyresearch'
        { creatorType: CreatorType.MYRESEARCH },
        // For other creatorTypes, only show if approved
        {
          creatorType: { $ne: CreatorType.MYRESEARCH },
          approvalStatus: CourseApprovalStatus.APPROVED,
        },
      ],
    };

    // Add subcategory if provided
    if (subcategory) {
      baseQuery['subcategory'] = subcategory;
    }

    // Find all courses matching the criteria
    const [courses, total] = await Promise.all([
      this.courseModel
        .find(baseQuery)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.courseModel.countDocuments(baseQuery),
    ]);

    // Get enrollment counts for all courses
    const enrollmentCounts = await Promise.all(
      courses.map((course) =>
        this.enrollmentModel.countDocuments({
          course: course._id,
          status: 'active',
        }),
      ),
    );

    // Map courses with enrollment counts
    const processedCourses = courses.map((course, index) =>
      this.mapToPublicMinimalDto(course, enrollmentCounts[index]),
    );

    return {
      data: processedCourses,
      total,
      pages: Math.ceil(total / limit),
    };
  }

  async findAllFiltered(
    filterDto: CourseFiltersDto,
  ): Promise<FilteredCoursesResponseDto> {
    const { categories, subcategories, page = 1, limit = 10 } = filterDto;
    const skip = (page - 1) * limit;

    // Build the base query with approval logic
    const baseQuery = {
      documentStatus: DocumentStatus.ACTIVE,
      status: CourseStatus.PUBLISHED,
      $or: [
        // Always show items with creatorType 'myyresearch'
        { creatorType: CreatorType.MYRESEARCH },
        // For other creatorTypes, only show if approved
        {
          creatorType: { $ne: CreatorType.MYRESEARCH },
          approvalStatus: CourseApprovalStatus.APPROVED,
        },
      ],
    };

    // Add category filter if provided
    if (categories && categories.length > 0) {
      baseQuery['category'] = { $in: categories };
    }

    // Add subcategory filter if provided
    if (subcategories && subcategories.length > 0) {
      baseQuery['subcategory'] = { $in: subcategories };
    }

    // Find all courses matching the criteria
    const [courses, total] = await Promise.all([
      this.courseModel
        .find(baseQuery)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.courseModel.countDocuments(baseQuery),
    ]);

    // Get enrollment counts for all courses
    const enrollmentCounts = await Promise.all(
      courses.map((course) =>
        this.enrollmentModel.countDocuments({
          course: course._id,
          status: 'active',
        }),
      ),
    );

    // Map courses with enrollment counts
    const processedCourses = courses.map((course, index) =>
      this.mapToPublicMinimalDto(course, enrollmentCounts[index]),
    );

    return {
      data: processedCourses,
      total,
      page,
      limit,
      pages: Math.ceil(total / limit),
    };
  }

  async findByPublicId(
    publicId: string,
    userId?: string,
  ): Promise<PublicCourseResponseDto> {
    const course = await this.courseModel
      .findOne({
        publicId,
        documentStatus: DocumentStatus.ACTIVE,
        status: CourseStatus.PUBLISHED,
        $or: [
          // Always show items with creatorType 'myyresearch'
          { creatorType: CreatorType.MYRESEARCH },
          // For other creatorTypes, only show if approved
          {
            creatorType: { $ne: CreatorType.MYRESEARCH },
            approvalStatus: CourseApprovalStatus.APPROVED,
          },
        ],
      })
      .exec();

    if (!course) {
      throw new NotFoundException(
        `Course with public ID ${publicId} not found`,
      );
    }

    // Get instructor username if course is created by instructor
    let instructorUsername: string | undefined;
    if (course.creatorType === CreatorType.INSTRUCTOR) {
      const instructor = await this.userModel
        .findById(course.createdBy)
        .select('username')
        .lean()
        .exec();
      instructorUsername = instructor?.username;
    }

    // Get reviews for the course
    const reviews = await this.reviewModel
      .find({
        item: course._id,
        itemType: 'Course',
        status: ReviewStatus.APPROVED,
      })
      .sort({ createdAt: -1 })
      .lean()
      .exec();

    // Get all user IDs from reviews
    const userIds = reviews.map((review) => review.user);

    // Fetch user details in bulk
    const users = await this.userModel
      .find({ _id: { $in: userIds } })
      .select('firstName lastName avatar')
      .lean()
      .exec();

    // Create a map of user details for quick lookup
    const userMap = new Map(
      users.map((user) => [
        user._id.toString(),
        {
          firstName: user['firstName'] as string,
          lastName: user['lastName'] as string,
          avatar: user['avatar'] as string | undefined,
        },
      ]),
    );

    // Map reviews with user details
    const mappedReviews = reviews.map((review) => {
      const user = userMap.get(review.user.toString()) || {
        firstName: '',
        lastName: '',
        avatar: undefined,
      };
      return {
        _id: review._id.toString(),
        user: {
          _id: review.user.toString(),
          firstName: user.firstName,
          lastName: user.lastName,
          avatar: user.avatar,
        },
        rating: review.rating,
        comment: review.comment,
        createdAt: review['createdAt'] as Date,
      };
    });

    // Calculate total duration and lessons
    const totalLessons = course.sections.reduce(
      (acc, section) => acc + section.lessons.length,
      0,
    );

    const duration = course.sections.reduce(
      (acc, section) =>
        acc +
        section.lessons.reduce(
          (lessonAcc, lesson) => lessonAcc + (lesson.duration || 0),
          0,
        ),
      0,
    );

    // Get enrollment count
    const enrollmentCount = await this.enrollmentModel.countDocuments({
      course: course._id,
      status: 'active',
    });

    // Check if user has purchased this course
    let purchased = false;
    if (userId) {
      purchased = await this.ordersService.hasUserPurchasedItem(
        userId,
        course._id.toString(),
        OrderItemType.COURSE,
      );
    }

    // Check if user should have access to full course content
    const hasFullAccess = purchased || course.isFree || course.price === 0;

    // Process sections and lessons with content visibility rules
    const processedSections = course.sections.map((section) => {
      const processedLessons = section.lessons.map((lesson) => {
        const baseLesson = {
          _id: lesson._id,
          title: lesson.title,
          type: lesson.type,
          order: lesson.order,
          duration: lesson.duration || 0,
          isFree: lesson.isFree,
        };

        // Include content, video, and media if user has full access or lesson is free
        if (hasFullAccess || lesson.isFree) {
          return {
            ...baseLesson,
            content: lesson.content,
            video: lesson.video,
            media: lesson.media,
          };
        }

        return baseLesson;
      });

      return {
        _id: section._id,
        title: section.title,
        type: section.type,
        order: section.order,
        lessons: processedLessons,
      };
    });

    const responseDto: PublicCourseResponseDto = {
      _id: course._id.toString(),
      title: course.title,
      publicId: course.publicId,
      shortDescription: course.shortDescription,
      description: course.description,
      thumbnail: course.thumbnail.key,
      preview: course.preview || undefined,
      category: course.category,
      subcategory: course.subcategory,
      level: course.level || CourseLevel.BEGINNER,
      price: course.price,
      discountPrice: course.discountPrice,
      enrollmentCount,
      free: course.isFree,
      status: course.status,
      creatorType: course.creatorType,
      instructorUsername,
      whatYouWillLearn: course.whatYouWillLearn || [],
      reviews: mappedReviews,
      averageReview: course.averageReview || 0,
      reviewCount: course.reviewCount || 0,
      topics: course.tags || [],
      duration,
      totalLessons,
      sections: processedSections,
      purchased,
    };

    return responseDto;
  }
}
