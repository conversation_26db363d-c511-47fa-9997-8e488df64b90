import { OmitType } from '@nestjs/mapped-types';
import {
  CreateCourseDto,
  CreateSectionDto,
  CreateLessonDto,
} from './create-course.dto';
import { IsString, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateLessonDto extends CreateLessonDto {
  @IsString()
  @IsOptional()
  _id?: string;
}

export class UpdateSectionDto extends OmitType(CreateSectionDto, [
  'lessons',
] as const) {
  @IsString()
  @IsOptional()
  _id?: string;

  @ValidateNested({ each: true })
  @Type(() => UpdateLessonDto)
  @IsOptional()
  lessons?: UpdateLessonDto[];
}

export class UpdateCourseDto extends OmitType(CreateCourseDto, [
  'sections',
] as const) {
  @ValidateNested({ each: true })
  @Type(() => UpdateSectionDto)
  @IsOptional()
  sections?: UpdateSectionDto[];
}
