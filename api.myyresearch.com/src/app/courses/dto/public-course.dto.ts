import { CourseStatus, CourseApprovalStatus } from '../schemas/course.schema';
import { CreatorType } from '../schemas/course.schema';

export class MinimalCourseDto {
  id: string;
  publicId: string;
  title: string;
  shortDescription: string;
  thumbnail: string;
  category: string;
  subcategory: string;
  price: number;
  discountPrice?: number;
  status: CourseStatus;
  approvalStatus: CourseApprovalStatus;
  enrollmentCount: number;
  instructorUsername?: string;
  creatorType?: CreatorType;
  createdAt: Date;
  updatedAt: Date;
}

export class PublicCourseDto extends MinimalCourseDto {
  description: string;
  sections: {
    title: string;
    lessons: {
      title: string;
      type: string;
      duration: number;
    }[];
  }[];
}
