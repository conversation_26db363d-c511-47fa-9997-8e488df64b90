import { ApiProperty } from '@nestjs/swagger';
import {
  CourseStatus,
  CreatorType,
  CourseLevel,
} from '../schemas/course.schema';

export class MinimalisticCourseDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  publicId: string;

  @ApiProperty()
  shortDescription: string;

  @ApiProperty()
  thumbnail: string;

  @ApiProperty()
  category: string;

  @ApiProperty()
  subcategory: string;

  @ApiProperty({ enum: CourseLevel })
  level: CourseLevel;

  @ApiProperty()
  price: number;

  @ApiProperty({ required: false })
  discountPrice?: number;

  @ApiProperty()
  enrollmentCount: number;

  @ApiProperty()
  free: boolean;

  @ApiProperty({ enum: CourseStatus })
  status: CourseStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ enum: CreatorType })
  creatorType: CreatorType;

  @ApiProperty({ required: false })
  instructorUsername?: string;

  @ApiProperty({ description: 'Average review rating', default: 0 })
  averageReview: number;

  @ApiProperty({ description: 'Total number of reviews', default: 0 })
  reviewCount: number;
}
