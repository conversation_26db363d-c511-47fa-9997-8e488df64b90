import { ApiProperty } from '@nestjs/swagger';
import { PublicMinimalCourseDto } from './public-minimal-course.dto';

export class FilteredCoursesResponseDto {
  @ApiProperty({ type: [PublicMinimalCourseDto] })
  data: PublicMinimalCourseDto[];

  @ApiProperty({ description: 'Total number of courses matching the criteria' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages available' })
  pages: number;
}
