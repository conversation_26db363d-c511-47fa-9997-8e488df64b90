import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, <PERSON>N<PERSON><PERSON>, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class CategoryFilterDto {
  @ApiProperty({ required: true })
  @IsString()
  category: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  subcategory?: string;

  @ApiProperty({ required: false, default: 1 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ required: false, default: 10 })
  @IsNumber()
  @Min(1)
  @Max(100)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @Type(() => Number)
  limit?: number = 10;
}
