import { ApiProperty } from '@nestjs/swagger';
import {
  CourseStatus,
  CreatorType,
  LessonType,
  CourseLevel,
} from '../schemas/course.schema';
import { UploadedFile } from '../../templates/schemas/template.schema';

export class ReviewUserDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  firstName: string;

  @ApiProperty()
  lastName: string;

  @ApiProperty({ required: false })
  avatar?: string;
}

export class ReviewDto {
  @ApiProperty()
  _id: string;

  @ApiProperty({ type: ReviewUserDto })
  user: ReviewUserDto;

  @ApiProperty()
  rating: number;

  @ApiProperty()
  comment: string;

  @ApiProperty()
  createdAt: Date;
}

export class PublicLessonDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  title: string;

  @ApiProperty({ enum: LessonType })
  type: LessonType;

  @ApiProperty()
  order: number;

  @ApiProperty()
  duration: number;

  @ApiProperty()
  isFree: boolean;

  @ApiProperty({
    required: false,
    description: 'Only available for free lessons',
  })
  content?: string;

  @ApiProperty({
    required: false,
    description: 'Only available for free lessons',
  })
  video?: UploadedFile;

  @ApiProperty({
    required: false,
    description: 'Only available for free lessons',
  })
  media?: UploadedFile;
}

export class PublicSectionDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  title: string;

  @ApiProperty({ enum: LessonType, required: false })
  type?: LessonType;

  @ApiProperty()
  order: number;

  @ApiProperty({ type: [PublicLessonDto] })
  lessons: PublicLessonDto[];
}

export class PublicCourseResponseDto {
  @ApiProperty()
  _id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  publicId: string;

  @ApiProperty()
  shortDescription: string;

  @ApiProperty()
  description: string;

  @ApiProperty({ type: [String], description: 'List of learning outcomes' })
  whatYouWillLearn: string[];

  @ApiProperty()
  thumbnail: string;

  @ApiProperty({ required: false, type: () => UploadedFile })
  preview?: UploadedFile;

  @ApiProperty()
  category: string;

  @ApiProperty()
  subcategory: string;

  @ApiProperty({ enum: CourseLevel })
  level: CourseLevel;

  @ApiProperty()
  price: number;

  @ApiProperty()
  discountPrice?: number;

  @ApiProperty()
  enrollmentCount: number;

  @ApiProperty()
  free: boolean;

  @ApiProperty({ enum: CourseStatus })
  status: CourseStatus;

  @ApiProperty({ enum: CreatorType })
  creatorType: CreatorType;

  @ApiProperty()
  instructorUsername?: string;

  @ApiProperty()
  averageReview: number;

  @ApiProperty()
  reviewCount: number;

  @ApiProperty({ type: [ReviewDto] })
  reviews: ReviewDto[];

  @ApiProperty({ type: [String] })
  topics: string[];

  @ApiProperty()
  duration: number;

  @ApiProperty()
  totalLessons: number;

  @ApiProperty({ type: [PublicSectionDto] })
  sections: PublicSectionDto[];

  @ApiProperty({
    description: 'Whether the current user has purchased this course',
    default: false,
  })
  purchased: boolean;
}
