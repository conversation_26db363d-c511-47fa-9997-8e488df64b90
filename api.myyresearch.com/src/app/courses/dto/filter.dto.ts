import { IsString, IsEnum, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum FilterOperator {
  ILIKE = 'iLike',
  NOT_ILIKE = 'notILike',
  EQ = 'eq',
  NE = 'ne',
  IS_EMPTY = 'isEmpty',
  IS_NOT_EMPTY = 'isNotEmpty',
}

export enum FilterType {
  TEXT = 'text',
  MULTI_SELECT = 'multi-select',
}

export enum JoinOperator {
  AND = 'and',
  OR = 'or',
}

export class FilterItemDto {
  @ApiProperty({
    description: 'Field name to filter by',
    example: 'title',
    enum: [
      'title',
      'shortDescription',
      'category',
      'subcategory',
      'status',
      'creatorType',
      'createdAt',
      'updatedAt',
    ],
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Filter value (string or array for multi-select)',
    example: 'Introduction to',
    required: false,
  })
  @IsOptional()
  value: string | string[];

  @ApiProperty({
    description: 'Type of filter field',
    enum: FilterType,
    example: FilterType.TEXT,
  })
  @IsEnum(FilterType)
  type: FilterType;

  @ApiProperty({
    description: 'Filter operator',
    enum: FilterOperator,
    example: FilterOperator.ILIKE,
  })
  @IsEnum(FilterOperator)
  operator: FilterOperator;

  @ApiProperty({
    description: 'Unique identifier for the filter row',
    example: 'PfsMrx',
  })
  @IsString()
  rowId: string;
}
