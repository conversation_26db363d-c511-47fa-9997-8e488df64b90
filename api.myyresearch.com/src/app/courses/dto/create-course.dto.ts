import {
  IsString,
  <PERSON><PERSON><PERSON>ber,
  IsOptional,
  IsEnum,
  IsArray,
  ValidateNested,
  IsBoolean,
  Min,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  CourseStatus,
  LessonType,
  CourseLevel,
} from '../schemas/course.schema';
import { ApiProperty } from '@nestjs/swagger';

export class UploadFileDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  key: string;

  @IsString()
  @IsNotEmpty()
  type: string;

  @IsNumber()
  @Min(0)
  @IsNotEmpty()
  size: number;
}

export class CreateLessonDto {
  @IsString()
  title: string;

  @IsEnum(LessonType)
  type: LessonType;

  @IsString()
  @IsOptional()
  content?: string;

  @IsNumber()
  @IsOptional()
  duration?: number;

  @ValidateNested()
  @Type(() => UploadFileDto)
  @IsOptional()
  video?: UploadFileDto;

  @ValidateNested()
  @Type(() => UploadFileDto)
  @IsOptional()
  media?: UploadFileDto;

  @IsNumber()
  @IsOptional()
  order?: number;

  @IsOptional()
  isFree?: boolean;
}

export class CreateSectionDto {
  @IsString()
  title: string;

  @IsNumber()
  @IsOptional()
  order?: number;

  @IsEnum(LessonType)
  @IsOptional()
  type?: LessonType;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateLessonDto)
  @IsOptional()
  lessons?: CreateLessonDto[];
}

export class CreateCourseDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  shortDescription: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ type: [String], description: 'List of learning outcomes' })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  whatYouWillLearn?: string[];

  @ValidateNested()
  @Type(() => UploadFileDto)
  thumbnail: UploadFileDto;

  @ValidateNested()
  @Type(() => UploadFileDto)
  @IsOptional()
  preview?: UploadFileDto;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  category: string;

  @IsString()
  subcategory: string;

  @ApiProperty({ enum: CourseLevel, default: CourseLevel.BEGINNER })
  @IsEnum(CourseLevel)
  @IsOptional()
  level?: CourseLevel;

  @IsBoolean()
  @IsOptional()
  isFree?: boolean;

  @IsEnum(CourseStatus)
  @IsOptional()
  status?: CourseStatus;

  @IsNumber()
  price: number;

  @IsNumber()
  @IsOptional()
  discountPrice?: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSectionDto)
  @IsOptional()
  sections?: CreateSectionDto[];
}
