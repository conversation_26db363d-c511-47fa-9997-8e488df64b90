import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsNumber, Min, Max } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class CourseFiltersDto {
  @ApiProperty({
    required: false,
    type: [String],
    description: 'Array of categories to filter by',
  })
  @IsArray()
  @IsOptional()
  @Transform(({ value }) =>
    Array.isArray(value) ? value : value ? [value] : [],
  )
  categories?: string[] = [];

  @ApiProperty({
    required: false,
    type: [String],
    description: 'Array of subcategories to filter by',
  })
  @IsArray()
  @IsOptional()
  @Transform(({ value }) =>
    Array.isArray(value) ? value : value ? [value] : [],
  )
  subcategories?: string[] = [];

  @ApiProperty({ required: false, default: 1 })
  @IsNumber()
  @Min(1)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({ required: false, default: 10 })
  @IsNumber()
  @Min(1)
  @Max(100)
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @Type(() => Number)
  limit?: number = 10;
}
