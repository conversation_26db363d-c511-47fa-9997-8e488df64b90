import { ApiProperty } from '@nestjs/swagger';
import { PublicMinimalCourseDto } from './public-minimal-course.dto';

export class CategoryCoursesResponseDto {
  @ApiProperty({ type: [PublicMinimalCourseDto] })
  data: PublicMinimalCourseDto[];

  @ApiProperty({ description: 'Total number of courses matching the criteria' })
  total: number;

  @ApiProperty({ description: 'Total number of pages available' })
  pages: number;
}
