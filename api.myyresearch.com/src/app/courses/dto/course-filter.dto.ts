import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { CourseStatus, CreatorType } from '../schemas/course.schema';
import { SortItemDto } from './sort.dto';
import { FilterItemDto, JoinOperator } from './filter.dto';

export class CourseFilterDto {
  @ApiPropertyOptional({ description: 'Search by title or description' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    enum: CourseStatus,
    description: 'Filter by course status',
  })
  @IsOptional()
  @IsEnum(CourseStatus)
  status?: CourseStatus;

  @ApiPropertyOptional({
    enum: CreatorType,
    description: 'Filter by creator type',
  })
  @IsOptional()
  @IsEnum(CreatorType)
  creatorType?: CreatorType;

  @ApiPropertyOptional({ description: 'Filter by category' })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ description: 'Filter by subcategory' })
  @IsOptional()
  @IsString()
  subcategory?: string;

  @ApiPropertyOptional({ description: 'Number of items per page' })
  @IsOptional()
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Page number' })
  @IsOptional()
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({
    type: [SortItemDto],
    description:
      'Sort criteria array. Example: [{"id":"createdAt","desc":true}]',
  })
  @IsOptional()
  @Type(() => SortItemDto)
  sort?: SortItemDto[];

  @ApiPropertyOptional({
    type: [FilterItemDto],
    description: 'Advanced filter criteria array',
  })
  @IsOptional()
  @Type(() => FilterItemDto)
  filters?: FilterItemDto[];

  @ApiPropertyOptional({
    enum: JoinOperator,
    description: 'Operator to join multiple filters (and/or)',
    default: JoinOperator.AND,
  })
  @IsOptional()
  @IsEnum(JoinOperator)
  joinOperator?: JoinOperator = JoinOperator.AND;
}
