import { IsString, <PERSON><PERSON><PERSON>ber, IsEnum, IsOptional } from 'class-validator';
import { LessonType } from '../schemas/course.schema';

export class CreateLessonDto {
  @IsString()
  title: string;

  @IsEnum(LessonType)
  type: LessonType;

  @IsString()
  @IsOptional()
  content?: string;

  @IsNumber()
  @IsOptional()
  duration?: number;

  @IsString()
  @IsOptional()
  videoUrl?: string;

  @IsNumber()
  @IsOptional()
  order?: number;
}
