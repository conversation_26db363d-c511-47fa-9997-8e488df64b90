import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { UploadedFile } from '../../templates/schemas/template.schema';
import { nanoid } from 'nanoid';

export type CourseDocument = Course & Document;

export enum LessonType {
  VIDEO = 'video',
  TEXT = 'text',
}

export enum CourseStatus {
  PUBLISHED = 'published',
  DRAFT = 'draft',
  PENDING = 'pending',
}

export enum CourseLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
}

export enum CourseApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

export enum DocumentStatus {
  ACTIVE = 'active',
  DELETED = 'deleted',
  ARCHIVED = 'archived',
}

export enum CreatorType {
  MYRESEARCH = 'myresearch',
  INSTRUCTOR = 'instructor',
}

@Schema()
export class Lesson {
  @Prop({
    required: true,
    default: () => new MongooseSchema.Types.ObjectId('000000000000').toString(),
  })
  _id: string;

  @Prop({ required: true })
  title: string;

  @Prop({ type: String, enum: LessonType, required: true })
  type: LessonType;

  @Prop()
  content?: string;

  @Prop()
  duration?: number;

  @Prop({ type: UploadedFile })
  video?: UploadedFile;

  @Prop({ type: UploadedFile })
  media?: UploadedFile;

  @Prop({ required: true })
  order: number;

  @Prop({ required: true, default: false })
  isFree: boolean;
}

@Schema()
export class Section {
  @Prop({
    required: true,
    default: () => new MongooseSchema.Types.ObjectId('000000000000').toString(),
  })
  _id: string;

  @Prop({ required: true })
  title: string;

  @Prop({ type: String, enum: LessonType })
  type?: LessonType;

  @Prop({ type: [Lesson], default: [] })
  lessons: Lesson[];

  @Prop({ required: true })
  order: number;
}

@Schema({ timestamps: true })
export class Course {
  @Prop({
    required: false,
    default: () => nanoid(10),
  })
  publicId?: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  shortDescription: string;

  @Prop()
  description?: string;

  @Prop({ type: [String], default: [] })
  whatYouWillLearn: string[];

  @Prop({ type: UploadedFile, required: true })
  thumbnail: UploadedFile;

  @Prop({ type: UploadedFile })
  preview?: UploadedFile;

  @Prop({ required: true })
  category: string;

  @Prop({ required: true })
  subcategory: string;

  @Prop({ type: String, enum: CourseLevel, default: CourseLevel.BEGINNER })
  level: CourseLevel;

  @Prop({ required: true })
  price: number;

  @Prop()
  discountPrice?: number;

  @Prop({ type: [Section], default: [] })
  sections: Section[];

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: Boolean, default: false })
  isFree: boolean;

  @Prop({ type: String, enum: CourseStatus, default: CourseStatus.DRAFT })
  status: CourseStatus;

  @Prop({ type: String, enum: DocumentStatus, default: DocumentStatus.ACTIVE })
  documentStatus: DocumentStatus;

  @Prop({
    type: String,
    enum: CourseApprovalStatus,
    default: CourseApprovalStatus.PENDING,
  })
  approvalStatus: CourseApprovalStatus;

  @Prop()
  approvalComment?: string;

  @Prop({ type: MongooseSchema.Types.ObjectId })
  approvedBy?: string;

  @Prop()
  approvalDate?: Date;

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true })
  createdBy: string;

  @Prop({ type: MongooseSchema.Types.ObjectId })
  updatedBy?: string;

  @Prop({ type: String, enum: CreatorType, required: true })
  creatorType: CreatorType;

  @Prop({ default: 0 })
  points: number;

  @Prop({ type: MongooseSchema.Types.ObjectId })
  originalId?: string;

  @Prop({ type: Number, default: 0 })
  averageReview: number;

  @Prop({ type: Number, default: 0 })
  reviewCount: number;
}

export const CourseSchema = SchemaFactory.createForClass(Course);

CourseSchema.index({ publicId: 1 }, { unique: true, sparse: true });
