import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { CoursesService } from './courses.service';
import { Course } from './schemas/course.schema';
import { CreateCourseDto } from './dto/create-course.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateSectionDto } from './dto/create-section.dto';
import { EnrolledCourseDto } from './dto/enrolled-course.dto';
import { Roles } from '../auth/decorators/roles.decorator';
import { ApproveCourseDto } from './dto/approve-course.dto';
import { UserRole } from '../users/schemas/user.schema';
import { UpdatePointsDto } from './dto/update-points.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CourseFilterDto } from './dto/course-filter.dto';
import { GetAllCoursesResponseDto } from './dto/get-all-courses-response.dto';
import { UpdateCourseDto } from './dto/update-course.dto';
import { CategoryFilterDto } from './dto/category-filter.dto';
import { CategoryCoursesResponseDto } from './dto/category-courses-response.dto';
import { CourseFiltersDto } from './dto/course-filters.dto';
import { FilteredCoursesResponseDto } from './dto/filtered-courses-response.dto';
import { PublicCourseResponseDto } from './dto/public-course-response.dto';
import { Public } from '../auth/decorators/public.decorator';
import { RolesGuard } from '@app/auth/guards/roles.guard';
import { OptionalAuthGuard } from '../cart/guards/optional-auth.guard';
import { GetUser } from '../auth/decorators/get-user.decorator';

@ApiTags('courses')
@Controller('courses')
export class CoursesController {
  constructor(private readonly coursesService: CoursesService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.INSTRUCTOR, UserRole.STAFF)
  create(@Body() createCourseDto: CreateCourseDto, @Request() req) {
    return this.coursesService.createWithAuth({
      createCourseDto,
      userId: req.user._id,
      userRole: req.user.role,
    });
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.INSTRUCTOR, UserRole.STAFF)
  @ApiOperation({ summary: 'Update course (Admin, Staff, Instructor)' })
  @ApiResponse({
    status: 200,
    description: 'Course updated successfully',
    type: Course,
  })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Course not found' })
  update(
    @Param('id') id: string,
    @Body() updateCourseDto: UpdateCourseDto,
    @Request() req,
  ) {
    return this.coursesService.updateWithAuth(id, updateCourseDto, {
      userId: req.user._id,
      role: req.user.role,
    });
  }

  // Student endpoints
  @Get('enrolled')
  @UseGuards(JwtAuthGuard)
  findEnrolledCourses(@Request() req): Promise<EnrolledCourseDto[]> {
    return this.coursesService.findEnrolledCourses(req.user._id);
  }

  @Get('enrolled/:publicId')
  @UseGuards(JwtAuthGuard)
  findEnrolledCourse(
    @Param('publicId') publicId: string,
    @Request() req,
  ): Promise<EnrolledCourseDto> {
    return this.coursesService.findEnrolledCourseByPublicId(
      publicId,
      req.user._id,
    );
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all courses (Admin, Staff, Instructor)' })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered courses with pagination',
    type: GetAllCoursesResponseDto,
  })
  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.STAFF, UserRole.INSTRUCTOR)
  findAll(
    @Query() filterDto: CourseFilterDto,
    @Request() req,
  ): Promise<GetAllCoursesResponseDto> {
    return this.coursesService.findAllWithAuth(
      {
        userId: req.user._id,
        role: req.user.role,
      },
      filterDto,
    );
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.STAFF, UserRole.INSTRUCTOR)
  findOne(@Param('id') id: string, @Request() req) {
    return this.coursesService.findOneWithAuth(id, {
      userId: req.user._id,
      role: req.user.role,
    });
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.STAFF, UserRole.INSTRUCTOR)
  @ApiOperation({ summary: 'Delete course (Admin, Staff, Instructor)' })
  @ApiResponse({
    status: 200,
    description: 'Course deleted successfully (soft delete)',
  })
  remove(@Param('id') id: string, @Request() req) {
    return this.coursesService.removeWithAuth(id, {
      userId: req.user._id,
      role: req.user.role,
    });
  }

  // Section management endpoints
  @Post(':id/sections')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  addSection(
    @Param('id') id: string,
    @Body() createSectionDto: CreateSectionDto,
    @Request() req,
  ) {
    return this.coursesService.addSectionWithAuth(id, createSectionDto, {
      userId: req.user._id,
      role: req.user.role,
    });
  }

  @Patch(':courseId/sections/:sectionId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  updateSection(
    @Param('courseId') courseId: string,
    @Param('sectionId') sectionId: string,
    @Body() updateSectionDto: Partial<CreateSectionDto>,
    @Request() req,
  ) {
    return this.coursesService.updateSectionWithAuth(
      courseId,
      sectionId,
      updateSectionDto,
      {
        userId: req.user._id,
        role: req.user.role,
      },
    );
  }

  @Patch(':id/approve')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  approveCourse(
    @Param('id') id: string,
    @Body() approveCourseDto: ApproveCourseDto,
    @Request() req,
  ) {
    return this.coursesService.approveCourse(
      id,
      approveCourseDto,
      req.user._id,
    );
  }

  @Get('admin/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.STAFF)
  findCourseForAdmin(@Param('id') id: string): Promise<Course> {
    return this.coursesService.findOne(id);
  }

  @Patch(':id/points')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update course points (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Course points updated successfully',
  })
  updatePoints(
    @Param('id') id: string,
    @Body() updatePointsDto: UpdatePointsDto,
    @Request() req,
  ) {
    return this.coursesService.updatePointsWithAuth(
      id,
      updatePointsDto.points,
      {
        userId: req.user._id,
        role: req.user.role,
      },
    );
  }

  @Public()
  @Get('public/by-category')
  @ApiOperation({
    summary: 'Get all public courses by category with pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns courses filtered by category with pagination',
    type: CategoryCoursesResponseDto,
  })
  async findByCategory(
    @Query() categoryFilterDto: CategoryFilterDto,
  ): Promise<CategoryCoursesResponseDto> {
    return this.coursesService.findByCategory(categoryFilterDto);
  }

  @Public()
  @Get('public/filter')
  @ApiOperation({
    summary: 'Get all public courses with multiple filters and pagination',
    description: 'Filter courses by categories and subcategories',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered courses with pagination',
    type: FilteredCoursesResponseDto,
  })
  async findAllFiltered(
    @Query() filterDto: CourseFiltersDto,
  ): Promise<FilteredCoursesResponseDto> {
    return this.coursesService.findAllFiltered(filterDto);
  }

  @UseGuards(OptionalAuthGuard)
  @Get('public/:publicId')
  @ApiOperation({
    summary: 'Get public course details by publicId',
    description:
      'Returns course details including reviews and purchase status if authenticated',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns course details with reviews and purchase status',
    type: PublicCourseResponseDto,
  })
  async findByPublicId(
    @Param('publicId') publicId: string,
    @GetUser() user?: any,
  ): Promise<PublicCourseResponseDto> {
    return this.coursesService.findByPublicId(publicId, user?._id);
  }
}
