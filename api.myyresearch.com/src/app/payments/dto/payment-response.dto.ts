import { ApiProperty } from '@nestjs/swagger';
import { PaymentStatus, PaymentMethod } from '../schemas/payment.schema';

export class PaymentDto {
  @ApiProperty({ example: '507f1f77bcf86cd799439011' })
  _id: string;

  @ApiProperty({ example: '507f1f77bcf86cd799439012' })
  userId: string;

  @ApiProperty({ example: '507f1f77bcf86cd799439013' })
  orderId: string;

  @ApiProperty({ example: 'pi_1234567890' })
  stripePaymentIntentId: string;

  @ApiProperty({ example: 'ch_1234567890', required: false })
  stripeChargeId?: string;

  @ApiProperty({ example: 9999 })
  amount: number;

  @ApiProperty({ example: 'usd' })
  currency: string;

  @ApiProperty({ enum: PaymentStatus, example: PaymentStatus.SUCCEEDED })
  status: PaymentStatus;

  @ApiProperty({ enum: PaymentMethod, example: PaymentMethod.CARD, required: false })
  paymentMethod?: PaymentMethod;

  @ApiProperty({ example: 'Visa ending in 4242', required: false })
  paymentMethodDetails?: string;

  @ApiProperty({ example: 'https://pay.stripe.com/receipts/...', required: false })
  receiptUrl?: string;

  @ApiProperty({ example: '2024-01-15T10:30:00Z', required: false })
  paidAt?: Date;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  createdAt: Date;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  updatedAt: Date;
}

export class GetPaymentResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Payment retrieved successfully' })
  message: string;

  @ApiProperty({ type: PaymentDto })
  payment: PaymentDto;
}

export class GetPaymentsResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Payments retrieved successfully' })
  message: string;

  @ApiProperty({ type: [PaymentDto] })
  payments: PaymentDto[];

  @ApiProperty({ example: 25 })
  total: number;

  @ApiProperty({ example: 1 })
  page: number;

  @ApiProperty({ example: 3 })
  totalPages: number;
}
