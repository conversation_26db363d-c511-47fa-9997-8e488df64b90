import { ApiProperty } from '@nestjs/swagger';
import { IsMongoId, IsOptional, IsObject, IsString } from 'class-validator';

export class CreatePaymentIntentDto {
  @ApiProperty({
    description: 'Order ID to create payment intent for',
    example: '507f1f77bcf86cd799439011',
  })
  @IsMongoId()
  orderId: string;

  @ApiProperty({
    description: 'Cart ID associated with the order',
    example: '507f1f77bcf86cd799439012',
  })
  @IsString()
  cartId: string;

  @ApiProperty({
    description: 'Additional metadata for the payment',
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class CreatePaymentIntentResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Payment intent created successfully' })
  message: string;

  @ApiProperty({
    description: 'Stripe client secret for frontend payment processing',
    example: 'pi_1234567890_secret_abcdefghijk',
  })
  clientSecret: string;

  @ApiProperty({
    description: 'Payment intent ID',
    example: 'pi_1234567890',
  })
  paymentIntentId: string;

  @ApiProperty({
    description: 'Amount in cents',
    example: 9999,
  })
  amount: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'usd',
  })
  currency: string;
}
