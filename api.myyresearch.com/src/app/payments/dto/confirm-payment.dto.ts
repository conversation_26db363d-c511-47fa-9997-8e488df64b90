import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject } from 'class-validator';

export class ConfirmPaymentDto {
  @ApiProperty({
    description: 'Stripe payment intent ID',
    example: 'pi_1234567890',
  })
  @IsString()
  paymentIntentId: string;

  @ApiProperty({
    description: 'Additional metadata for the payment confirmation',
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class ConfirmPaymentResponseDto {
  @ApiProperty({ example: true })
  success: boolean;

  @ApiProperty({ example: 'Payment confirmed successfully' })
  message: string;

  @ApiProperty({
    description: 'Updated order information',
  })
  order: any;

  @ApiProperty({
    description: 'Payment record information',
  })
  payment: any;
}
