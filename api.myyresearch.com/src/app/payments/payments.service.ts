import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import {
  Payment,
  PaymentDocument,
  PaymentStatus,
  PaymentMethod,
} from './schemas/payment.schema';
import { Order, OrderDocument } from '../orders/schemas/order.schema';

import {
  Enrollment,
  EnrollmentDocument,
} from '../enrollments/schemas/enrollment.schema';
import {
  TemplateEnrollment,
  TemplateEnrollmentDocument,
} from '../template-enrollments/schemas/template-enrollment.schema';
import { User, UserDocument } from '../users/schemas/user.schema';
import { OrdersService } from '../orders/orders.service';
import {
  CreatePaymentIntentDto,
  CreatePaymentIntentResponseDto,
} from './dto/create-payment-intent.dto';
import {
  ConfirmPaymentDto,
  ConfirmPaymentResponseDto,
} from './dto/confirm-payment.dto';

@Injectable()
export class PaymentsService {
  private stripe: Stripe;

  constructor(
    @InjectModel(Payment.name) private paymentModel: Model<PaymentDocument>,
    @InjectModel(Order.name) private orderModel: Model<OrderDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(Enrollment.name)
    private enrollmentModel: Model<EnrollmentDocument>,
    @InjectModel(TemplateEnrollment.name)
    private templateEnrollmentModel: Model<TemplateEnrollmentDocument>,
    private ordersService: OrdersService,
    private configService: ConfigService,
  ) {
    this.stripe = new Stripe(
      this.configService.get<string>('STRIPE_SECRET_KEY'),
      {
        apiVersion: '2025-01-27.acacia',
      },
    );
  }

  async createPaymentIntent(
    userId: string,
    createPaymentIntentDto: CreatePaymentIntentDto,
  ): Promise<CreatePaymentIntentResponseDto> {
    const { orderId, metadata } = createPaymentIntentDto;

    // Get the order
    const order = await this.orderModel.findOne({
      _id: orderId,
      userId,
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    if (order.paymentStatus !== 'pending') {
      throw new BadRequestException('Order payment is not pending');
    }

    // Get or create Stripe customer
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    let customerId = user.stripeCustomerId;
    // if (!customerId) {
    //   const customer = await this.stripe.customers.create({
    //     email: user.email,
    //     metadata: {
    //       userId: userId,
    //     },
    //   });
    //   customerId = customer.id;

    //   // Update user with Stripe customer ID
    //   await this.userModel.findByIdAndUpdate(userId, {
    //     stripeCustomerId: customerId,
    //   });
    // }

    try {
      // Create payment intent
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(order.total * 100), // Convert to cents
        currency: 'usd',
        // customer: customerId,
        metadata: {
          orderId: order._id.toString(),
          userId: userId,
          ...metadata,
        },
        automatic_payment_methods: {
          enabled: true,
        },
      });

      // Update order with payment intent ID
      await this.orderModel.findByIdAndUpdate(orderId, {
        paymentIntentId: paymentIntent.id,
      });

      // Create payment record
      await this.paymentModel.create({
        userId,
        orderId,
        stripePaymentIntentId: paymentIntent.id,
        amount: order.total,
        currency: 'usd',
        status: PaymentStatus.PENDING,
        metadata,
      });

      return {
        success: true,
        message: 'Payment intent created successfully',
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        amount: Math.round(order.total * 100),
        currency: 'usd',
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to create payment intent: ${error.message}`,
      );
    }
  }

  async confirmPayment(
    userId: string,
    confirmPaymentDto: ConfirmPaymentDto,
  ): Promise<ConfirmPaymentResponseDto> {
    const { paymentIntentId, metadata } = confirmPaymentDto;

    try {
      // Retrieve payment intent from Stripe
      const paymentIntent =
        await this.stripe.paymentIntents.retrieve(paymentIntentId);

      if (paymentIntent.status !== 'succeeded') {
        throw new BadRequestException('Payment has not succeeded');
      }

      // Find the payment record
      const payment = await this.paymentModel.findOne({
        stripePaymentIntentId: paymentIntentId,
        userId,
      });

      if (!payment) {
        throw new NotFoundException('Payment record not found');
      }

      // Find the order
      const order = await this.orderModel.findById(payment.orderId);
      if (!order) {
        throw new NotFoundException('Order not found');
      }

      // Update payment record
      await this.paymentModel.findByIdAndUpdate(payment._id, {
        status: PaymentStatus.SUCCEEDED,
        stripeChargeId: paymentIntent.latest_charge as string,
        paymentMethod: this.getPaymentMethodType(paymentIntent.payment_method),
        paymentMethodDetails: this.getPaymentMethodDetails(paymentIntent),
        receiptUrl: paymentIntent.receipt_email
          ? `Receipt sent to ${paymentIntent.receipt_email}`
          : undefined,
        paidAt: new Date(),
        metadata: { ...payment.metadata, ...metadata },
      });

      // Update order status
      const updatedOrder = await this.ordersService.updatePaymentStatus(
        order._id.toString(),
        'completed' as any,
        {
          paymentIntentId: paymentIntentId,
          stripePaymentId: paymentIntent.latest_charge,
        },
      );

      // Create enrollments for purchased items
      await this.createEnrollments(userId, order);

      // Note: Cart is already cleared when the order was created

      return {
        success: true,
        message: 'Payment confirmed successfully',
        order: updatedOrder.toObject(),
        payment: payment.toObject(),
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to confirm payment: ${error.message}`,
      );
    }
  }

  private async createEnrollments(
    userId: string,
    order: OrderDocument,
  ): Promise<void> {
    for (const item of order.items) {
      if (item.itemType === 'course') {
        // Check if enrollment already exists
        const existingEnrollment = await this.enrollmentModel.findOne({
          userId,
          courseId: item.itemId,
        });

        if (!existingEnrollment) {
          await this.enrollmentModel.create({
            userId,
            courseId: item.itemId,
            progress: 0,
            completed: false,
            lessonsProgress: [],
          });
        }
      } else if (item.itemType === 'template') {
        // Check if template enrollment already exists
        const existingTemplateEnrollment =
          await this.templateEnrollmentModel.findOne({
            userId,
            templateId: item.itemId,
          });

        if (!existingTemplateEnrollment) {
          await this.templateEnrollmentModel.create({
            userId,
            templateId: item.itemId,
            downloaded: false,
            downloadCount: 0,
          });
        }
      }
    }
  }

  private getPaymentMethodType(paymentMethod: any): PaymentMethod {
    if (typeof paymentMethod === 'string') {
      return PaymentMethod.CARD; // Default fallback
    }

    switch (paymentMethod?.type) {
      case 'card':
        return PaymentMethod.CARD;
      case 'bank_transfer':
        return PaymentMethod.BANK_TRANSFER;
      default:
        return PaymentMethod.CARD;
    }
  }

  private getPaymentMethodDetails(paymentIntent: Stripe.PaymentIntent): string {
    const paymentMethod = paymentIntent.payment_method;
    if (typeof paymentMethod === 'string') {
      return 'Card payment';
    }

    if (paymentMethod?.card) {
      return `${paymentMethod.card.brand?.toUpperCase()} ending in ${paymentMethod.card.last4}`;
    }

    return 'Payment method';
  }
}
