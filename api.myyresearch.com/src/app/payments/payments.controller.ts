import { Controller, Post, Body, UseGuards, Request } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { PaymentsService } from './payments.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  CreatePaymentIntentDto,
  CreatePaymentIntentResponseDto,
} from './dto/create-payment-intent.dto';
import {
  ConfirmPaymentDto,
  ConfirmPaymentResponseDto,
} from './dto/confirm-payment.dto';

@ApiTags('Payments')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('payments')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post('create-intent')
  @ApiOperation({ summary: 'Create Stripe payment intent for order' })
  @ApiResponse({
    status: 201,
    description: 'Payment intent created successfully',
    type: CreatePaymentIntentResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
  })
  @ApiResponse({
    status: 400,
    description: 'Order payment is not pending',
  })
  async createPaymentIntent(
    @Request() req: any,
    @Body() createPaymentIntentDto: CreatePaymentIntentDto,
  ): Promise<CreatePaymentIntentResponseDto> {
    return this.paymentsService.createPaymentIntent(
      req.user._id,
      createPaymentIntentDto,
    );
  }

  @Post('confirm')
  @ApiOperation({ summary: 'Confirm payment and complete order' })
  @ApiResponse({
    status: 200,
    description: 'Payment confirmed successfully',
    type: ConfirmPaymentResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Payment or order not found',
  })
  @ApiResponse({
    status: 400,
    description: 'Payment has not succeeded',
  })
  async confirmPayment(
    @Request() req: any,
    @Body() confirmPaymentDto: ConfirmPaymentDto,
  ): Promise<ConfirmPaymentResponseDto> {
    return this.paymentsService.confirmPayment(req.user._id, confirmPaymentDto);
  }
}
