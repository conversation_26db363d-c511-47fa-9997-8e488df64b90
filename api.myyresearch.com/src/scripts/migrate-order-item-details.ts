import { NestFactory } from '@nestjs/core';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  OrderItemDetails,
  OrderItemDetailsDocument,
  OrderItemType,
} from '../app/orders/schemas/order.schema';
import { Course, CourseDocument } from '../app/courses/schemas/course.schema';
import {
  Template,
  TemplateDocument,
} from '../app/templates/schemas/template.schema';
import { AppModule } from '@app/app.module';

export class OrderItemDetailsMigration {
  constructor(
    @InjectModel(OrderItemDetails.name)
    private orderItemDetailsModel: Model<OrderItemDetailsDocument>,
    @InjectModel(Course.name)
    private courseModel: Model<CourseDocument>,
    @InjectModel(Template.name)
    private templateModel: Model<TemplateDocument>,
  ) {}

  async migrateOrderItemDetails() {
    console.log('Starting OrderItemDetails migration...');

    try {
      // Find all OrderItemDetails records that don't have category/subCategory
      const orderItems = await this.orderItemDetailsModel
        .find({
          $or: [
            { category: { $exists: false } },
            { subCategory: { $exists: false } },
            { orderId: { $exists: false } },
          ],
        })
        .lean()
        .exec();

      console.log(`Found ${orderItems.length} records to migrate`);

      let migratedCount = 0;
      let errorCount = 0;

      for (const orderItem of orderItems) {
        try {
          let itemDetails: any = null;

          // Fetch item details based on itemType
          if (orderItem.itemType === OrderItemType.COURSE) {
            itemDetails = await this.courseModel
              .findById(orderItem.itemId)
              .select('category subcategory')
              .lean()
              .exec();
          } else if (orderItem.itemType === OrderItemType.TEMPLATE) {
            itemDetails = await this.templateModel
              .findById(orderItem.itemId)
              .select('category subcategory')
              .lean()
              .exec();
          }

          if (!itemDetails) {
            console.warn(
              `Item not found for itemType: ${orderItem.itemType}, itemId: ${orderItem.itemId}`,
            );
            errorCount++;
            continue;
          }

          // Prepare update data
          const updateData: any = {};

          if (!orderItem.category && itemDetails.category) {
            updateData.category = itemDetails.category;
          }

          if (!orderItem.subCategory && itemDetails.subcategory) {
            updateData.subCategory = itemDetails.subcategory;
          }

          // Generate orderId if missing (using a simple format)
          if (!orderItem.orderId) {
            updateData.orderId = `ORD-${orderItem._id.toString().slice(-8).toUpperCase()}`;
          }

          // Update the record if there's data to update
          if (Object.keys(updateData).length > 0) {
            await this.orderItemDetailsModel.updateOne(
              { _id: orderItem._id },
              { $set: updateData },
            );

            migratedCount++;
            console.log(
              `Migrated OrderItemDetails ${orderItem._id}: ${JSON.stringify(updateData)}`,
            );
          }
        } catch (error) {
          console.error(
            `Error migrating OrderItemDetails ${orderItem._id}:`,
            error,
          );
          errorCount++;
        }
      }

      console.log(`Migration completed:`);
      console.log(`- Successfully migrated: ${migratedCount} records`);
      console.log(`- Errors: ${errorCount} records`);
      console.log(`- Total processed: ${orderItems.length} records`);

      return {
        total: orderItems.length,
        migrated: migratedCount,
        errors: errorCount,
      };
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }
}

// Script runner
async function runMigration() {
  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    const migration = app.get(OrderItemDetailsMigration);
    const result = await migration.migrateOrderItemDetails();

    console.log('\n=== Migration Summary ===');
    console.log(`Total records processed: ${result.total}`);
    console.log(`Successfully migrated: ${result.migrated}`);
    console.log(`Errors encountered: ${result.errors}`);
    console.log('=========================\n');

    if (result.errors > 0) {
      console.warn(
        'Migration completed with errors. Please review the logs above.',
      );
      process.exit(1);
    } else {
      console.log('Migration completed successfully!');
      process.exit(0);
    }
  } catch (error) {
    console.error('Migration script failed:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  runMigration();
}

export { runMigration };
