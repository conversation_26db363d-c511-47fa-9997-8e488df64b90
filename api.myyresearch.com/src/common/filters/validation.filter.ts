import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Response } from 'express';
import { MongoError } from 'mongodb';

@Catch()
export class ValidationFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    if (exception instanceof HttpException) {
      // Handle NestJS exceptions
      const status = exception.getStatus();
      const exceptionResponse = exception.getResponse() as any;

      return response.status(status).json({
        statusCode: status,
        message: exceptionResponse.message || exception.message,
        error: exceptionResponse.error || 'Bad Request',
      });
    } else if (exception.name === 'ValidationError') {
      // Handle mongoose validation errors
      return response.status(HttpStatus.BAD_REQUEST).json({
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed',
        errors: Object.values(exception.errors).map((err: any) => ({
          field: err.path,
          message: err.message,
        })),
      });
    } else if (exception instanceof MongoError && exception.code === 11000) {
      // Handle duplicate key errors
      return response.status(HttpStatus.CONFLICT).json({
        statusCode: HttpStatus.CONFLICT,
        message: 'Duplicate entry',
        error: 'This value already exists',
      });
    }

    // Handle other errors
    return response.status(HttpStatus.BAD_REQUEST).json({
      statusCode: HttpStatus.BAD_REQUEST,
      message: 'Invalid input data',
      error: exception.message,
    });
  }
}
