import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EnvironmentConfig } from '@shared/models/env/environment-config.model';

@Injectable()
export class EnvironmentConfigService {
  constructor(
    private readonly configService: ConfigService<EnvironmentConfig, true>,
  ) {}

  getNodeEnv(): string {
    return this.configService.getOrThrow<string>(`NODE_ENV`);
  }

  getPort(): string {
    return this.configService.getOrThrow<string>(`PORT`);
  }

  getGoogleClientId(): string {
    return this.configService.getOrThrow<string>(`GOOGLE_CLIENT_ID`);
  }

  getGoogleClientSecret(): string {
    return this.configService.getOrThrow<string>(`GOOGLE_CLIENT_SECRET`);
  }

  getMongoDbUri(): string {
    return this.configService.getOrThrow<string>(`MONGODB_URI`);
  }

  getJwtAccessSecret(): string {
    return this.configService.getOrThrow<string>(`JWT_ACCESS_SECRET`);
  }

  getJwtRefreshSecret(): string {
    return this.configService.getOrThrow<string>(`JWT_REFRESH_SECRET`);
  }

  getTokenEncryptionKey(): string {
    return this.configService.getOrThrow<string>(`TOKEN_ENCRYPTION_KEY`);
  }

  getResendApiKey(): string {
    return this.configService.getOrThrow<string>(`RESEND_API_KEY`);
  }

  getFrontendUrl(): string {
    return this.configService.getOrThrow<string>(`FRONTEND_URL`);
  }

  getApiUrl(): string {
    return this.configService.getOrThrow<string>(`API_URL`);
  }

  getFacebookAppId(): string {
    return this.configService.getOrThrow<string>(`FACEBOOK_APP_ID`);
  }

  getFacebookAppSecret(): string {
    return this.configService.getOrThrow<string>(`FACEBOOK_APP_SECRET`);
  }

  getSessionSecret(): string {
    return this.configService.getOrThrow<string>(`SESSION_SECRET`);
  }

  getDialogSmsApiKey(): string {
    return this.configService.getOrThrow<string>(`DIALOG_SMS_API_KEY`);
  }

  getDialogSmsMask(): string {
    return this.configService.getOrThrow<string>(`DIALOG_SMS_MASK`);
  }
}
