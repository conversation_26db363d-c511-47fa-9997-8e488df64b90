import {
  <PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  <PERSON><PERSON>ptional,
  IsPort,
  IsString,
} from 'class-validator';
import { NodeEnvironment } from '../env';

export class EnvironmentConfig {
  @IsOptional()
  @IsPort()
  PORT = `8080`;

  @IsOptional()
  @IsEnum(NodeEnvironment)
  NODE_ENV: NodeEnvironment = NodeEnvironment.DEV;

  @IsString()
  @IsNotEmpty()
  GOOGLE_CLIENT_ID: string;

  @IsString()
  @IsNotEmpty()
  GOOGLE_CLIENT_SECRET: string;

  @IsString()
  @IsNotEmpty()
  MONGODB_URI: string;

  @IsString()
  @IsNotEmpty()
  JWT_ACCESS_SECRET: string;

  @IsString()
  @IsNotEmpty()
  JWT_REFRESH_SECRET: string;

  @IsString()
  @IsNotEmpty()
  TOKEN_ENCRYPTION_KEY: string;

  @IsString()
  @IsNotEmpty()
  RESEND_API_KEY: string;

  @IsString()
  @IsNotEmpty()
  STRIPE_SECRET_KEY: string;

  @IsString()
  @IsNotEmpty()
  FRONTEND_URL: string;

  @IsString()
  @IsNotEmpty()
  API_URL: string;

  @IsString()
  @IsNotEmpty()
  FACEBOOK_APP_ID: string;

  @IsString()
  @IsNotEmpty()
  FACEBOOK_APP_SECRET: string;

  @IsString()
  @IsNotEmpty()
  SESSION_SECRET: string;

  @IsString()
  @IsNotEmpty()
  DIALOG_SMS_API_KEY: string;

  @IsString()
  @IsNotEmpty()
  DIALOG_SMS_MASK: string;
}
