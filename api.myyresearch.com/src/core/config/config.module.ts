import { Module } from '@nestjs/common';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import * as Joi from 'joi';
import { NodeEnvironment } from 'src/shared/models/env';

@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      validationSchema: Joi.object({
        NODE_ENV: Joi.string()
          .valid(...Object.values(NodeEnvironment))
          .default(NodeEnvironment.DEV),
        PORT: Joi.number().default(8080),
        GOOGLE_CLIENT_ID: Joi.string().required(),
        GOOGLE_CLIENT_SECRET: Joi.string().required(),
        MONGODB_URI: Joi.string().required(),
        JWT_ACCESS_SECRET: Joi.string().required(),
        JWT_REFRESH_SECRET: Joi.string().required(),
        TOKEN_ENCRYPTION_KEY: Joi.string().required(),
        RESEND_API_KEY: Joi.string().required(),
        STRIPE_SECRET_KEY: Joi.string().required(),
        FRONTEND_URL: Joi.string().default('http://localhost:3000'),
        API_URL: Joi.string().default('http://localhost:8080'),
      }),
    }),
  ],
})
export class ConfigModule {}
