import { NestFactory } from '@nestjs/core';
import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
// import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import helmet from 'helmet';
import { ApiResponseInterceptor } from '@core/interceptors/api-response.interceptor';
import { ApiErrorFilter } from '@core/filters/api-error.filter';
import { EnvironmentConfig } from '@shared/models/env';
import { AppModule } from '@app/app.module';
import * as session from 'express-session';
import * as passport from 'passport';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

const bootstrap = async () => {
  const app = await NestFactory.create(AppModule);

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  app.setGlobalPrefix('api');
  app.useGlobalInterceptors(new ApiResponseInterceptor());
  app.useGlobalFilters(new ApiErrorFilter());

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  app.enableCors();
  app.use(helmet());

  // Add session middleware
  app.use(
    session({
      secret: process.env.SESSION_SECRET || 'your-secret-key-1',
      resave: false,
      saveUninitialized: false,
      cookie: {
        maxAge: 60000 * 60 * 24, // 24 hours
        secure: process.env.NODE_ENV === 'prod',
      },
    }),
  );

  // Initialize passport and session
  app.use(passport.initialize());
  app.use(passport.session());

  const config = new DocumentBuilder()
    .setTitle('MyResearch API')
    .setDescription('MyResearch API')
    .setVersion('1.2')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  const configService = app.get(ConfigService<EnvironmentConfig, true>);
  await app.listen(configService.get('PORT'));

  const currentTime = new Date();
  Logger.log(`UTC Offset: ${currentTime.getTimezoneOffset()}`);
  Logger.log(`Port: ${configService.get('PORT')}`);
  Logger.log(`Node environment: ${configService.get('NODE_ENV')}`);
};

bootstrap().catch((reason) => {
  Logger.warn('Failed to start the server');
  Logger.error(reason);
});
