{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@assets/*": ["src/assets/*"], "@core/*": ["src/core/*"], "@config/*": ["src/core/config/*"], "@constants/*": ["src/core/constants/*"], "@decorators/*": ["src/core/decorators/*"], "@filters/*": ["src/core/filters/*"], "@guards/*": ["src/core/guards/*"], "@interceptors/*": ["src/core/interceptors/*"], "@shared/*": ["src/shared/*"], "@features/*": ["src/features/*"], "@app/*": ["src/app/*"]}}}