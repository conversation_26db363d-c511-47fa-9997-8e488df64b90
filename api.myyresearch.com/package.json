{"name": "e9-nest-api-template", "version": "0.0.1", "description": "E9 Nest API Template", "author": "E9", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/mapped-types": "^2.0.6", "@nestjs/mongoose": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^8.0.7", "@nestjs/throttler": "^6.2.1", "@types/express-session": "^1.18.1", "@types/passport-google-oauth20": "^2.0.16", "bcrypt": "^5.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "express-session": "^1.18.1", "handlebars": "^4.7.8", "helmet": "^8.0.0", "joi": "^17.13.3", "jose": "^5.9.6", "moment": "^2.30.1", "mongoose": "^7.0.0", "nanoid": "^3.0.0", "nodemailer": "^6.9.16", "passport": "^0.6.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-session": "^1.0.2", "qrcode": "^1.5.4", "reflect-metadata": "^0.2.0", "resend": "^4.0.1", "rxjs": "^7.8.1", "speakeasy": "^2.0.0", "stripe": "^17.6.0", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-facebook": "^3.0.3", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.35", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "resolutions": {"string-width": "4.2.3", "strip-ansi": "6.0.0"}}