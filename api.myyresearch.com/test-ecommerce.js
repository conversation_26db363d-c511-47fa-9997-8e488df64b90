/**
 * Simple test script to verify e-commerce functionality
 * Run with: node test-ecommerce.js
 * 
 * Prerequisites:
 * 1. Server running on localhost:3000
 * 2. Valid JWT token
 * 3. Valid course/template IDs
 */

const BASE_URL = 'http://localhost:3000';
const JWT_TOKEN = 'your_jwt_token_here'; // Replace with actual token

// Test data - replace with actual IDs from your database
const TEST_COURSE_ID = '507f1f77bcf86cd799439011';
const TEST_TEMPLATE_ID = '507f1f77bcf86cd799439012';

async function makeRequest(endpoint, method = 'GET', body = null) {
  const options = {
    method,
    headers: {
      'Authorization': `Bearer ${JWT_TOKEN}`,
      'Content-Type': 'application/json',
    },
  };

  if (body) {
    options.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const data = await response.json();
    
    console.log(`${method} ${endpoint}`);
    console.log(`Status: ${response.status}`);
    console.log('Response:', JSON.stringify(data, null, 2));
    console.log('---');
    
    return { response, data };
  } catch (error) {
    console.error(`Error with ${method} ${endpoint}:`, error.message);
    return { error };
  }
}

async function testECommerceFlow() {
  console.log('🛒 Testing E-Commerce System\n');

  // 1. Get initial cart
  console.log('1. Getting initial cart...');
  await makeRequest('/cart');

  // 2. Add course to cart
  console.log('2. Adding course to cart...');
  await makeRequest('/cart/add', 'POST', {
    itemType: 'course',
    itemId: TEST_COURSE_ID,
    quantity: 1
  });

  // 3. Add template to cart
  console.log('3. Adding template to cart...');
  await makeRequest('/cart/add', 'POST', {
    itemType: 'template',
    itemId: TEST_TEMPLATE_ID,
    quantity: 1
  });

  // 4. Get updated cart
  console.log('4. Getting updated cart...');
  const { data: cartData } = await makeRequest('/cart');

  // 5. Create order from cart
  console.log('5. Creating order from cart...');
  const { data: orderData } = await makeRequest('/orders/create', 'POST', {
    metadata: {
      source: 'test_script',
      timestamp: new Date().toISOString()
    }
  });

  if (orderData && orderData.order) {
    const orderId = orderData.order._id;

    // 6. Create payment intent
    console.log('6. Creating payment intent...');
    await makeRequest('/payments/create-intent', 'POST', {
      orderId: orderId,
      metadata: {
        test: true
      }
    });

    // 7. Get order details
    console.log('7. Getting order details...');
    await makeRequest(`/orders/${orderId}`);
  }

  // 8. Get user orders
  console.log('8. Getting user orders...');
  await makeRequest('/orders?page=1&limit=5');

  // 9. Clear cart
  console.log('9. Clearing cart...');
  await makeRequest('/cart/clear', 'DELETE');

  console.log('✅ E-Commerce test completed!');
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.error('❌ This script requires Node.js 18+ or install node-fetch');
  console.log('Install node-fetch: npm install node-fetch');
  console.log('Or use Node.js 18+');
  process.exit(1);
}

// Check if JWT token is set
if (JWT_TOKEN === 'your_jwt_token_here') {
  console.error('❌ Please set a valid JWT_TOKEN in the script');
  console.log('1. Login to get a JWT token');
  console.log('2. Replace JWT_TOKEN variable in this script');
  process.exit(1);
}

// Run the test
testECommerceFlow().catch(console.error);
