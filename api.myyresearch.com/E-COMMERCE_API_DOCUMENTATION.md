# E-Commerce System API Documentation

This document describes the complete e-commerce system implementation for courses and templates with Stripe payment integration.

## Overview

The e-commerce system consists of three main modules:
- **Cart Module**: Shopping cart functionality with cookie-based cart ID storage
- **Orders Module**: Order management with cartId-based order creation
- **Payments Module**: Stripe payment processing

## Key Features

- **Universal Cart System**: Works for both authenticated and unauthenticated users
- **Cookie-based Cart Storage**: Cart persistence through browser cookies using cartId
- **Seamless User Experience**: Cart operations work independently of authentication status
- **Secure Order Processing**: Orders require authentication but use cartId for cart lookup

## Environment Variables

Make sure to set the following environment variables:

```bash
STRIPE_SECRET_KEY=sk_test_...  # Your Stripe secret key
STRIPE_PUBLISHABLE_KEY=pk_test_...  # Your Stripe publishable key (for frontend)
```

## Cart ID Management

### Overview
The cart system uses a UUID v4-based `cartId` for cart identification instead of user authentication. This enables:
- **Guest shopping**: Users can add items to cart without creating an account
- **Cart persistence**: Cart contents persist across browser sessions via cookies
- **Seamless checkout**: Users can authenticate at checkout time while preserving their cart

### Implementation Guidelines

#### Frontend Cart ID Management
```javascript
// Generate UUID v4
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Cookie management
function setCookie(name, value, days) {
  const expires = new Date(Date.now() + days * 864e5).toUTCString();
  document.cookie = name + '=' + encodeURIComponent(value) + '; expires=' + expires + '; path=/';
}

function getCookie(name) {
  return document.cookie.split('; ').reduce((r, v) => {
    const parts = v.split('=');
    return parts[0] === name ? decodeURIComponent(parts[1]) : r;
  }, '');
}
```

#### Best Practices
1. **Generate cartId on first cart interaction**: Create UUID when user first adds item to cart
2. **Store in both localStorage and cookies**: Ensures persistence across sessions and tabs
3. **30-day expiry**: Set reasonable expiry for cart persistence
4. **Clear after purchase**: Remove cartId after successful order completion
5. **Validate UUID format**: Always validate cartId format on backend

## API Endpoints

### Cart Management

**Note**: All cart operations require a `cartId` parameter. The cartId should be stored in browser cookies and generated as a UUID v4. Cart operations work for both authenticated and unauthenticated users.

#### Get Cart
```http
GET /cart?cartId=550e8400-e29b-41d4-a716-************
```

#### Add Item to Cart
```http
POST /cart/add
Content-Type: application/json

{
  "cartId": "550e8400-e29b-41d4-a716-************",
  "itemType": "course",  // or "template"
  "itemId": "507f1f77bcf86cd799439011",
  "quantity": 1
}
```

#### Update Cart Item
```http
PUT /cart/update
Content-Type: application/json

{
  "cartId": "550e8400-e29b-41d4-a716-************",
  "itemId": "cart_item_id",
  "quantity": 2
}
```

#### Remove Item from Cart
```http
DELETE /cart/remove
Content-Type: application/json

{
  "cartId": "550e8400-e29b-41d4-a716-************",
  "itemId": "cart_item_id"
}
```

#### Clear Cart
```http
DELETE /cart/clear
Content-Type: application/json

{
  "cartId": "550e8400-e29b-41d4-a716-************"
}
```

### Order Management

#### Create Order from Cart
```http
POST /orders/create
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "cartId": "550e8400-e29b-41d4-a716-************",
  "metadata": {
    "source": "web",
    "campaign": "summer_sale"
  }
}
```

**Note**: Order creation requires authentication (JWT token) and a valid cartId. The cart will be automatically cleared after successful order creation.

#### Get User Orders
```http
GET /orders?page=1&limit=10&status=completed
Authorization: Bearer <jwt_token>
```

Query Parameters:
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by order status
- `paymentStatus` (optional): Filter by payment status
- `from` (optional): Filter orders from date (YYYY-MM-DD)
- `to` (optional): Filter orders to date (YYYY-MM-DD)

#### Get Specific Order
```http
GET /orders/{orderId}
Authorization: Bearer <jwt_token>
```

### Payment Processing

#### Create Payment Intent
```http
POST /payments/create-intent
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "orderId": "507f1f77bcf86cd799439011",
  "metadata": {
    "source": "web"
  }
}
```

Response:
```json
{
  "success": true,
  "message": "Payment intent created successfully",
  "clientSecret": "pi_1234567890_secret_abcdefghijk",
  "paymentIntentId": "pi_1234567890",
  "amount": 9999,
  "currency": "usd"
}
```

#### Confirm Payment
```http
POST /payments/confirm
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "paymentIntentId": "pi_1234567890",
  "metadata": {
    "confirmationSource": "web"
  }
}
```

## Frontend Integration

### Basic Shopping Flow

1. **Generate cartId**: Create a UUID v4 and store it in browser cookies
2. **Add items to cart**: Use `POST /cart/add` with cartId to add courses/templates
3. **Review cart**: Use `GET /cart` with cartId to display cart contents
4. **User authentication**: Require user to login before checkout
5. **Create order**: Use `POST /orders/create` with cartId to convert cart to order (cart is automatically cleared)
6. **Create payment intent**: Use `POST /payments/create-intent` to get Stripe client secret
7. **Process payment**: Use Stripe.js on frontend with the client secret
8. **Confirm payment**: Use `POST /payments/confirm` to complete the purchase and create enrollments

### Frontend Integration with Cart Management

```javascript
// Cart ID management
function getOrCreateCartId() {
  let cartId = localStorage.getItem('cartId') || getCookie('cartId');
  if (!cartId) {
    cartId = generateUUID(); // Generate UUID v4
    localStorage.setItem('cartId', cartId);
    setCookie('cartId', cartId, 30); // 30 days expiry
  }
  return cartId;
}

// Add item to cart (works for both authenticated and unauthenticated users)
async function addToCart(itemType, itemId, quantity = 1) {
  const cartId = getOrCreateCartId();

  const response = await fetch('/cart/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      cartId,
      itemType,
      itemId,
      quantity
    })
  });

  return response.json();
}

// Get cart contents
async function getCart() {
  const cartId = getOrCreateCartId();

  const response = await fetch(`/cart?cartId=${cartId}`);
  return response.json();
}

// Create order (requires authentication)
async function createOrder(token, metadata = {}) {
  const cartId = getOrCreateCartId();

  const response = await fetch('/orders/create', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      cartId,
      metadata
    })
  });

  return response.json();
}

// Complete Stripe payment flow
async function processPayment(token, orderId) {
  // Initialize Stripe
  const stripe = Stripe('pk_test_...');

  // Create payment intent and get client secret
  const response = await fetch('/payments/create-intent', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ orderId })
  });

  const { clientSecret } = await response.json();

  // Confirm payment with Stripe
  const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
    payment_method: {
      card: cardElement,
      billing_details: {
        name: 'Customer Name',
        email: '<EMAIL>'
      }
    }
  });

  if (!error && paymentIntent.status === 'succeeded') {
    // Confirm payment on backend
    await fetch('/payments/confirm', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        paymentIntentId: paymentIntent.id
      })
    });

    // Clear cart ID after successful purchase
    localStorage.removeItem('cartId');
    deleteCookie('cartId');
  }
}
```

## Database Schemas

### Cart Schema
- **cartId**: Required UUID v4 for cart identification (no user association)
- **items**: Cart items with course/template references
- **totalAmount**: Automatically calculated total
- **totalItems**: Automatically calculated item count
- **documentStatus**: Active/archived status
- **lastUpdated**: Timestamp of last modification
- **timestamps**: Created/updated timestamps

### Order Schema
- Unique order numbers
- Order items with pricing details
- Order status tracking
- Payment status tracking
- Stripe integration fields

### Payment Schema
- Stripe payment intent tracking
- Payment status management
- Receipt and refund handling
- Comprehensive metadata support

## Features

### Cart Features
- ✅ **Universal cart system**: Works for both authenticated and unauthenticated users
- ✅ **Cookie-based persistence**: Cart ID stored in browser cookies
- ✅ **Add courses and templates**: Add items to cart with cartId
- ✅ **Update item quantities**: Modify quantities using cartId
- ✅ **Remove items from cart**: Remove specific items using cartId
- ✅ **Clear entire cart**: Clear all items using cartId
- ✅ **Automatic total calculation**: Dynamic calculation of totals
- ✅ **Independent operation**: Cart operations work without user authentication

### Order Features
- ✅ **CartId-based order creation**: Create orders using cartId (requires authentication)
- ✅ **Automatic cart clearing**: Cart is cleared after successful order creation
- ✅ **Unique order number generation**: Nanoid-based order numbers
- ✅ **Order status tracking**: Pending, processing, completed, cancelled, refunded
- ✅ **Payment status tracking**: Pending, processing, completed, failed, refunded
- ✅ **Order history with filtering**: Filter by status, payment status, date range
- ✅ **Comprehensive order details**: Full order information with items and pricing

### Payment Features
- ✅ **Stripe payment intent creation**: Secure payment intent generation
- ✅ **Secure payment processing**: Full Stripe integration with webhooks ready
- ✅ **Automatic enrollment creation**: Course/template enrollments created after payment
- ✅ **Optimized cart handling**: Cart cleared during order creation (not payment)
- ✅ **Payment status tracking**: Comprehensive payment state management
- ✅ **Customer management**: Stripe customer creation and management
- ✅ **Receipt handling**: Payment confirmation and receipt management

### Security Features
- ✅ **Flexible authentication**: Cart operations work without auth, orders require JWT
- ✅ **CartId validation**: UUID v4 validation for all cart operations
- ✅ **User ownership validation**: Orders are always associated with authenticated users
- ✅ **Stripe webhook support ready**: Prepared for webhook integration
- ✅ **Comprehensive error handling**: Detailed error responses with appropriate status codes
- ✅ **Input validation**: Strict validation for all API inputs

## Error Handling

The API provides comprehensive error handling with appropriate HTTP status codes:

- `400 Bad Request`: Invalid input data
- `401 Unauthorized`: Missing or invalid authentication
- `404 Not Found`: Resource not found
- `409 Conflict`: Business logic conflicts (e.g., already owns item)
- `500 Internal Server Error`: Server-side errors

## Testing

To test the implementation:

1. **Start the server**: `npm run start:dev`
2. **Test cart operations without authentication**:
   - Generate a UUID v4 for cartId
   - Add items to cart using the cartId
   - Verify cart persistence and operations
3. **Test authenticated order flow**:
   - Create an order using cartId (requires JWT token)
   - Verify cart is cleared after order creation
   - Process payment and verify enrollments
4. **Test complete user journey**:
   - Guest adds items to cart (no auth required)
   - User registers/logs in at checkout
   - Order created with existing cartId
   - Payment processed successfully

### Example Test Flow
```bash
# 1. Add item to cart (no auth)
curl -X POST http://localhost:3000/cart/add \
  -H "Content-Type: application/json" \
  -d '{
    "cartId": "550e8400-e29b-41d4-a716-************",
    "itemType": "course",
    "itemId": "507f1f77bcf86cd799439011",
    "quantity": 1
  }'

# 2. Get cart contents
curl "http://localhost:3000/cart?cartId=550e8400-e29b-41d4-a716-************"

# 3. Create order (requires auth)
curl -X POST http://localhost:3000/orders/create \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "cartId": "550e8400-e29b-41d4-a716-************",
    "metadata": {"source": "test"}
  }'
```

## Next Steps

1. **Webhook Implementation**: Add Stripe webhook handling for payment events
2. **Refund System**: Implement refund processing
3. **Discount System**: Add coupon and discount functionality
4. **Inventory Management**: Add stock tracking for limited items
5. **Analytics**: Add purchase analytics and reporting
6. **Email Notifications**: Send order confirmations and receipts
