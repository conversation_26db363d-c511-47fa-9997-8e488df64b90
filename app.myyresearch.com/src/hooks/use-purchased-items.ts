"use client";

import { useQuery } from "@tanstack/react-query";
import {
  getAdminPurchasedItems,
  getAdminPurchasedTemplates,
  getAdminPurchasedCourses,
} from "@/lib/purchased-items";
import {
  PurchasedItemsFilterParams,
  ItemType,
  GetPurchasedItemsResponse,
} from "@/types/purchased-items";

// Query keys for purchased items
export const purchasedItemsKeys = {
  all: ["purchased-items"] as const,
  lists: () => [...purchasedItemsKeys.all, "list"] as const,
  list: (filters: PurchasedItemsFilterParams) =>
    [...purchasedItemsKeys.lists(), { filters }] as const,
  templates: (filters: Omit<PurchasedItemsFilterParams, 'itemType'>) =>
    [...purchasedItemsKeys.all, "templates", { filters }] as const,
  courses: (filters: Omit<PurchasedItemsFilterParams, 'itemType'>) =>
    [...purchasedItemsKeys.all, "courses", { filters }] as const,
};

/**
 * Hook to get all purchased items (Admin only)
 */
export function useAdminPurchasedItems(filters: PurchasedItemsFilterParams) {
  return useQuery({
    queryKey: purchasedItemsKeys.list(filters),
    queryFn: () => getAdminPurchasedItems(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true, // Admin access is handled by the API
  });
}

/**
 * Hook to get purchased templates (Admin only)
 */
export function useAdminPurchasedTemplates(
  filters: Omit<PurchasedItemsFilterParams, 'itemType'>
) {
  return useQuery({
    queryKey: purchasedItemsKeys.templates(filters),
    queryFn: () => getAdminPurchasedTemplates(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true,
  });
}

/**
 * Hook to get purchased courses (Admin only)
 */
export function useAdminPurchasedCourses(
  filters: Omit<PurchasedItemsFilterParams, 'itemType'>
) {
  return useQuery({
    queryKey: purchasedItemsKeys.courses(filters),
    queryFn: () => getAdminPurchasedCourses(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true,
  });
}
