"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getAdminOrders,
  getAdminOrderById,
  createOrder,
  getUserOrders,
  getOrderById,
  cancelOrder,
  updateOrderStatus,
} from "@/lib/orders";
import {
  AdminOrderFilter,
  OrderFilter,
  CreateOrderRequest,
  Order,
} from "@/types/orders";

// Query keys for orders
export const ordersKeys = {
  all: ["orders"] as const,
  lists: () => [...ordersKeys.all, "list"] as const,
  list: (filters: AdminOrderFilter) =>
    [...ordersKeys.lists(), { filters }] as const,
  details: () => [...ordersKeys.all, "detail"] as const,
  detail: (id: string) => [...ordersKeys.details(), id] as const,
  userOrders: (filters: OrderFilter) =>
    [...ordersKeys.all, "user", { filters }] as const,
};

/**
 * Hook to get all orders (Admin only)
 */
export function useAdminOrders(filters: AdminOrderFilter) {
  return useQuery({
    queryKey: ordersKeys.list(filters),
    queryFn: () => getAdminOrders(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true, // Admin access is handled by the API
  });
}

/**
 * Hook to get a specific order by ID (Admin only)
 */
export function useAdminOrderById(orderId: string) {
  return useQuery({
    queryKey: ordersKeys.detail(orderId),
    queryFn: () => getAdminOrderById(orderId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: !!orderId,
  });
}

/**
 * Hook to get user's orders
 */
export function useUserOrders(filters: OrderFilter) {
  return useQuery({
    queryKey: ordersKeys.userOrders(filters),
    queryFn: () => getUserOrders(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true,
  });
}

/**
 * Hook to get a specific order by ID
 */
export function useOrderById(orderId: string) {
  return useQuery({
    queryKey: ordersKeys.detail(orderId),
    queryFn: () => getOrderById(orderId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: !!orderId,
  });
}

/**
 * Hook to create a new order
 */
export function useCreateOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOrderRequest) => createOrder(data),
    onSuccess: () => {
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: ordersKeys.all });
    },
  });
}

/**
 * Hook to cancel an order
 */
export function useCancelOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, reason }: { orderId: string; reason?: string }) =>
      cancelOrder(orderId, reason),
    onSuccess: (data, variables) => {
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: ordersKeys.all });
      queryClient.invalidateQueries({ 
        queryKey: ordersKeys.detail(variables.orderId) 
      });
    },
  });
}

/**
 * Hook to update order status (Admin only)
 */
export function useUpdateOrderStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, status }: { orderId: string; status: string }) =>
      updateOrderStatus(orderId, status),
    onSuccess: (data, variables) => {
      // Invalidate and refetch orders
      queryClient.invalidateQueries({ queryKey: ordersKeys.all });
      queryClient.invalidateQueries({ 
        queryKey: ordersKeys.detail(variables.orderId) 
      });
    },
  });
}
