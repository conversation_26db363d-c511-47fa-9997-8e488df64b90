"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getAdminPayments,
  getAdminPaymentById,
  createPaymentIntent,
  confirmPayment,
  getUserPayments,
  getPaymentById,
  refundPayment,
} from "@/lib/payments";
import {
  AdminPaymentFilter,
  PaymentFilter,
  CreatePaymentIntentRequest,
  ConfirmPaymentRequest,
} from "@/types/payments";

// Query keys for payments
export const paymentsKeys = {
  all: ["payments"] as const,
  lists: () => [...paymentsKeys.all, "list"] as const,
  list: (filters: AdminPaymentFilter) =>
    [...paymentsKeys.lists(), { filters }] as const,
  details: () => [...paymentsKeys.all, "detail"] as const,
  detail: (id: string) => [...paymentsKeys.details(), id] as const,
  userPayments: (filters: PaymentFilter) =>
    [...paymentsKeys.all, "user", { filters }] as const,
};

/**
 * Hook to get all payments (Admin only)
 */
export function useAdminPayments(filters: AdminPaymentFilter) {
  return useQuery({
    queryKey: paymentsKeys.list(filters),
    queryFn: () => getAdminPayments(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true, // Admin access is handled by the API
  });
}

/**
 * Hook to get a specific payment by ID (Admin only)
 */
export function useAdminPaymentById(paymentId: string) {
  return useQuery({
    queryKey: paymentsKeys.detail(paymentId),
    queryFn: () => getAdminPaymentById(paymentId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: !!paymentId,
  });
}

/**
 * Hook to get user's payments
 */
export function useUserPayments(filters: PaymentFilter) {
  return useQuery({
    queryKey: paymentsKeys.userPayments(filters),
    queryFn: () => getUserPayments(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: true,
  });
}

/**
 * Hook to get a specific payment by ID
 */
export function usePaymentById(paymentId: string) {
  return useQuery({
    queryKey: paymentsKeys.detail(paymentId),
    queryFn: () => getPaymentById(paymentId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: !!paymentId,
  });
}

/**
 * Hook to create payment intent
 */
export function useCreatePaymentIntent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePaymentIntentRequest) => createPaymentIntent(data),
    onSuccess: () => {
      // Invalidate and refetch payments
      queryClient.invalidateQueries({ queryKey: paymentsKeys.all });
    },
  });
}

/**
 * Hook to confirm payment
 */
export function useConfirmPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ConfirmPaymentRequest) => confirmPayment(data),
    onSuccess: () => {
      // Invalidate and refetch payments and orders
      queryClient.invalidateQueries({ queryKey: paymentsKeys.all });
      queryClient.invalidateQueries({ queryKey: ["orders"] });
    },
  });
}

/**
 * Hook to refund payment (Admin only)
 */
export function useRefundPayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      paymentId, 
      amount, 
      reason 
    }: { 
      paymentId: string; 
      amount?: number; 
      reason?: string; 
    }) => refundPayment(paymentId, amount, reason),
    onSuccess: (data, variables) => {
      // Invalidate and refetch payments
      queryClient.invalidateQueries({ queryKey: paymentsKeys.all });
      queryClient.invalidateQueries({ 
        queryKey: paymentsKeys.detail(variables.paymentId) 
      });
      // Also invalidate orders as refunds affect order status
      queryClient.invalidateQueries({ queryKey: ["orders"] });
    },
  });
}
