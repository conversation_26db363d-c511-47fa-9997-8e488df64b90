"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  submitContactForm,
  getAdminContactSubmissions,
  getContactStatusCounts,
  ContactFormData,
  ContactSubmissionResponse,
  ContactSubmissionsFilter,
  GetContactSubmissionsResponse,
  ContactStatusCounts,
} from "@/lib/contact-us";

// Query keys for contact submissions
export const contactKeys = {
  all: ["contact-submissions"] as const,
  lists: () => [...contactKeys.all, "list"] as const,
  list: (filter?: ContactSubmissionsFilter) =>
    [...contactKeys.lists(), filter] as const,
  statusCounts: () => [...contactKeys.all, "status-counts"] as const,
};

/**
 * Hook to submit contact form with optimistic updates
 */
export function useSubmitContactForm() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ContactFormData) => submitContactForm(data),
    onSuccess: (data) => {
      // Invalidate admin contact submissions list to show the new submission
      queryClient.invalidateQueries({ queryKey: contactKeys.lists() });
      
      // Invalidate status counts to update the counts
      queryClient.invalidateQueries({ queryKey: contactKeys.statusCounts() });
    },
    onError: (error) => {
      console.error("Error submitting contact form:", error);
    },
  });
}

/**
 * Hook to get admin contact submissions with filtering
 */
export function useAdminContactSubmissions(filter?: ContactSubmissionsFilter) {
  return useQuery({
    queryKey: contactKeys.list(filter),
    queryFn: () => getAdminContactSubmissions(filter || {}),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: false,
    enabled: true, // Admin access is handled by the API
  });
}

/**
 * Hook to get contact submission status counts
 */
export function useContactStatusCounts() {
  return useQuery({
    queryKey: contactKeys.statusCounts(),
    queryFn: () => getContactStatusCounts(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}
