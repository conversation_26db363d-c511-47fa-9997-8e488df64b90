"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  createInstructorPaymentDetails,
  updateInstructorPaymentDetails,
  getInstructorPaymentDetails,
  getInstructorBalance,
  getInstructorPaymentHistory,
  createPaymentRequest,
  getInstructorPaymentRequests,
  CreateInstructorPaymentDetailsRequest,
  UpdateInstructorPaymentDetailsRequest,
  CreatePaymentRequestRequest,
} from "@/lib/instructor-payments copy";

// Query keys for instructor payment operations
export const instructorPaymentKeys = {
  all: ["instructor-payments"] as const,
  lists: () => [...instructorPaymentKeys.all, "list"] as const,
  details: () => [...instructorPaymentKeys.all, "detail"] as const,
  paymentDetails: () =>
    [...instructorPaymentKeys.all, "payment-details"] as const,
  balance: () => [...instructorPaymentKeys.all, "balance"] as const,
  paymentHistory: () =>
    [...instructorPaymentKeys.all, "payment-history"] as const,
  paymentRequests: () =>
    [...instructorPaymentKeys.all, "payment-requests"] as const,
} as const;

/**
 * Hook to get instructor payment details
 */
export function useInstructorPaymentDetails() {
  return useQuery({
    queryKey: instructorPaymentKeys.paymentDetails(),
    queryFn: () => getInstructorPaymentDetails(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to create instructor payment details
 */
export function useCreateInstructorPaymentDetails() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateInstructorPaymentDetailsRequest) =>
      createInstructorPaymentDetails(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.paymentDetails(),
      });
    },
  });
}

/**
 * Hook to update instructor payment details
 */
export function useUpdateInstructorPaymentDetails() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateInstructorPaymentDetailsRequest) =>
      updateInstructorPaymentDetails(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.paymentDetails(),
      });
    },
  });
}

/**
 * Hook to get instructor balance
 */
export function useInstructorBalance() {
  return useQuery({
    queryKey: instructorPaymentKeys.balance(),
    queryFn: () => getInstructorBalance(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get instructor payment history
 */
export function useInstructorPaymentHistory() {
  return useQuery({
    queryKey: instructorPaymentKeys.paymentHistory(),
    queryFn: () => getInstructorPaymentHistory(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get instructor payment requests
 */
export function useInstructorPaymentRequests() {
  return useQuery({
    queryKey: instructorPaymentKeys.paymentRequests(),
    queryFn: () => getInstructorPaymentRequests(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to create a payment request
 */
export function useCreatePaymentRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePaymentRequestRequest) =>
      createPaymentRequest(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.paymentRequests(),
      });
      queryClient.invalidateQueries({
        queryKey: instructorPaymentKeys.balance(),
      });
    },
  });
}
