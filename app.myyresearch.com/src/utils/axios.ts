import axios from "axios";

import { type ApiResponse } from "@/types/common";
import { getSession } from "@/lib/authenticaton";

const axiosService = () => {
  const defaultOptions = {
    baseURL: process.env.NEXT_PUBLIC_API_URL + "/api/v1",
    timeout: 10000,
  };

  const instance = axios.create(defaultOptions);

  instance.interceptors.request.use(async (request) => {
    try {
      const session = await getSession();
      if (session) {
        request.headers.Authorization = `Bearer ${session.user.token}`;
      }
    } catch (error) {
      console.error("Error getting session:", error);
    }
    return request;
  });

  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      return Promise.reject(error.response?.data as ApiResponse<unknown>);
    }
  );

  return instance;
};

export default axiosService();
