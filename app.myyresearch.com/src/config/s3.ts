/**
 * AWS Configuration utility to map custom MYYRESEARCH_ environment variables
 * to AWS SDK configuration format
 */
export const awsConfig = {
  region: process.env.MYYRESEARCH_AWS_REGION!,
  credentials: {
    accessKeyId: process.env.MYYRESEARCH_AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.MYYRESEARCH_AWS_SECRET_ACCESS_KEY!,
  },
};

export const s3Config = {
  cloudfrontUrl: "https://d21gn4dq5wyf31.cloudfront.net",
  buckets: {
    files: {
      name: process.env.AWS_FILES_BUCKET_NAME!,
      cloudfrontUrl: process.env.AWS_FILES_CLOUDFRONT_URL!,
    },
    images: {
      name: process.env.AWS_IMAGES_BUCKET_NAME!,
      cloudfrontUrl: process.env.AWS_IMAGES_CLOUDFRONT_URL!,
    },
    videos: {
      name: process.env.AWS_VIDEOS_BUCKET_NAME!,
      cloudfrontUrl: process.env.AWS_VIDEOS_CLOUDFRONT_URL!,
    },
  },
};
