"use client";

import React, { useRef, useState, useEffect } from "react";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Maximize,
  Minimize,
  SkipBack,
  SkipForward,
  Settings,
  Subtitles,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Slider } from "@/components/ui/slider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

interface Quality {
  label: string;
  src: string;
}

interface Caption {
  label: string;
  src: string;
}

interface VideoPlayerProps {
  sources: Quality[];
  captions?: Caption[];
  className?: string;
}

export function VideoPlayer({
  sources,
  captions = [],
  className,
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentQuality, setCurrentQuality] = useState<Quality>(
    sources[sources.length - 1]
  );
  const [currentCaption, setCurrentCaption] = useState<Caption | null>(null);
  const [showCaptions, setShowCaptions] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);

  const playbackSpeeds = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const updateTime = () => setCurrentTime(video.currentTime);
    const updateDuration = () => setDuration(video.duration);

    video.addEventListener("timeupdate", updateTime);
    video.addEventListener("loadedmetadata", updateDuration);

    return () => {
      video.removeEventListener("timeupdate", updateTime);
      video.removeEventListener("loadedmetadata", updateDuration);
    };
  }, []);

  const changeQuality = (quality: Quality) => {
    if (videoRef.current) {
      const currentTime = videoRef.current.currentTime;
      const wasPlaying = !videoRef.current.paused;
      setCurrentQuality(quality);

      videoRef.current.addEventListener("loadedmetadata", function onLoad() {
        videoRef.current!.currentTime = currentTime;
        if (wasPlaying) {
          videoRef.current!.play();
        }
        videoRef.current!.removeEventListener("loadedmetadata", onLoad);
      });
    }
  };

  const toggleCaptions = () => {
    setShowCaptions(!showCaptions);
    if (videoRef.current) {
      const tracks = videoRef.current.textTracks;
      for (let i = 0; i < tracks.length; i++) {
        tracks[i].mode = !showCaptions ? "showing" : "hidden";
      }
    }
  };

  const changeCaption = (caption: Caption | null) => {
    setCurrentCaption(caption);
    if (videoRef.current) {
      const tracks = videoRef.current.textTracks;
      for (let i = 0; i < tracks.length; i++) {
        tracks[i].mode = "hidden";
      }
      if (caption) {
        const track = Array.from(tracks).find(
          (track) => track.label === caption.label
        );
        if (track) {
          track.mode = "showing";
        }
      }
    }
  };

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeChange = (value: number[]) => {
    if (videoRef.current) {
      videoRef.current.currentTime = value[0];
      setCurrentTime(value[0]);
    }
  };

  const handleVolumeChange = (value: number[]) => {
    if (videoRef.current) {
      const newVolume = value[0];
      videoRef.current.volume = newVolume;
      setVolume(newVolume);
      setIsMuted(newVolume === 0);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      const newMuted = !isMuted;
      videoRef.current.muted = newMuted;
      setIsMuted(newMuted);
      if (newMuted) {
        videoRef.current.volume = 0;
        setVolume(0);
      } else {
        videoRef.current.volume = 1;
        setVolume(1);
      }
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      videoRef.current?.parentElement?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const skip = (seconds: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime += seconds;
    }
  };

  const changePlaybackSpeed = (speed: number) => {
    if (videoRef.current) {
      videoRef.current.playbackRate = speed;
      setPlaybackSpeed(speed);
    }
  };

  return (
    <div
      className={cn("relative group w-full aspect-video bg-black", className)}
    >
      <video
        ref={videoRef}
        src={currentQuality.src}
        className="w-full h-full"
        onClick={togglePlay}
      >
        {captions.map((caption) => (
          <track
            key={caption.label}
            kind="subtitles"
            src={caption.src}
            srcLang={caption.label.toLowerCase()}
            label={caption.label}
            default={caption === currentCaption}
          />
        ))}
      </video>

      {/* Video Controls */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="space-y-2">
          {/* Progress bar */}
          <Slider
            value={[currentTime]}
            min={0}
            max={duration || 100}
            step={1}
            onValueChange={handleTimeChange}
            className="w-full"
          />

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {/* Play/Pause */}
              <button
                onClick={togglePlay}
                className="p-1.5 hover:bg-white/20 rounded-full transition"
              >
                {isPlaying ? (
                  <Pause className="w-5 h-5 text-white" />
                ) : (
                  <Play className="w-5 h-5 text-white" />
                )}
              </button>

              {/* Skip buttons */}
              <button
                onClick={() => skip(-10)}
                className="p-1.5 hover:bg-white/20 rounded-full transition"
              >
                <SkipBack className="w-5 h-5 text-white" />
              </button>
              <button
                onClick={() => skip(10)}
                className="p-1.5 hover:bg-white/20 rounded-full transition"
              >
                <SkipForward className="w-5 h-5 text-white" />
              </button>

              {/* Volume control */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={toggleMute}
                  className="p-1.5 hover:bg-white/20 rounded-full transition"
                >
                  {isMuted || volume === 0 ? (
                    <VolumeX className="w-5 h-5 text-white" />
                  ) : (
                    <Volume2 className="w-5 h-5 text-white" />
                  )}
                </button>
                <Slider
                  value={[volume]}
                  min={0}
                  max={1}
                  step={0.1}
                  onValueChange={handleVolumeChange}
                  className="w-20"
                />
              </div>

              {/* Time display */}
              <span className="text-white text-sm">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              {/* Captions toggle */}
              {captions.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button
                      className={cn(
                        "p-1.5 hover:bg-white/20 rounded-full transition",
                        showCaptions && "bg-white/20"
                      )}
                    >
                      <Subtitles className="w-5 h-5 text-white" />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-32">
                    <DropdownMenuItem
                      onClick={() => changeCaption(null)}
                      className={cn("text-sm", !currentCaption && "font-bold")}
                    >
                      Off
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {captions.map((caption) => (
                      <DropdownMenuItem
                        key={caption.label}
                        onClick={() => changeCaption(caption)}
                        className={cn(
                          "text-sm",
                          currentCaption?.label === caption.label && "font-bold"
                        )}
                      >
                        {caption.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {/* Quality selector */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="p-1.5 hover:bg-white/20 rounded-full transition flex items-center space-x-1">
                    <Settings className="w-5 h-5 text-white" />
                    <span className="text-white text-sm">
                      {currentQuality.label}
                    </span>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-24">
                  {sources.map((quality) => (
                    <DropdownMenuItem
                      key={quality.label}
                      onClick={() => changeQuality(quality)}
                      className={cn(
                        "text-sm",
                        currentQuality.label === quality.label && "font-bold"
                      )}
                    >
                      {quality.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Speed Control */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="p-1.5 hover:bg-white/20 rounded-full transition">
                    <span className="text-white text-sm font-medium">
                      {playbackSpeed}x
                    </span>
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-24">
                  {playbackSpeeds.map((speed) => (
                    <DropdownMenuItem
                      key={speed}
                      onClick={() => changePlaybackSpeed(speed)}
                      className={cn(
                        "text-sm",
                        playbackSpeed === speed && "font-bold"
                      )}
                    >
                      {speed}x
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Fullscreen toggle */}
              <button
                onClick={toggleFullscreen}
                className="p-1.5 hover:bg-white/20 rounded-full transition"
              >
                {isFullscreen ? (
                  <Minimize className="w-5 h-5 text-white" />
                ) : (
                  <Maximize className="w-5 h-5 text-white" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
