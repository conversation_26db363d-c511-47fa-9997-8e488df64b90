"use client";

import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { VideoIcon, Upload, X } from "lucide-react";
import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { UploadedFile } from "@/types/common";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/hooks/use-toast";
import { getS3UploadParams } from "@/lib/s3/action";
import { s3Config } from "@/config/s3";
import { VideoPlayer } from "@/components/video-player";
import { Progress } from "@/components/ui/progress";

interface VideoUploaderProps {
  uploadedVideo: UploadedFile | null;
  onVideoChange: (video: UploadedFile | null) => void;
  onServerUpdate?: (video: UploadedFile | null) => Promise<void>;
  className?: string;
}

async function uploadFileWithProgress(
  file: File,
  onProgress: (progress: number) => void
): Promise<{ url: string; key: string } | { error: string }> {
  try {
    const { url, fields, cloudfrontUrl } = await getS3UploadParams(
      file.name,
      file.type,
      file.size,
      "videos"
    );

    if (!url || !fields) {
      throw new Error("Failed to get upload parameters");
    }

    const formData = new FormData();
    Object.entries(fields).forEach(([key, value]) => {
      formData.append(key, value as string);
    });
    formData.append("file", file);

    const xhr = new XMLHttpRequest();

    const uploadPromise = new Promise<void>((resolve, reject) => {
      xhr.upload.addEventListener("progress", (event) => {
        if (event.lengthComputable) {
          const percentComplete = (event.loaded / event.total) * 100;
          onProgress(percentComplete);
        }
      });

      xhr.addEventListener("load", () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve();
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });

      xhr.addEventListener("error", () => {
        reject(new Error("Upload failed"));
      });

      xhr.open("POST", url, true);
      xhr.send(formData);
    });

    await uploadPromise;

    return {
      url: `${cloudfrontUrl}/${fields.key}`,
      key: fields.key,
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to upload file",
    };
  }
}

export function VideoUploader({
  uploadedVideo,
  onVideoChange,
  onServerUpdate,
  className,
}: VideoUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;

      setIsUploading(true);
      setUploadError(null);
      setUploadProgress(0);

      const handleUpload = async (file: File) => {
        try {
          const result = await uploadFileWithProgress(file, (progress) => {
            setUploadProgress(progress);
          });

          if ("error" in result) {
            throw new Error(result.error);
          }

          const uploadedFile: UploadedFile = {
            name: file.name,
            key: result.key,
            type: file.type,
            size: file.size,
          };

          onVideoChange(uploadedFile);

          if (onServerUpdate) {
            await onServerUpdate(uploadedFile);
          }

          setIsUploading(false);
          setUploadError(null);
          setUploadProgress(0);
        } catch (error) {
          setUploadError(
            error instanceof Error ? error.message : "Failed to upload video"
          );
          setIsUploading(false);
          setUploadProgress(0);
          toast({
            title: "Error",
            description:
              error instanceof Error ? error.message : "Failed to upload video",
            variant: "destructive",
          });
        }
      };

      try {
        await handleUpload(acceptedFiles[0]);
      } catch (error) {
        setUploadError(
          error instanceof Error ? error.message : "Failed to upload video"
        );
        setIsUploading(false);
        setUploadProgress(0);
      }
    },
    [onVideoChange, onServerUpdate]
  );

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "video/mp4": [".mp4"],
      "video/quicktime": [".mov"],
      "video/x-msvideo": [".avi"],
      "video/x-matroska": [".mkv"],
      "video/webm": [".webm"],
    },
    maxFiles: 1,
    multiple: false,
    disabled: isUploading,
  });

  return (
    <div className={cn("space-y-4 w-full mt-2", className)}>
      {uploadedVideo?.key ? (
        <Card className="relative overflow-hidden border-2 border-muted shadow-sm hover:shadow-md transition-all duration-300">
          <CardContent className="p-4">
            <div className="space-y-4">
              {isUploading ? (
                <div className="flex flex-col items-center gap-4 p-8">
                  <div className="relative w-16 h-16">
                    <Skeleton className="h-full w-full rounded-full animate-pulse" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Upload className="h-6 w-6 text-muted-foreground animate-bounce" />
                    </div>
                  </div>
                  <div className="w-full max-w-md space-y-2">
                    <Progress value={uploadProgress} className="w-full" />
                    <p className="text-sm font-medium text-center text-muted-foreground">
                      Uploading... {Math.round(uploadProgress)}%
                    </p>
                  </div>
                </div>
              ) : (
                <VideoPlayer
                  sources={[
                    {
                      label: "Original",
                      src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${uploadedVideo.key}`,
                    },
                    {
                      label: "720p",
                      src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${uploadedVideo.key}?quality=720`,
                    },
                    {
                      label: "480p",
                      src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${uploadedVideo.key}?quality=480`,
                    },
                    {
                      label: "360p",
                      src: `${process.env.NEXT_PUBLIC_AWS_VIDEOS_CLOUDFRONT_URL}/${uploadedVideo.key}?quality=360`,
                    },
                  ]}
                />
              )}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <VideoIcon className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium truncate max-w-[200px]">
                      {uploadedVideo.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {formatFileSize(uploadedVideo.size)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    size="sm"
                    variant="secondary"
                    className="relative z-10"
                    disabled={isUploading}
                    {...getRootProps()}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Change
                    <input {...getInputProps()} className="hidden" />
                  </Button>
                  <Button
                    type="button"
                    size="sm"
                    variant="destructive"
                    onClick={() => onVideoChange(null)}
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div {...getRootProps()}>
          <Card
            className={cn(
              "border-2 border-dashed rounded-xl transition-all duration-300 cursor-pointer hover:border-primary/50 hover:bg-primary/5",
              isDragActive
                ? "border-primary bg-primary/10 shadow-lg scale-[1.02]"
                : "border-muted-foreground/25 hover:shadow-md",
              isUploading && "pointer-events-none opacity-50"
            )}
          >
            <CardContent className="flex flex-col items-center justify-center gap-4 p-8 text-center">
              {isUploading ? (
                <div className="flex flex-col items-center gap-4 w-full max-w-md">
                  <div className="relative w-16 h-16">
                    <Skeleton className="h-full w-full rounded-full animate-pulse" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Upload className="h-6 w-6 text-muted-foreground animate-bounce" />
                    </div>
                  </div>
                  <Progress value={uploadProgress} className="w-full" />
                  <p className="text-sm font-medium text-muted-foreground">
                    Uploading... {Math.round(uploadProgress)}%
                  </p>
                </div>
              ) : (
                <>
                  <div className="rounded-full bg-primary/10 p-6 group-hover:bg-primary/20 transition-colors duration-300">
                    <VideoIcon className="h-10 w-10 text-primary" />
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Drag & drop or click to browse
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Supports MP4, MOV, AVI, MKV, WEBM
                    </p>
                  </div>
                  <input {...getInputProps()} />
                </>
              )}
            </CardContent>
          </Card>
        </div>
      )}
      {uploadError && (
        <p className="text-sm text-destructive mt-2 text-center bg-destructive/10 py-2 px-3 rounded-lg">
          {uploadError}
        </p>
      )}
    </div>
  );
}
