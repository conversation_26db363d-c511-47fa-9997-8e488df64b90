"use client";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ImageIcon, Upload, X } from "lucide-react";
import Image from "next/image";
import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { UploadedFile } from "@/types/common";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/hooks/use-toast";
import { getS3UploadParams } from "@/lib/s3/action";
import { s3Config } from "@/config/s3";
import { Progress } from "@/components/ui/progress";

interface ThumbnailImageUploaderProps {
  uploadedImage: UploadedFile | null;
  onImageChange: (image: UploadedFile | null) => void;
  onServerUpdate?: (image: UploadedFile | null) => Promise<void>;
  className?: string;
}

async function uploadFileWithProgress(
  file: File,
  onProgress: (progress: number) => void
): Promise<{ url: string; key: string } | { error: string }> {
  try {
    const { url, fields, cloudfrontUrl } = await getS3UploadParams(
      file.name,
      file.type,
      file.size,
      "images"
    );

    if (!url || !fields) {
      throw new Error("Failed to get upload parameters");
    }

    const formData = new FormData();
    Object.entries(fields).forEach(([key, value]) => {
      formData.append(key, value as string);
    });
    formData.append("file", file);

    const xhr = new XMLHttpRequest();

    const uploadPromise = new Promise<void>((resolve, reject) => {
      xhr.upload.addEventListener("progress", (event) => {
        if (event.lengthComputable) {
          const percentComplete = (event.loaded / event.total) * 100;
          onProgress(percentComplete);
        }
      });

      xhr.addEventListener("load", () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve();
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });

      xhr.addEventListener("error", () => {
        reject(new Error("Upload failed"));
      });

      xhr.open("POST", url, true);
      xhr.send(formData);
    });

    await uploadPromise;

    return {
      url: `${cloudfrontUrl}/${fields.key}`,
      key: fields.key,
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Failed to upload file",
    };
  }
}

export function ThumbnailImageUploader({
  uploadedImage,
  onImageChange,
  onServerUpdate,
  className,
}: ThumbnailImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;

      setIsUploading(true);
      setUploadError(null);
      setUploadProgress(0);

      const handleUpload = async (file: File) => {
        try {
          const result = await uploadFileWithProgress(file, (progress) => {
            setUploadProgress(progress);
          });

          if ("error" in result) {
            throw new Error(result.error);
          }

          const uploadedFile: UploadedFile = {
            name: file.name,
            key: result.key,
            type: file.type,
            size: file.size,
          };

          onImageChange(uploadedFile);

          if (onServerUpdate) {
            await onServerUpdate(uploadedFile);
          }

          setIsUploading(false);
          setUploadError(null);
          setUploadProgress(0);
        } catch (error) {
          setUploadError(
            error instanceof Error ? error.message : "Failed to upload image"
          );
          setIsUploading(false);
          setUploadProgress(0);
          toast({
            title: "Error",
            description:
              error instanceof Error ? error.message : "Failed to upload image",
            variant: "destructive",
          });
        }
      };

      try {
        await handleUpload(acceptedFiles[0]);
      } catch (error) {
        setUploadError(
          error instanceof Error ? error.message : "Failed to upload image"
        );
        setIsUploading(false);
        setUploadProgress(0);
      }
    },
    [onImageChange, onServerUpdate]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".png", ".jpg", ".jpeg", ".gif"],
    },
    maxFiles: 1,
    multiple: false,
    disabled: isUploading,
  });

  return (
    <div className={cn("space-y-4 w-full mt-2 max-w-[300px]", className)}>
      {uploadedImage ? (
        <Card className="relative overflow-hidden rounded-xl border-2 border-muted shadow-sm hover:shadow-md transition-all duration-300">
          <CardContent className="p-0">
            <div className="relative aspect-video w-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_AWS_IMAGES_CLOUDFRONT_URL}/${uploadedImage.key}`}
                alt="Profile"
                fill
                className={cn(
                  "object-cover rounded-lg transition-opacity duration-300",
                  isUploading && "opacity-50"
                )}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              <div
                className={cn(
                  "absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent transition-all duration-300 flex items-center justify-center",
                  isUploading ? "opacity-100" : "opacity-0 hover:opacity-100"
                )}
              >
                {isUploading ? (
                  <div className="flex flex-col items-center gap-2 w-full px-4">
                    <div className="relative w-12 h-12">
                      <Skeleton className="h-full w-full rounded-full animate-pulse" />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Upload className="h-6 w-6 text-muted-foreground animate-bounce" />
                      </div>
                    </div>
                    <Progress
                      value={uploadProgress}
                      className="w-full bg-white/20"
                    />
                    <span className="text-sm font-medium text-white bg-black/50 px-3 py-1 rounded-full">
                      {Math.round(uploadProgress)}%
                    </span>
                  </div>
                ) : (
                  <Button
                    type="button"
                    size="sm"
                    variant="secondary"
                    className="bg-white/90 hover:bg-white shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 relative z-10"
                    disabled={isUploading}
                    {...getRootProps()}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    <span className="text-sm font-medium">Change Photo</span>
                    <input {...getInputProps()} className="hidden" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div {...getRootProps()}>
          <Card
            className={cn(
              "border-2 border-dashed rounded-xl transition-all duration-300 cursor-pointer hover:border-primary/50 hover:bg-primary/5",
              isDragActive
                ? "border-primary bg-primary/10 shadow-lg scale-[1.02]"
                : "border-muted-foreground/25 hover:shadow-md",
              isUploading && "pointer-events-none opacity-50"
            )}
          >
            <CardContent className="flex flex-col items-center justify-center gap-4 p-8 text-center aspect-square">
              {isUploading ? (
                <div className="flex flex-col items-center gap-3 w-full">
                  <div className="relative w-16 h-16">
                    <Skeleton className="h-full w-full rounded-full animate-pulse" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Upload className="h-6 w-6 text-muted-foreground animate-bounce" />
                    </div>
                  </div>
                  <Progress value={uploadProgress} className="w-full" />
                  <p className="text-sm font-medium text-muted-foreground">
                    Uploading... {Math.round(uploadProgress)}%
                  </p>
                </div>
              ) : (
                <>
                  <div className="rounded-full bg-primary/10 p-6 group-hover:bg-primary/20 transition-colors duration-300">
                    <ImageIcon className="h-10 w-10 text-primary" />
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">
                      Drag & drop or click to browse
                    </p>
                  </div>
                  <input {...getInputProps()} />
                </>
              )}
            </CardContent>
          </Card>
        </div>
      )}
      {uploadError && (
        <p className="text-sm text-destructive mt-2 text-center bg-destructive/10 py-2 px-3 rounded-lg">
          {uploadError}
        </p>
      )}
    </div>
  );
}
