import { Join<PERSON>perator } from "./common";

export interface MinimalInstructor {
  id: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  country: string;
  city: string;
  profileImageUrl?: string;
}

export interface SortItem {
  id: string;
  desc: boolean;
}

export interface FilterItem {
  id: string;
  value: any;
  operator: string;
}

export interface InstructorQueryParams {
  search?: string;
  limit?: number;
  page?: number;
  sort?: SortItem[];
  filters?: FilterItem[];
  joinOperator?: JoinOperator;
  from?: string;
  to?: string;
}

export interface PaginatedInstructorsResponse {
  instructors: MinimalInstructor[];
  total: number;
  page: number;
  totalPages: number;
}
