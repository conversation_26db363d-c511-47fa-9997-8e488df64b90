import { ApiResponse } from "./common";

export enum ApplicationStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
  UNDER_REVIEW = "under_review",
}

export interface InstructorApplication {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  description: string;
  educationalLevels: string[];
  occupations: string[];
  status: ApplicationStatus;
  reviewedBy?: string;
  reviewedAt?: Date;
  reviewNotes?: string;
  statusHistory?: Array<{
    status: ApplicationStatus;
    changedBy: string;
    changedAt: Date;
    notes: string;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateInstructorApplicationDto {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  description: string;
  educationalLevels: string[];
  occupations: string[];
}

export interface UpdateApplicationStatusDto {
  status: ApplicationStatus;
  reviewNotes: string;
}
