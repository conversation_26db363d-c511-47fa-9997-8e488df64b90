export enum AccountType {
  EMAIL = "email",
  GOOGLE = "google",
  FACEBOOK = "facebook",
}

export enum UserRole {
  USER = "user",
  ADMIN = "admin",
  STAFF = "staff",
  INSTRUCTOR = "instructor",
}

export enum TwoFactorMethod {
  NONE = "none",
  SMS = "sms",
  AUTHENTICATOR = "authenticator",
}

export enum UserStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  SUSPENDED = "suspended",
}

export enum DocumentStatus {
  ACTIVE = "active",
  ARCHIVED = "archived",
}

export interface User {
  _id: string;
  username: string;
  avatar?: string;
  email: string;
  emailVerified: string;
  accountType: AccountType;
  role: UserRole;
  twoFactorMethod: TwoFactorMethod;
  twoFactorEnabled: boolean;
  status: UserStatus;
  documentStatus: DocumentStatus;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateProfileRequest {
  username?: string;
  email?: string;
  currentPassword?: string;
  newPassword?: string;
}

export interface AdminUpdateUserRequest {
  userId: string;
  username?: string;
  email?: string;
  password?: string;
  role?: UserRole;
  status?: UserStatus;
  accountType?: AccountType;
  twoFactorEnabled?: boolean;
}

export interface UsersQueryParams {
  search?: string;
  role?: UserRole;
  status?: UserStatus;
  accountType?: AccountType;
  limit?: number;
  page?: number;
}

export interface PaginatedUsersResponse {
  users: User[];
  total: number;
  page: number;
  totalPages: number;
}

export interface DeleteManyUsersRequest {
  userIds: string[];
}

export interface DeleteManyUsersResponse {
  message: string;
  matchedCount: number;
  modifiedCount: number;
}

export interface DeleteUserResponse {
  message: string;
  userId: string;
}

export interface UserStatusCounts {
  counts: {
    active: number;
    inactive: number;
    suspended: number;
  };
  total: number;
}

export interface UserRoleCounts {
  counts: {
    admin: number;
    user: number;
    staff: number;
    instructor: number;
  };
  total: number;
}
