import { UploadedFile } from ".";
import { DocumentStatus } from "./common";

// Core enums that define template properties
export enum TemplateStatus {
  PUBLISHED = "published",
  DRAFT = "draft",
}

export enum TemplateMediaType {
  // Document types
  DOC = "doc",
  EXCEL = "excel",
  PDF = "pdf",
  PPT = "ppt",
  TXT = "txt",
  CSV = "csv",

  // Media types
  AUDIO = "audio",
  IMAGE = "image",
  VIDEO = "video",
  TEXT = "text",

  // Fallback
  OTHER = "other",
}

export enum TemplateTag {
  NONE = "none",
  BEST_SELLER = "best-seller",
  NEW_ARRIVAL = "new-arrival",
  TRENDING = "trending",
}

export enum TemplateApprovalStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}

// Main Template interface
export interface Template {
  // Core identifiers
  _id?: string;
  title: string;
  slug: string;

  // Content details
  shortDescription: string;
  description: string | null;
  thumbnail: UploadedFile;

  // Classification
  mediaType: TemplateMediaType;
  category: string;
  subcategory: string;
  tag: TemplateTag;

  // Pricing
  price: number;
  discountPrice: number | null;
  value: number;

  downloadCount: number;
  downloadMedia: UploadedFile;
  previewMedia: UploadedFile | null;
  helpMedia: UploadedFile | null;
  free: boolean;

  // Metadata
  version: string;
  createdAt: string;
  updatedAt: string;
  documentStatus: DocumentStatus;
  status: TemplateStatus;
  approvalStatus?: TemplateApprovalStatus;
  approvalComment?: string;
  approvedBy?: string;
  approvalDate?: string;
  createdBy: string;
  updatedBy: string;
}

export interface UploadedFileDto {
  key: string;
  name: string;
}

export interface CreateTemplateDto {
  title: string;
  slug: string;
  shortDescription: string;
  description?: string;
  thumbnail: UploadedFileDto;
  mediaType: TemplateMediaType;
  category: string;
  subcategory: string;
  price: number;
  discountPrice?: number;
  downloadMedia: UploadedFileDto;
  previewMedia?: UploadedFileDto;
  helpMedia?: UploadedFileDto;
  free?: boolean;
  version: string;
  status?: TemplateStatus;
}

export interface UpdateTemplateDto {
  title?: string;
  slug?: string;
  shortDescription?: string;
  description?: string;
  thumbnail?: UploadedFileDto;
  mediaType?: TemplateMediaType;
  helpMedia?: UploadedFileDto;
  category?: string;
  subcategory?: string;
  price?: number;
  discountPrice?: number;
  downloadMedia?: UploadedFileDto;
  previewMedia?: UploadedFileDto;
  free?: boolean;
  version?: string;
  status?: TemplateStatus;
}

export type TemplateFormValues = Omit<
  Template,
  "status" | "documentStatus" | "_id" | "createdAt" | "updatedAt"
> & {
  status: boolean; // true for published, false for draft
};
