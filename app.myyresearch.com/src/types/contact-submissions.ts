export interface ContactSubmission {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  message: string;
  status: 'new' | 'in_progress' | 'resolved' | 'closed';
  documentStatus: 'active' | 'archived';
  adminNotes: string | null;
  assignedTo: string | null;
  resolvedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface ContactSubmissionsFilter {
  search?: string;
  status?: 'new' | 'in_progress' | 'resolved' | 'closed';
  email?: string;
  limit?: number;
  page?: number;
  from?: string;
  to?: string;
}

export interface GetContactSubmissionsResponse {
  submissions: ContactSubmission[];
  total: number;
  page: number;
  totalPages: number;
}

export interface ContactStatusCounts {
  counts: Record<'new' | 'in_progress' | 'resolved' | 'closed', number>;
  total: number;
}
