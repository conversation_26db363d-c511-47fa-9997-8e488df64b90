import { DocumentStatus } from "./common";

export enum EnrollmentStatus {
  ACTIVE = "active",
  COMPLETED = "completed",
  DROPPED = "dropped",
}

export interface MinimalEnrollment {
  _id: string;
  user: string;
  courseId: string;
  coursePublicId: string;
  courseName: string;
  courseTitle: string;
  courseThumbnailUrl: string;
  progress: number;
  lastAccessedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  status?: EnrollmentStatus;
  documentStatus?: DocumentStatus;
}

export interface PaginatedEnrollmentsResponse {
  enrollments: MinimalEnrollment[];
  total: number;
  page: number;
  totalPages: number;
  hasMore: boolean;
}

export interface EnrollmentFilterParams {
  search?: string;
  page?: number;
  limit?: number;
  from?: string; // YYYY-MM-DD format
  to?: string; // YYYY-MM-DD format
  status?: EnrollmentStatus;
  documentStatus?: DocumentStatus;
}
