import { ApiResponse } from "./common";

// Payment status enums matching backend
export enum StripePaymentStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  SUCCEEDED = "succeeded",
  FAILED = "failed",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
  PARTIALLY_REFUNDED = "partially_refunded",
}

export enum PaymentMethod {
  CARD = "card",
  BANK_TRANSFER = "bank_transfer",
  WALLET = "wallet",
}

// Payment interface
export interface Payment {
  _id: string;
  userId?: string;
  orderId: string;
  cartId: string;
  stripePaymentIntentId: string;
  stripeChargeId?: string;
  amount: number;
  currency: string;
  status: StripePaymentStatus;
  paymentMethod?: PaymentMethod;
  paymentMethodDetails?: string;
  receiptUrl?: string;
  failureReason?: string;
  refundAmount?: number;
  refundReason?: string;
  refundedAt?: Date;
  paidAt?: Date;
  stripeMetadata?: Record<string, any>;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Request DTOs
export interface CreatePaymentIntentRequest {
  orderId: string;
  cartId: string;
  metadata?: Record<string, any>;
}

export interface ConfirmPaymentRequest {
  paymentIntentId: string;
  metadata?: Record<string, any>;
}

// Response DTOs
export interface CreatePaymentIntentResponse {
  success: boolean;
  message: string;
  clientSecret: string;
  paymentIntentId: string;
}

export interface ConfirmPaymentResponse {
  success: boolean;
  message: string;
  order: any;
  payment: Payment;
}

export interface GetPaymentResponse {
  success: boolean;
  message: string;
  payment: Payment;
}

export interface GetPaymentsResponse {
  success: boolean;
  message: string;
  payments: Payment[];
  total: number;
  page: number;
  totalPages: number;
}

// Admin filter interface for payments
export interface AdminPaymentFilter {
  status?: StripePaymentStatus;
  page?: number;
  limit?: number;
  from?: string;
  to?: string;
  userId?: string;
  orderId?: string;
  cartId?: string;
  paymentMethod?: PaymentMethod;
}

// Admin response interface for payments
export interface GetAllPaymentsResponse {
  success: boolean;
  message: string;
  payments: Payment[];
  total: number;
  page: number;
  totalPages: number;
}

// Filter interface for getting payments
export interface PaymentFilter {
  status?: StripePaymentStatus;
  page?: number;
  limit?: number;
  from?: string;
  to?: string;
}

// Legacy types for backward compatibility
export type PaymentResponse = ApiResponse<Payment>;
export type PaymentsResponse = ApiResponse<Payment[]>;
