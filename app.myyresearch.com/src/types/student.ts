// Enums
export enum Gender {
  MALE = "male",
  FEMALE = "female",
  OTHER = "other",
}

export enum TaskType {
  ASSIGNMENT_HELP = "assignment_help",
  RESEARCH_HELP = "research_help",
}

export enum AssignmentType {
  ESSAY = "essay",
  REPORT = "report",
  PRESENTATION = "presentation",
  PROJECT = "project",
  OTHER = "other",
}

export enum AcademicLevel {
  UNDERGRADUATE = "undergraduate",
  POSTGRADUATE = "postgraduate",
  DOCTORAL = "doctoral",
  OTHER = "other",
}

export enum FileFormat {
  PDF = "pdf",
  WORD = "word",
  POWERPOINT = "powerpoint",
  OTHER = "other",
}

export enum ResearchType {
  QUALITATIVE = "qualitative",
  QUANTITATIVE = "quantitative",
  MIXED_METHODS = "mixed_methods",
}

export enum ResearchStage {
  PROPOSAL = "proposal",
  DATA_COLLECTION = "data_collection",
  DATA_ANALYSIS = "data_analysis",
  WRITING = "writing",
  REVIEW = "review",
}

export enum ResearchSupport {
  METHODOLOGY = "methodology",
  DATA_ANALYSIS = "data_analysis",
  WRITING = "writing",
  REVIEW = "review",
}

export enum ResearchTool {
  SPSS = "spss",
  R = "r",
  NVIVO = "nvivo",
  MINITAB = "minitab",
  EXCEL = "excel",
  SMART_PLS = "smart_pls",
  STAT = "stat",
  EVIEWS = "eviews",
}

// Types
export interface AssignmentTask {
  type: TaskType.ASSIGNMENT_HELP;
  subjects: string[];
  assignmentType: AssignmentType;
  academicLevel: AcademicLevel;
  preferredFormat: FileFormat;
  assignedDate: Date;
}

export interface ResearchTask {
  type: TaskType.RESEARCH_HELP;
  researchTopic: string;
  researchType: ResearchType;
  fieldOfStudy: string;
  researchStage: ResearchStage;
  requiredSupport: ResearchSupport;
  toolsUsed: ResearchTool[];
  assignedDate: Date;
}

export interface CreateStudentDto {
  name: string;
  idPassport: string;
  age: number;
  gender: Gender;
  region: string;
  occupation: string;
  primaryReasons: string;
  email: string;
  tasks: (AssignmentTask | ResearchTask)[];
}

export interface Student extends CreateStudentDto {
  _id: string;
  user: string;
  createdAt: string;
  updatedAt: string;
}
