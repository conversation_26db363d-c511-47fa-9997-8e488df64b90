export interface ApiResponse<T> {
  status: ApiStatus;
  message: string | null;
  data: T;
}

export enum ApiStatus {
  SUCCESS = "SUCCESS",
  FAIL = "FAIL",
}

export type Session = {
  user: {
    token: string;
    refreshToken: string;
  };
  expires: Date;
  createdAt: Date;
};

export type UploadedFile = {
  key: string;
  name: string;
  size: number;
  type: string;
};

export enum DocumentStatus {
  ACTIVE = "active",
  ARCHIVED = "archived",
}

export enum JoinOperator {
  AND = "and",
  OR = "or",
}
