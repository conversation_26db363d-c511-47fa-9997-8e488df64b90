// Enums
export enum PaymentMethod {
  PAYPAL = "paypal",
  BANK_TRANSFER = "bank_transfer",
  STRIPE = "stripe",
}

export enum PaymentRequestStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
  PAID = "paid",
}

export enum DocumentStatus {
  ACTIVE = "active",
  ARCHIVED = "archived",
}

// Instructor Payment Details interfaces
export interface InstructorPaymentDetails {
  _id: string;
  instructorId: string;
  preferredPaymentMethod: PaymentMethod;
  paypalEmail?: string;
  bankAccountNumber?: string;
  bankRoutingNumber?: string;
  bankName?: string;
  accountHolderName?: string;
  swiftCode?: string;
  stripeAccountId?: string;
  documentStatus: DocumentStatus;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateInstructorPaymentDetailsRequest {
  preferredPaymentMethod: PaymentMethod;
  paypalEmail?: string;
  bankAccountNumber?: string;
  bankRoutingNumber?: string;
  bankName?: string;
  accountHolderName?: string;
  swiftCode?: string;
  stripeAccountId?: string;
}

export interface UpdateInstructorPaymentDetailsRequest {
  preferredPaymentMethod?: PaymentMethod;
  paypalEmail?: string;
  bankAccountNumber?: string;
  bankRoutingNumber?: string;
  bankName?: string;
  accountHolderName?: string;
  swiftCode?: string;
  stripeAccountId?: string;
}

// Payment Record interfaces
export interface PaymentRecord {
  _id: string;
  instructorId: string;
  amount: number;
  currency: string;
  paymentMethod: PaymentMethod;
  paymentDate: Date;
  transactionId?: string;
  notes?: string;
  orderItemDetailsIds: string[];
  lastPaidOrderItemDetailsId: string;
  processedBy: string;
  documentStatus: DocumentStatus;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreatePaymentRecordRequest {
  instructorId: string;
  amount: number;
  currency: string;
  paymentMethod: PaymentMethod;
  paymentDate: string;
  transactionId?: string;
  notes?: string;
  orderItemDetailsIds: string[];
}

// Payment Request interfaces
export interface PaymentRequest {
  _id: string;
  instructorId: string;
  requestedAmount: number;
  currency: string;
  status: PaymentRequestStatus;
  requestNotes?: string;
  adminNotes?: string;
  approvedAt?: Date;
  rejectedAt?: Date;
  paidAt?: Date;
  processedBy?: string;
  paymentRecordId?: string;
  documentStatus: DocumentStatus;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreatePaymentRequestRequest {
  requestedAmount: number;
  currency: string;
  requestNotes?: string;
}

export interface UpdatePaymentRequestRequest {
  status: PaymentRequestStatus;
  adminNotes?: string;
}

// Balance and Summary interfaces
export interface InstructorPaymentBalance {
  instructorId: string;
  instructorName: string;
  instructorEmail: string;
  totalEarnings: number;
  totalPaid: number;
  currentBalance: number;
  currency: string;
  unpaidOrderItemsCount: number;
  lastPaymentDate?: Date;
  firstEarningDate: Date;
}

export interface GetInstructorBalancesResponse {
  instructors: InstructorPaymentBalance[];
  totalInstructors: number;
  totalUnpaidAmount: number;
  currency: string;
}

export interface UnpaidOrderItem {
  _id: string;
  itemType: string;
  itemTitle: string;
  totalPrice: number;
  instructorEarning: number;
  orderDate: Date;
  publicId: string;
}

export interface PaymentHistory {
  _id: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  paymentDate: Date;
  transactionId?: string;
  notes?: string;
  orderItemsCount: number;
}

export interface InstructorBalanceDetails {
  instructorId: string;
  totalEarnings: number;
  totalPaid: number;
  currentBalance: number;
  currency: string;
  unpaidOrderItems: UnpaidOrderItem[];
  paymentHistory: PaymentHistory[];
}

export interface PaymentSummaryStats {
  totalEarningsAllTime: number;
  totalPaidAllTime: number;
  totalUnpaidAmount: number;
  totalInstructorsWithEarnings: number;
  totalInstructorsWithUnpaidBalance: number;
  currency: string;
  averageUnpaidBalance: number;
  highestUnpaidBalance: number;
}

// Filter interfaces
export interface InstructorBalanceFilter {
  search?: string;
  minBalance?: number;
  maxBalance?: number;
  fromDate?: string;
  toDate?: string;
  page?: number;
  limit?: number;
}

// API Response interfaces
export interface InstructorPaymentDetailsResponse {
  success: boolean;
  message: string;
  data: InstructorPaymentDetails;
}

export interface PaymentRecordResponse {
  success: boolean;
  message: string;
  data: PaymentRecord;
}

export interface PaymentRequestResponse {
  success: boolean;
  message: string;
  data: PaymentRequest;
}

export interface InstructorBalancesResponse {
  success: boolean;
  message: string;
  data: GetInstructorBalancesResponse;
}

export interface InstructorBalanceDetailsResponse {
  success: boolean;
  message: string;
  data: InstructorBalanceDetails;
}

export interface PaymentSummaryStatsResponse {
  success: boolean;
  message: string;
  data: PaymentSummaryStats;
}

export interface PaymentHistoryResponse {
  success: boolean;
  message: string;
  data: PaymentRecord[];
}

export interface PaymentRequestsResponse {
  success: boolean;
  message: string;
  data: PaymentRequest[];
}
