import { ApiResponse } from "./common";

// Order status enums matching backend
export enum OrderStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
  REFUNDED = "refunded",
}

export enum PaymentStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
  REFUNDED = "refunded",
}

export enum OrderItemType {
  COURSE = "course",
  TEMPLATE = "template",
}

export enum CreatorType {
  MYRESEARCH = "myresearch",
  INSTRUCTOR = "instructor",
}

// Order item interface
export interface OrderItem {
  _id: string;
  itemType: OrderItemType;
  itemId: string;
  publicId: string;
  quantity: number;
  unitPrice: number;
  discountPrice?: number;
  totalPrice: number;
  title: string;
  thumbnail?: string;
}

// Order item details interface (for successful orders)
export interface OrderItemDetails {
  _id: string;
  itemType: OrderItemType;
  itemId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  publicId: string;
  createdBy: string;
  creatorType: CreatorType;
  orderedBy: string;
  orderDate: Date;
  itemTitle: string;
  thumbnail: {
    url: string;
    key: string;
    name: string;
    size: number;
  };
  shortDescription: string;
  orderId: string;
  category: string;
  subCategory: string;
  createdAt: Date;
  updatedAt: Date;
}

// Order interface
export interface Order {
  _id: string;
  orderNumber: string;
  userId?: string;
  cartId: string;
  items: OrderItem[];
  subtotal: number;
  discount: number;
  tax: number;
  total: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentIntentId?: string;
  stripePaymentId?: string;
  paidAt?: Date;
  completedAt?: Date;
  cancelledAt?: Date;
  cancellationReason?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Request DTOs
export interface CreateOrderRequest {
  cartId: string;
  metadata?: Record<string, any>;
}

// Response DTOs
export interface CreateOrderResponse {
  success: boolean;
  message: string;
  order: Order;
}

export interface GetOrderResponse {
  success: boolean;
  message: string;
  order: Order;
}

export interface GetOrdersResponse {
  success: boolean;
  message: string;
  orders: Order[];
  total: number;
  page: number;
  totalPages: number;
}

// Admin filter interface for orders
export interface AdminOrderFilter {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  page?: number;
  limit?: number;
  from?: string;
  to?: string;
  userId?: string;
  cartId?: string;
  orderNumber?: string;
  paymentIntentId?: string;
}

// Admin response interface for orders
export interface GetAllOrdersResponse {
  success: boolean;
  message: string;
  orders: Order[];
  total: number;
  page: number;
  totalPages: number;
}

// Filter interface for getting orders
export interface OrderFilter {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  page?: number;
  limit?: number;
  from?: string;
  to?: string;
}

// Legacy types for backward compatibility
export type OrderResponse = ApiResponse<Order>;
export type OrdersResponse = ApiResponse<Order[]>;
