import { UploadedFile } from "./common";

// Types
export enum LessonType {
  VIDEO = "video",
  TEXT = "text",
}

export interface CreateLessonDto {
  title: string;
  type: LessonType;
  content?: string;
  duration?: number;
  video?: UploadedFile;
  order: number;
  isFree: boolean;
}

export interface Lesson extends CreateLessonDto {
  _id: string;
}

export interface CreateSectionDto {
  title: string;
  order: number;
  type?: LessonType;
  lessons?: CreateLessonDto[];
}

export interface Section extends CreateSectionDto {
  _id: string;
  lessons?: Lesson[];
}

export interface CreateCourseDto {
  title: string;
  shortDescription: string;
  description?: string;
  thumbnail: UploadedFile;
  category: string;
  subcategory: string;
  price: number;
  discountPrice?: number;
  sections?: CreateSectionDto[];
  status: "draft" | "published";
}

export interface Course extends CreateCourseDto {
  _id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy?: string;
  sections?: Section[];
}
