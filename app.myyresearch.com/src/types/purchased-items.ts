export enum ItemType {
  COURSE = 'course',
  TEMPLATE = 'template',
}

export enum CreatorType {
  INSTRUCTOR = 'instructor',
  MYRESEARCH = 'myresearch',
}

export interface PurchasedItem {
  _id: string;
  purchasedBy?: string;
  itemType: ItemType;
  itemId: string;
  publicId: string;
  title: string;
  thumbnail?: string;
  category: string;
  subCategory: string;
  creatorType: CreatorType;
  createdBy: string;
  sellingPrice: number;
  purchasedDate: Date;
  orderId: string;
  paymentId: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface GetPurchasedItemsFilterDto {
  itemType?: ItemType;
  category?: string;
  subCategory?: string;
  search?: string;
  page?: number;
  limit?: number;
}

export interface GetPurchasedItemsResponse {
  items: PurchasedItem[];
  total: number;
  page: number;
  totalPages: number;
  success: boolean;
  message: string;
}

// Specific interfaces for admin panel
export interface PurchasedTemplate extends PurchasedItem {
  itemType: ItemType.TEMPLATE;
}

export interface PurchasedCourse extends PurchasedItem {
  itemType: ItemType.COURSE;
}

export interface PurchasedItemsFilterParams {
  search?: string;
  page?: number;
  limit?: number;
  from?: string; // YYYY-MM-DD format
  to?: string; // YYYY-MM-DD format
  category?: string;
  subCategory?: string;
  itemType?: ItemType;
}
