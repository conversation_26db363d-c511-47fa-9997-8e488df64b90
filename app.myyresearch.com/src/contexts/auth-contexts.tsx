"use client";

import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { User } from "@/types/auth";
import { getProfile } from "@/lib/authenticaton";
import { ApiStatus } from "@/types/common";

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  login: (
    user: User,
    tokens: { accessToken: string; refreshToken: string }
  ) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (user: User) => void;
  refreshProfile: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const loadUser = async () => {
      try {
        const userData = await getProfile();
        if (userData.status === ApiStatus.SUCCESS) {
          console.log("userData AUTH", userData.data);
          setUser(userData.data);
          setIsAuthenticated(true);
        } else {
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error("Failed to load user:", error);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    setLoading(true);
    loadUser();
  }, []);

  const login = async (userData: User) => {
    setUser(userData);
    setIsAuthenticated(true);
  };

  const logout = async () => {
    setUser(null);
    setIsAuthenticated(false);
  };

  const updateUser = (user: User) => {
    setUser(user);
  };

  const refreshProfile = async () => {
    try {
      const updatedProfile = await getProfile();
      if (updatedProfile.status === ApiStatus.SUCCESS) {
        setUser(updatedProfile.data);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error("Failed to refresh profile:", error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        isAuthenticated,
        login,
        logout,
        updateUser,
        refreshProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

export function useProfile() {
  const { user, loading, refreshProfile } = useAuth();
  return { profile: user, isLoading: loading, refreshProfile };
}

export function useAuthStatus() {
  const { isAuthenticated, loading } = useAuth();
  return { isAuthenticated, isLoading: loading };
}
