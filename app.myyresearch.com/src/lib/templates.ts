import { ApiResponse } from "@/types/common";
import axios from "@/utils/axios";
import {
  CreateTemplateDto,
  Template,
  TemplateMediaType,
  TemplateStatus,
  TemplateTag,
  UpdateTemplateDto,
  UploadedFileDto,
  TemplateApprovalStatus,
} from "@/types/template";

// Add new types for CreatorType and MinimalisticTemplateDto
export enum CreatorType {
  ADMIN = "admin",
  INSTRUCTOR = "instructor",
}

export interface MinimalisticTemplateDto {
  _id: string;
  title: string;
  publicId: string;
  shortDescription: string;
  thumbnail: UploadedFileDto;
  mediaType: TemplateMediaType;
  category: string;
  subcategory: string;
  tag: TemplateTag;
  price: number;
  discountPrice?: number;
  downloadCount: number;
  free: boolean;
  status: TemplateStatus;
  approvalStatus: TemplateApprovalStatus;
  createdAt: Date;
  updatedAt: Date;
  creatorType: CreatorType;
  instructorUsername?: string;
}

export interface GetAllTemplatesResponseDto {
  templates: MinimalisticTemplateDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// API Functions for Template Management
export async function getTemplates(params: {
  search?: string;
  category?: string;
  mediaType?: string;
  tag?: string;
  status?: string;
  limit?: number;
  page?: number;
  filters?: any[];
  from?: string;
  to?: string;
}): Promise<ApiResponse<GetAllTemplatesResponseDto>> {
  try {
    const res = await axios.get("/templates/", { params });
    console.log("res.data ::: ", res.data);
    return res.data;
  } catch (error) {
    console.log("error in getTemplates", error);
    return error as ApiResponse<GetAllTemplatesResponseDto>;
  }
}

export async function getTemplateCategoryCounts(params: {
  search?: string;
  category?: string;
  mediaType?: string;
  tag?: string;
  status?: string;
  limit?: number;
  page?: number;
  from?: string;
  to?: string;
}): Promise<ApiResponse<{ counts: Record<string, number> }>> {
  try {
    const res = await axios.get("/templates/category-counts", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<{ counts: Record<string, number> }>;
  }
}

export async function getTemplateMediaTypeCounts(params: {
  search?: string;
  category?: string;
  mediaType?: string;
  tag?: string;
  status?: string;
  limit?: number;
  page?: number;
  from?: string;
  to?: string;
}): Promise<ApiResponse<{ counts: Record<string, number> }>> {
  try {
    const res = await axios.get("/templates/media-type-counts", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<{ counts: Record<string, number> }>;
  }
}

export async function getTemplateTagCounts(params: {
  search?: string;
  category?: string;
  mediaType?: string;
  tag?: string;
  status?: string;
  limit?: number;
  page?: number;
  from?: string;
  to?: string;
}): Promise<ApiResponse<{ counts: Record<string, number> }>> {
  try {
    const res = await axios.get("/templates/tag-counts", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<{ counts: Record<string, number> }>;
  }
}

export async function updateTemplates(
  templateIds: string[],
  data: Partial<Template>
): Promise<ApiResponse<Template[]>> {
  try {
    const res = await axios.patch("/templates/bulk", {
      templateIds,
      ...data,
    });
    return res.data;
  } catch (error) {
    return error as ApiResponse<Template[]>;
  }
}

// get template by id
export async function getTemplateById(
  id: string
): Promise<ApiResponse<Template>> {
  try {
    const res = await axios.get(`/templates/${id}`);
    return res.data;
  } catch (error) {
    console.log("error in getTemplateById", error);
    return error as ApiResponse<Template>;
  }
}

export async function createTemplate(
  data: CreateTemplateDto
): Promise<ApiResponse<Template>> {
  try {
    console.log("data in createTemplate ######## ", data);
    const res = await axios.post("/templates", data);
    return res.data;
  } catch (error) {
    console.log("error in createTemplate", error);
    return error as ApiResponse<Template>;
  }
}

export async function updateTemplate(
  id: string,
  data: UpdateTemplateDto
): Promise<ApiResponse<Template>> {
  try {
    console.log("data in updateTemplate", data);
    const res = await axios.patch(`/templates/${id}`, data);
    return res.data;
  } catch (error) {
    console.log("error in updateTemplate", error);
    return error as ApiResponse<Template>;
  }
}

export async function deleteTemplate(id: string): Promise<ApiResponse<void>> {
  try {
    const res = await axios.delete(`/templates/${id}`);
    return res.data;
  } catch (error) {
    console.log("error in deleteTemplate", error);
    return error as ApiResponse<void>;
  }
}

// Note: TemplateApprovalStatus is imported from types/template

// Approval DTO interface
export interface ApproveTemplateDto {
  status: TemplateApprovalStatus;
  comment?: string;
}

// Approve template function
export async function approveTemplate(
  id: string,
  data: ApproveTemplateDto
): Promise<ApiResponse<Template>> {
  try {
    const res = await axios.patch(`/templates/${id}/approve`, data);
    return res.data;
  } catch (error) {
    console.log("error in approveTemplate", error);
    return error as ApiResponse<Template>;
  }
}
