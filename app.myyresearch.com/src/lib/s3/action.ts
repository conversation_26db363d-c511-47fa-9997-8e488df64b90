"use server";

import { deleteFile, getDownloadUrl, getUploadParams } from "./core";
import { s3Config } from "@/config/s3";

// Configuration object
const uploadConfig = {
  maxSizeBytes: 500 * 1024 * 1024, // 500MB
};

export async function getS3UploadParams(
  filename: string,
  contentType: string,
  sizeBytes: number,
  bucketType: "files" | "images" | "videos"
) {
  // Validate file size
  if (sizeBytes > uploadConfig.maxSizeBytes) {
    throw new Error(
      `File size exceeds the maximum limit of ${
        uploadConfig.maxSizeBytes / (1024 * 1024)
      }MB`
    );
  }

  // If validation passes, proceed with getting upload params
  const response = await getUploadParams({ filename, contentType, bucketType });

  return response;
}

export async function getS3DownloadUrl(
  key: string,
  bucketType: "files" | "images" | "videos"
) {
  const { url, size } = await getDownloadUrl(key, bucketType);

  return url;
}

export async function deleteFileFromS3(
  key: string,
  bucketType: "files" | "images" | "videos"
) {
  const { success } = await deleteFile(key, bucketType);

  return { success };
}
