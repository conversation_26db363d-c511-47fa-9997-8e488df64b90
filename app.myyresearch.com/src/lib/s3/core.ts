import "server-only";

import { s3Config, awsConfig } from "@/config/s3";
import {
  DeleteObjectCommand,
  GetObjectCommand,
  S3Client,
  HeadObjectCommand,
} from "@aws-sdk/client-s3";
import { createPresignedPost } from "@aws-sdk/s3-presigned-post";
import { v4 as uuidv4 } from "uuid";

interface GetUploadParams {
  filename: string;
  contentType: string;
  bucketType: "files" | "images" | "videos";
}

// Initialize S3 client with custom MYYRESEARCH_ credentials
const s3Client = new S3Client({
  region: awsConfig.region,
  credentials: awsConfig.credentials,
});

export async function getUploadParams({
  contentType,
  bucketType,
}: GetUploadParams) {
  try {
    const key = uuidv4();
    const bucket = s3Config.buckets[bucketType];

    if (!bucket) {
      throw new Error(`Invalid bucket type: ${bucketType}`);
    }

    const { url, fields } = await createPresignedPost(s3Client, {
      Bucket: bucket.name,
      Key: key,
      Conditions: [
        ["content-length-range", 0, 524288000], // up to 10 MB
        ["starts-with", "$Content-Type", contentType],
      ],
      Fields: {
        "Content-Type": contentType,
      },
      Expires: 600, // Seconds before the presigned post expires. 3600 by default.
    });

    return { url, fields, cloudfrontUrl: bucket.cloudfrontUrl };
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: "Unknown error" };
  }
}

export async function getDownloadUrl(
  key: string,
  bucketType: "files" | "images" | "videos"
) {
  try {
    const bucket = s3Config.buckets[bucketType];

    if (!bucket) {
      throw new Error(`Invalid bucket type: ${bucketType}`);
    }

    // First, get the object metadata to retrieve the size
    const headCommand = new HeadObjectCommand({
      Bucket: bucket.name,
      Key: key,
    });

    const headResponse = await s3Client.send(headCommand);
    const size = headResponse.ContentLength; // This is the file size in bytes

    const url = `${bucket.cloudfrontUrl}/${key}`;

    return { url, size };
  } catch (error) {
    console.error("Error generating download URL:", error);
    return { error: "Failed to generate download URL" };
  }
}

export async function deleteFile(
  key: string,
  bucketType: "files" | "images" | "videos"
) {
  try {
    const bucket = s3Config.buckets[bucketType];

    if (!bucket) {
      throw new Error(`Invalid bucket type: ${bucketType}`);
    }

    const command = new DeleteObjectCommand({
      Bucket: bucket.name,
      Key: key,
    });

    await s3Client.send(command);

    return { success: true };
  } catch (error) {
    return { error: "Failed to delete file from S3 Bucket" };
  }
}
