import {
  LayoutGrid,
  LucideIcon,
  BookOpen,
  Files,
  GraduationCap,
  School,
  UserCog,
  UserCircle,
  CreditCard,
  Download,
  Star,
  MessageSquare,
} from "lucide-react";
import { Role } from "@/types/auth";

type Submenu = {
  href: string;
  label: string;
  active?: boolean;
};

type Menu = {
  href: string;
  label: string;
  active?: boolean;
  icon: LucideIcon;
  submenus?: Submenu[];
  roles?: Role[]; // Optional: specify which roles can see this menu item
};

type Group = {
  groupLabel: string;
  menus: Menu[];
  roles?: Role[]; // Optional: specify which roles can see this group
};

// Helper function to check if a user role can access a menu item
function canAccessMenuItem(
  userRole: Role | undefined,
  allowedRoles?: Role[]
): boolean {
  if (!allowedRoles || allowedRoles.length === 0) {
    return true; // No restrictions
  }
  return userRole ? allowedRoles.includes(userRole) : false;
}

export function getMenuList(userRole?: Role): Group[] {
  const allGroups: Group[] = [
    {
      groupLabel: "",
      menus: [
        {
          href: "/dashboard",
          label: "Dashboard",
          icon: LayoutGrid,
          submenus: [],
          roles: [Role.Admin], // Only ADMIN can see Dashboard
        },
      ],
    },
    {
      groupLabel: "Contents",
      menus: [
        {
          href: "",
          label: "Courses",
          icon: BookOpen,
          submenus: [
            {
              href: "/courses",
              label: "All Courses",
            },
            {
              href: "/courses/add",
              label: "Add Course",
            },
          ],
        },
        {
          href: "",
          label: "Templates",
          icon: Files,
          submenus: [
            {
              href: "/templates",
              label: "All Templates",
            },
            {
              href: "/templates/add",
              label: "Add Template",
            },
          ],
        },
        //reviews
        {
          href: "/reviews",
          label: "Reviews",
          icon: Star,
          roles: [Role.Admin, Role.Instructor], // STAFF cannot see Reviews
        },
      ],
    },
    //Profiles
    {
      groupLabel: "Profiles",
      menus: [
        {
          href: "/instructors",
          label: "Instructors",
          icon: School,
          roles: [Role.Admin], // Only ADMIN and INSTRUCTOR can see Instructors
        },
        {
          href: "/students",
          label: "Students",
          icon: GraduationCap,
          roles: [Role.Admin], // Only ADMIN can see Students
          submenus: [
            {
              href: "/students",
              label: "All Students",
            },
            {
              href: "/students/add",
              label: "Add Student",
            },
          ],
        },
      ],
    },
    // payments
    {
      groupLabel: "Payments",
      menus: [
        //enrollments
        {
          href: "/enrollments",
          label: "Enrollments",
          icon: GraduationCap,
          roles: [Role.Admin, Role.Instructor], // STAFF cannot see Enrollments
        },
        //downloads
        {
          href: "/downloads",
          label: "Downloads",
          icon: Download,
          roles: [Role.Admin, Role.Instructor], // STAFF cannot see Downloads
        },
        //payments
        {
          href: "/payments",
          label: "Payments",
          icon: CreditCard,
          roles: [Role.Admin], // Only ADMIN can see Payments
        },
      ],
    },
    // Payment Details
    {
      groupLabel: "Payment Details",
      menus: [
        {
          href: "/payment-details",
          label: "Payment Details",
          icon: CreditCard,
          roles: [Role.Instructor], // Only INSTRUCTOR can see Payment Details
        },
        {
          href: "/instructor-payments",
          label: "Instructor Payments",
          icon: CreditCard,
          roles: [Role.Admin], // Only ADMIN can see Instructor Payments
        },
      ],
    },
    {
      groupLabel: "Settings",
      menus: [
        {
          href: "/users",
          label: "Users",
          icon: UserCog,
          roles: [Role.Admin], // Only ADMIN can see Users
        },
        {
          href: "/contact-submissions",
          label: "Contact Submissions",
          icon: MessageSquare,
          roles: [Role.Admin], // Only ADMIN can see Contact Submissions
        },
        {
          href: "/account",
          label: "Account",
          icon: UserCircle,
        },
      ],
    },
  ];

  // Filter groups and menus based on user role
  return allGroups
    .map((group) => ({
      ...group,
      menus: group.menus.filter((menu) =>
        canAccessMenuItem(userRole, menu.roles)
      ),
    }))
    .filter(
      (group) =>
        group.menus.length > 0 && canAccessMenuItem(userRole, group.roles)
    );
}
