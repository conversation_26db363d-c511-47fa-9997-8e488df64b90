import { ApiResponse } from "@/types/common";
import {
  GetAllPaymentsResponse,
  AdminPaymentFilter,
  Payment,
  CreatePaymentIntentRequest,
  CreatePaymentIntentResponse,
  ConfirmPaymentRequest,
  ConfirmPaymentResponse,
  GetPaymentResponse,
  GetPaymentsResponse,
  PaymentFilter,
} from "@/types/payments";
import axios from "@/utils/axios";

// API Function to get all payments (Admin only)
export async function getAdminPayments(
  params: AdminPaymentFilter
): Promise<ApiResponse<GetAllPaymentsResponse>> {
  try {
    const res = await axios.get("/payments/admin/all", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetAllPaymentsResponse>;
  }
}

// API Function to get a specific payment by ID (Admin only)
export async function getAdminPaymentById(
  paymentId: string
): Promise<ApiResponse<GetPaymentResponse>> {
  try {
    const res = await axios.get(`/payments/admin/${paymentId}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetPaymentResponse>;
  }
}

// API Function to create payment intent
export async function createPaymentIntent(
  data: CreatePaymentIntentRequest
): Promise<ApiResponse<CreatePaymentIntentResponse>> {
  try {
    const res = await axios.post("/payments/create-intent", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<CreatePaymentIntentResponse>;
  }
}

// API Function to confirm payment
export async function confirmPayment(
  data: ConfirmPaymentRequest
): Promise<ApiResponse<ConfirmPaymentResponse>> {
  try {
    const res = await axios.post("/payments/confirm", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<ConfirmPaymentResponse>;
  }
}

// API Function to get user's payments
export async function getUserPayments(
  params: PaymentFilter
): Promise<ApiResponse<GetPaymentsResponse>> {
  try {
    const res = await axios.get("/payments", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetPaymentsResponse>;
  }
}

// API Function to get a specific payment by ID
export async function getPaymentById(
  paymentId: string
): Promise<ApiResponse<GetPaymentResponse>> {
  try {
    const res = await axios.get(`/payments/${paymentId}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetPaymentResponse>;
  }
}

// API Function to refund payment (Admin only)
export async function refundPayment(
  paymentId: string,
  amount?: number,
  reason?: string
): Promise<ApiResponse<GetPaymentResponse>> {
  try {
    const res = await axios.post(`/payments/admin/${paymentId}/refund`, {
      amount,
      reason,
    });
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetPaymentResponse>;
  }
}
