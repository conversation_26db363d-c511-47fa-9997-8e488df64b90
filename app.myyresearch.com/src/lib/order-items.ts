import { ApiResponse } from "@/types/common";
import { OrderItemDetails, OrderItemType, CreatorType } from "@/types/orders";
import axios from "@/utils/axios";

// Filter interface for order items queries (Admin/Instructor)
export interface OrderItemsFilter {
  search?: string;
  itemType?: OrderItemType;
  creatorType?: CreatorType;
  category?: string;
  subCategory?: string;
  createdBy?: string;
  orderedBy?: string;
  from?: string;
  to?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Response interface for order items data
export interface OrderItemsResponse {
  success: boolean;
  message: string;
  data: OrderItemDetails[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Get all order items with role-based filtering (<PERSON><PERSON> sees all, Instructor sees only their items)
export async function getOrderItems(
  filters: OrderItemsFilter = {}
): Promise<ApiResponse<OrderItemsResponse>> {
  try {
    const res = await axios.get("/orders/items/all", { params: filters });
    return res.data;
  } catch (error) {
    console.error("Error fetching order items:", error);
    return error as ApiResponse<OrderItemsResponse>;
  }
}

// Get course enrollments (Admin sees all, Instructor sees only their courses)
export async function getCourseEnrollments(
  filters: Omit<OrderItemsFilter, "itemType"> = {}
): Promise<ApiResponse<OrderItemsResponse>> {
  try {
    const res = await axios.get("/orders/items/enrollments", {
      params: filters,
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching course enrollments:", error);
    return error as ApiResponse<OrderItemsResponse>;
  }
}

// Get template downloads (Admin sees all, Instructor sees only their templates)
export async function getTemplateDownloads(
  filters: Omit<OrderItemsFilter, "itemType"> = {}
): Promise<ApiResponse<OrderItemsResponse>> {
  try {
    const res = await axios.get("/orders/items/downloads", { params: filters });
    return res.data;
  } catch (error) {
    console.error("Error fetching template downloads:", error);
    return error as ApiResponse<OrderItemsResponse>;
  }
}

// Helper function to get category counts for filtering
export async function getOrderItemsCategoryCounts(): Promise<
  Array<{ category: string; count: number }>
> {
  try {
    const res = await axios.get("/orders/items/categories");
    return res.data?.data || [];
  } catch (error) {
    console.error("Error fetching category counts:", error);
    return [];
  }
}

// Get order items statistics for dashboard
export async function getOrderItemsStats(): Promise<{
  totalEnrollments: number;
  totalDownloads: number;
  totalRevenue: number;
  recentItems: OrderItemDetails[];
}> {
  try {
    const [enrollmentsResponse, downloadsResponse] = await Promise.all([
      getCourseEnrollments({ limit: 100 }), // Get all enrollments for stats
      getTemplateDownloads({ limit: 100 }), // Get all downloads for stats
    ]);

    const enrollments = enrollmentsResponse.data?.data || [];
    const downloads = downloadsResponse.data?.data || [];

    const allItems = [...enrollments, ...downloads];
    const totalRevenue = allItems.reduce(
      (sum, item) => sum + item.totalPrice,
      0
    );

    // Get recent items (last 10)
    const recentItems = allItems
      .sort(
        (a, b) =>
          new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime()
      )
      .slice(0, 10);

    return {
      totalEnrollments: enrollments.length,
      totalDownloads: downloads.length,
      totalRevenue,
      recentItems,
    };
  } catch (error) {
    console.error("Error fetching order items stats:", error);
    return {
      totalEnrollments: 0,
      totalDownloads: 0,
      totalRevenue: 0,
      recentItems: [],
    };
  }
}
