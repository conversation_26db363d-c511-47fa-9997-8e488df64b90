import { ApiResponse } from "@/types/common";
import axios from "@/utils/axios";

// Types for contact form submission
export interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  message: string;
}

export interface ContactSubmissionResponse {
  message: string;
  submissionId: string;
}

// Types for admin contact submissions
export interface ContactSubmission {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  message: string;
  status: 'new' | 'in_progress' | 'resolved' | 'closed';
  documentStatus: 'active' | 'archived';
  adminNotes: string | null;
  assignedTo: string | null;
  resolvedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface ContactSubmissionsFilter {
  search?: string;
  status?: 'new' | 'in_progress' | 'resolved' | 'closed';
  email?: string;
  limit?: number;
  page?: number;
  from?: string;
  to?: string;
}

export interface GetContactSubmissionsResponse {
  submissions: ContactSubmission[];
  total: number;
  page: number;
  totalPages: number;
}

export interface ContactStatusCounts {
  counts: Record<'new' | 'in_progress' | 'resolved' | 'closed', number>;
  total: number;
}

/**
 * Submit contact form (Public endpoint)
 */
export async function submitContactForm(
  data: ContactFormData
): Promise<ApiResponse<ContactSubmissionResponse>> {
  try {
    const res = await axios.post("/contact-us", data);
    return res.data;
  } catch (error) {
    console.error("Error submitting contact form:", error);
    return error as ApiResponse<ContactSubmissionResponse>;
  }
}

/**
 * Get all contact submissions (Admin only)
 */
export async function getAdminContactSubmissions(
  params: ContactSubmissionsFilter
): Promise<ApiResponse<GetContactSubmissionsResponse>> {
  try {
    const res = await axios.get("/contact-us/admin/all", { params });
    return res.data;
  } catch (error) {
    console.error("Error fetching contact submissions:", error);
    return error as ApiResponse<GetContactSubmissionsResponse>;
  }
}

/**
 * Get contact submission status counts (Admin only)
 */
export async function getContactStatusCounts(): Promise<ApiResponse<ContactStatusCounts>> {
  try {
    const res = await axios.get("/contact-us/admin/status-counts");
    return res.data;
  } catch (error) {
    console.error("Error fetching contact status counts:", error);
    return error as ApiResponse<ContactStatusCounts>;
  }
}
