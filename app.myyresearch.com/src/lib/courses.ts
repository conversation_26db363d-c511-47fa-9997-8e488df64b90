import { ApiResponse, ApiStatus, UploadedFile } from "@/types/common";
import axios from "@/utils/axios";
import {
  Course,
  CreateCourseDto,
  CreateLessonDto,
  CreateSectionDto,
  UpdateCourseDto,
} from "@/types/course";

// API Functions
export async function createCourse(
  data: CreateCourseDto
): Promise<ApiResponse<Course>> {
  try {
    console.log("Creating course:", data);
    const res = await axios.post("/courses", data);
    return res.data;
  } catch (error) {
    console.error("Error creating course:", error);
    return error as ApiResponse<Course>;
  }
}

export async function getCourses(params: {
  search?: string;
  category?: string;
  limit?: number;
  page?: number;
  filters?: any[];
  sort?: any[];
  from?: string;
  to?: string;
}): Promise<
  ApiResponse<{
    courses: Course[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }>
> {
  try {
    const res = await axios.get("/courses", { params });
    console.log("res", JSON.stringify(res.data, null, 2));
    return res.data;
  } catch (error) {
    return error as ApiResponse<{
      courses: Course[];
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    }>;
  }
}

export async function getCourse(id: string): Promise<ApiResponse<Course>> {
  try {
    const res = await axios.get(`/courses/admin/${id}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<Course>;
  }
}

export async function updateCourse(
  id: string,
  data: Partial<Course>
): Promise<ApiResponse<Course>> {
  try {
    const res = await axios.patch(`/courses/instructor/courses/${id}`, data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<Course>;
  }
}

export async function deleteCourse(id: string): Promise<ApiResponse<void>> {
  try {
    const res = await axios.delete(`/courses/instructor/courses/${id}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<void>;
  }
}

export async function addSection(
  courseId: string,
  data: CreateSectionDto
): Promise<ApiResponse<Course>> {
  try {
    const res = await axios.post(
      `/courses/instructor/courses/${courseId}/sections`,
      data
    );
    return res.data;
  } catch (error) {
    return error as ApiResponse<Course>;
  }
}

export async function updateSection(
  courseId: string,
  sectionId: string,
  data: Partial<CreateSectionDto>
): Promise<ApiResponse<Course>> {
  try {
    const res = await axios.patch(
      `/courses/instructor/courses/${courseId}/sections/${sectionId}`,
      data
    );
    return res.data;
  } catch (error) {
    return error as ApiResponse<Course>;
  }
}

export async function deleteSection(
  courseId: string,
  sectionId: string
): Promise<ApiResponse<Course>> {
  try {
    const res = await axios.delete(
      `/courses/instructor/courses/${courseId}/sections/${sectionId}`
    );
    return res.data;
  } catch (error) {
    return error as ApiResponse<Course>;
  }
}

export async function addLesson(
  courseId: string,
  sectionId: string,
  data: CreateLessonDto
): Promise<ApiResponse<Course>> {
  try {
    const res = await axios.post(
      `/courses/instructor/courses/${courseId}/sections/${sectionId}/lessons`,
      data
    );
    return res.data;
  } catch (error) {
    return error as ApiResponse<Course>;
  }
}

export async function updateLesson(
  courseId: string,
  sectionId: string,
  lessonId: string,
  data: Partial<CreateLessonDto>
): Promise<ApiResponse<Course>> {
  try {
    const res = await axios.patch(
      `/courses/instructor/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}`,
      data
    );
    return res.data;
  } catch (error) {
    return error as ApiResponse<Course>;
  }
}

export async function deleteLesson(
  courseId: string,
  sectionId: string,
  lessonId: string
): Promise<ApiResponse<Course>> {
  try {
    const res = await axios.delete(
      `/courses/instructor/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}`
    );
    return res.data;
  } catch (error) {
    return error as ApiResponse<Course>;
  }
}

export async function getCourseCategoryCounts(params: {
  search?: string;
  category?: string;
  limit?: number;
  page?: number;
  from?: string;
  to?: string;
}): Promise<ApiResponse<{ counts: Record<string, number> }>> {
  try {
    const res = await axios.get("/courses/instructor/courses/category-counts", {
      params,
    });
    return res.data;
  } catch (error) {
    return error as ApiResponse<{ counts: Record<string, number> }>;
  }
}

export async function deleteCourses(
  courseIds: string[]
): Promise<ApiResponse<void>> {
  try {
    const res = await axios.delete("/courses/instructor/courses/bulk", {
      data: { courseIds },
    });
    return res.data;
  } catch (error) {
    return error as ApiResponse<void>;
  }
}

export async function updateCourses(
  courseIds: string[],
  data: Partial<Course>
): Promise<ApiResponse<Course[]>> {
  try {
    const res = await axios.patch("/courses/instructor/courses/bulk", {
      courseIds,
      ...data,
    });
    return res.data;
  } catch (error) {
    return error as ApiResponse<Course[]>;
  }
}

export async function softDeleteCourse(id: string): Promise<ApiResponse<void>> {
  try {
    const res = await axios.delete(`/courses/${id}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<void>;
  }
}

export async function updateCourseWithAuth(
  id: string,
  data: UpdateCourseDto
): Promise<ApiResponse<Course>> {
  try {
    const res = await axios.patch(`/courses/${id}`, data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<Course>;
  }
}

// Approval status enum (matching backend)
export enum CourseApprovalStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}

// Approval DTO interface
export interface ApproveCourseDto {
  status: CourseApprovalStatus;
  comment?: string;
}

// Approve course function
export async function approveCourse(
  id: string,
  data: ApproveCourseDto
): Promise<ApiResponse<Course>> {
  try {
    const res = await axios.patch(`/courses/${id}/approve`, data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<Course>;
  }
}
