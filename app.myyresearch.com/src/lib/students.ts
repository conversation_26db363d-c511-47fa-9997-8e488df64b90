import { ApiResponse } from "@/types/common";
import { Student, CreateStudentDto, Gender } from "@/types/student";
import axios from "@/utils/axios";

export interface StudentFilterDto {
  search?: string;
  gender?: Gender;
  region?: string;
  occupation?: string;
  limit?: number;
  page?: number;
  sort?: { id: string; desc: boolean }[];
  filters?: { id: string; value: any }[];
  joinOperator?: "AND" | "OR";
  from?: string;
  to?: string;
}

export interface PaginatedStudentsResponse {
  students: Student[];
  total: number;
  page: number;
  totalPages: number;
}

export async function createStudent(
  data: CreateStudentDto
): Promise<ApiResponse<Student>> {
  try {
    const res = await axios.post("/students", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<Student>;
  }
}

export async function getStudents(
  params: StudentFilterDto
): Promise<ApiResponse<PaginatedStudentsResponse>> {
  try {
    const res = await axios.get("/students", { params });
    console.log("res Data :::: ", res.data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaginatedStudentsResponse>;
  }
}

export async function getMyStudent(): Promise<ApiResponse<Student>> {
  try {
    const res = await axios.get("/students/me");
    return res.data;
  } catch (error) {
    return error as ApiResponse<Student>;
  }
}

export async function getStudent(id: string): Promise<ApiResponse<Student>> {
  try {
    const res = await axios.get(`/students/${id}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<Student>;
  }
}

export async function deleteStudent(id: string): Promise<ApiResponse<void>> {
  try {
    const res = await axios.delete(`/students/${id}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<void>;
  }
}

export async function getStudentByUser(
  userId: string
): Promise<ApiResponse<Student>> {
  try {
    const res = await axios.get(`/students/user/${userId}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<Student>;
  }
}

export async function updateStudent(
  id: string,
  data: Partial<CreateStudentDto>
): Promise<ApiResponse<Student>> {
  try {
    const res = await axios.patch(`/students/${id}`, data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<Student>;
  }
}
