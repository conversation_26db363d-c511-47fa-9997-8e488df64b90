import { ApiResponse } from "@/types/common";
import {
  TemplateDownloadFilterParams,
  PaginatedTemplateDownloadsResponse,
} from "@/types/download";
import axios from "@/utils/axios";

// API Function
export async function getAdminTemplateDownloads(
  params: TemplateDownloadFilterParams
): Promise<ApiResponse<PaginatedTemplateDownloadsResponse>> {
  try {
    const res = await axios.get("/template-downloads/admin", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaginatedTemplateDownloadsResponse>;
  }
}
