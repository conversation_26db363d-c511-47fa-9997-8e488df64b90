import { ApiResponse } from "@/types/common";
import {
  GetPurchasedItemsResponse,
  PurchasedItemsFilterParams,
  ItemType,
} from "@/types/purchased-items";
import axios from "@/utils/axios";

// API Function to get all purchased items (Admin only)
export async function getAdminPurchasedItems(
  params: PurchasedItemsFilterParams
): Promise<ApiResponse<GetPurchasedItemsResponse>> {
  try {
    const res = await axios.get("/users/purchased-items/admin/all", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetPurchasedItemsResponse>;
  }
}

// API Function to get purchased templates (Admin only)
export async function getAdminPurchasedTemplates(
  params: Omit<PurchasedItemsFilterParams, 'itemType'>
): Promise<ApiResponse<GetPurchasedItemsResponse>> {
  try {
    const templateParams = {
      ...params,
      itemType: ItemType.TEMPLATE,
    };
    const res = await axios.get("/users/purchased-items/admin/all", { 
      params: templateParams 
    });
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetPurchasedItemsResponse>;
  }
}

// API Function to get purchased courses (Admin only)
export async function getAdminPurchasedCourses(
  params: Omit<PurchasedItemsFilterParams, 'itemType'>
): Promise<ApiResponse<GetPurchasedItemsResponse>> {
  try {
    const courseParams = {
      ...params,
      itemType: ItemType.COURSE,
    };
    const res = await axios.get("/users/purchased-items/admin/all", { 
      params: courseParams 
    });
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetPurchasedItemsResponse>;
  }
}
