import { jwtVerify, SignJWT, JWTPayload } from "jose";
import { NextRequest, NextResponse } from "next/server";
import { ApiStatus, Session, ApiResponse } from "@/types/common";
import {
  LoginCredentials,
  RegisterCredentials,
  AuthResponse,
  RegisterResponse,
  User,
  UpdateProfileData,
  UsernameCheckResponse,
  TwoFactorResponse,
  TwoFactorVerifyLoginResponse,
  TwoFactorVerifyResponse,
  ForgotPasswordResponse,
  ResetPasswordResponse,
  Role,
  CreateUserRequest,
} from "@/types/auth";
import axios from "@/utils/axios";
import { default as defaultAxios } from "axios";
import Cookies from "js-cookie";

const secretKey = process.env.NEXT_PUBLIC_JWT_SECRET || "secret123";
const key = new TextEncoder().encode(secretKey);

export async function encrypt(payload: JWTPayload | undefined) {
  return await new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("7 days")
    .sign(key);
}

export async function decrypt(input: string): Promise<Session> {
  const { payload } = await jwtVerify(input, key, {
    algorithms: ["HS256"],
  });
  return payload as Session;
}

// Session Management
export async function createSession(tokens: {
  accessToken: string;
  refreshToken: string;
}) {
  const user = {
    token: tokens.accessToken,
    refreshToken: tokens.refreshToken,
  };

  const expires = new Date(Date.now() + 1000 * 60 * 60 * 24 * 1);
  const refreshExpires = new Date(Date.now() + 1000 * 60 * 60 * 24 * 7);
  const createdAt = new Date(Date.now());
  const session = await encrypt({ user, expires, createdAt });

  // Use js-cookie for client-side
  if (typeof window !== "undefined") {
    Cookies.set("session", session, { expires: 7 }); // 7 days
  } else {
    // Server-side
    const { cookies } = await import("next/headers");
    (await cookies()).set("session", session, { expires: refreshExpires });
  }
}

export async function getSession(): Promise<Session | null> {
  let sessionCookie;

  // Get cookie based on environment
  if (typeof window !== "undefined") {
    sessionCookie = Cookies.get("session");
  } else {
    const { cookies } = await import("next/headers");
    sessionCookie = (await cookies()).get("session")?.value;
  }

  if (!sessionCookie) return null;

  const session = await decrypt(sessionCookie);
  const now = new Date();
  const expires = new Date(session.expires);

  // Check if session is expired
  if (now > expires) {
    if (session.user?.refreshToken) {
      const refreshResponse = await refreshAccessToken(
        session.user.refreshToken
      );

      if (
        refreshResponse.status === ApiStatus.SUCCESS &&
        refreshResponse.data
      ) {
        return {
          ...session,
          user: {
            token: refreshResponse.data.accessToken,
            refreshToken: refreshResponse.data.refreshToken,
          },
          expires: new Date(Date.now() + 1000 * 60), // 7 days
          createdAt: new Date(),
        };
      }
    }

    await clearSession();
    return null;
  }

  return session;
}

export async function clearSession() {
  if (typeof window !== "undefined") {
    Cookies.remove("session");
  } else {
    const { cookies } = await import("next/headers");
    (await cookies()).delete("session");
  }
}

// Authentication Actions
export async function register(
  data: RegisterCredentials
): Promise<ApiResponse<RegisterResponse>> {
  try {
    const res = await axios.post("/auth/register", {
      username: data.username,
      email: data.email,
      password: data.password,
    });

    if (res.data.status === ApiStatus.SUCCESS) {
      await createSession(res.data.data.tokens);
    }

    return res.data;
  } catch (error) {
    return error as ApiResponse<RegisterResponse>;
  }
}

export async function login(
  data: LoginCredentials
): Promise<ApiResponse<AuthResponse>> {
  try {
    const res = await axios.post("/auth/login", {
      identifier: data.identifier,
      password: data.password,
    });

    if (res.data.status === ApiStatus.SUCCESS) {
      if (res.data.data.requires2FA) {
        return res.data;
      }

      if (res.data.data.user.role === Role.User) {
        return {
          status: ApiStatus.FAIL,
          message: "Access denied. Admin access only.",
          data: res.data.data,
        };
      }

      await createSession(res.data.data.tokens);
    }

    return res.data;
  } catch (error) {
    return error as ApiResponse<AuthResponse>;
  }
}

export async function logout(): Promise<void> {
  try {
    await clearSession();
    // await axios.post("/auth/logout");
  } catch (error) {
    throw error;
  }
}

export async function getProfile(): Promise<ApiResponse<User>> {
  try {
    const res = await axios.get("/users/profile");
    return res.data;
  } catch (error) {
    return error as ApiResponse<User>;
  }
}

export async function checkUsername(
  username: string
): Promise<ApiResponse<UsernameCheckResponse>> {
  try {
    const res = await axios.get(`/auth/check-username/${username}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<UsernameCheckResponse>;
  }
}

export async function forgotPassword(
  email: string
): Promise<ApiResponse<ForgotPasswordResponse>> {
  try {
    const res = await axios.post("/auth/forgot-password", { email });
    return res.data;
  } catch (error) {
    return error as ApiResponse<ForgotPasswordResponse>;
  }
}

export async function resetPassword(
  token: string,
  newPassword: string
): Promise<ApiResponse<ResetPasswordResponse>> {
  try {
    const res = await axios.post("/auth/reset-password", {
      token,
      newPassword,
    });
    return res.data;
  } catch (error) {
    return error as ApiResponse<ResetPasswordResponse>;
  }
}

export async function enable2FA(
  code: string
): Promise<ApiResponse<TwoFactorResponse>> {
  try {
    const res = await axios.post("/auth/2fa/enable", { code });
    return res.data;
  } catch (error) {
    return error as ApiResponse<TwoFactorResponse>;
  }
}

export async function verify2FA(
  userId: string,
  code: string
): Promise<ApiResponse<TwoFactorVerifyLoginResponse>> {
  try {
    const res = await axios.post("/auth/2fa/verify", { userId, code });
    if (res.data.status === ApiStatus.SUCCESS) {
      await createSession(res.data.data.tokens);
    }
    return res.data;
  } catch (error) {
    return error as ApiResponse<TwoFactorVerifyLoginResponse>;
  }
}

export async function verify2FASetup(
  code: string
): Promise<ApiResponse<TwoFactorVerifyResponse>> {
  try {
    const res = await axios.post("/auth/2fa/verify-setup", { code });
    return res.data;
  } catch (error) {
    return error as ApiResponse<TwoFactorVerifyResponse>;
  }
}

export async function disable2FA(): Promise<
  ApiResponse<TwoFactorVerifyResponse>
> {
  try {
    const res = await axios.post("/auth/2fa/disable");
    return res.data;
  } catch (error) {
    return error as ApiResponse<TwoFactorVerifyResponse>;
  }
}

export async function generate2FA(): Promise<ApiResponse<TwoFactorResponse>> {
  try {
    const res = await axios.post("/auth/2fa/generate");
    return res.data;
  } catch (error) {
    return error as ApiResponse<TwoFactorResponse>;
  }
}

export async function updateProfile(
  data: UpdateProfileData
): Promise<ApiResponse<User>> {
  try {
    const res = await axios.post("/users/update-profile", data);
    return res.data;
  } catch (error) {
    console.log("error ::: ", error);
    return error as ApiResponse<User>;
  }
}

export async function updateSession(request: NextRequest) {
  const session = request.cookies.get("session")?.value;

  if (!session) {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  return NextResponse.next();
}

export async function refreshAccessToken(
  refreshToken: string
): Promise<ApiResponse<{ accessToken: string; refreshToken: string }>> {
  try {
    const res = await defaultAxios.post(
      process.env.API_URL + "/api/v1" + "/auth/refresh",
      { refreshToken }
    );

    if (res.data.status === ApiStatus.SUCCESS) {
      await createSession(res.data.data);
    }

    return res.data;
  } catch (error) {
    return error as ApiResponse<{ accessToken: string; refreshToken: string }>;
  }
}

export async function createUser(
  data: CreateUserRequest
): Promise<ApiResponse<User>> {
  try {
    const res = await axios.post("/auth/create-user", {
      username: data.username,
      email: data.email,
      password: data.password,
      accountType: data.accountType,
      role: data.role,
      twoFactorMethod: data.twoFactorMethod,
      status: data.status,
    });

    return res.data;
  } catch (error) {
    return error as ApiResponse<User>;
  }
}
