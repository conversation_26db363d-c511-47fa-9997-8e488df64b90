import { ApiResponse } from "@/types/common";
import {
  GetAllOrdersResponse,
  AdminOrderFilter,
  Order,
  CreateOrderRequest,
  CreateOrderResponse,
  GetOrderResponse,
  GetOrdersResponse,
  OrderFilter,
} from "@/types/orders";
import axios from "@/utils/axios";

// API Function to get all orders (Admin only)
export async function getAdminOrders(
  params: AdminOrderFilter
): Promise<ApiResponse<GetAllOrdersResponse>> {
  try {
    const res = await axios.get("/orders/admin/all", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetAllOrdersResponse>;
  }
}

// API Function to get a specific order by ID (Admin only)
export async function getAdminOrderById(
  orderId: string
): Promise<ApiResponse<GetOrderResponse>> {
  try {
    const res = await axios.get(`/orders/admin/${orderId}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetOrderResponse>;
  }
}

// API Function to create a new order
export async function createOrder(
  data: CreateOrderRequest
): Promise<ApiResponse<CreateOrderResponse>> {
  try {
    const res = await axios.post("/orders", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<CreateOrderResponse>;
  }
}

// API Function to get user's orders
export async function getUserOrders(
  params: OrderFilter
): Promise<ApiResponse<GetOrdersResponse>> {
  try {
    const res = await axios.get("/orders", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetOrdersResponse>;
  }
}

// API Function to get a specific order by ID
export async function getOrderById(
  orderId: string
): Promise<ApiResponse<GetOrderResponse>> {
  try {
    const res = await axios.get(`/orders/${orderId}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetOrderResponse>;
  }
}

// API Function to cancel an order
export async function cancelOrder(
  orderId: string,
  reason?: string
): Promise<ApiResponse<GetOrderResponse>> {
  try {
    const res = await axios.patch(`/orders/${orderId}/cancel`, { reason });
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetOrderResponse>;
  }
}

// API Function to update order status (Admin only)
export async function updateOrderStatus(
  orderId: string,
  status: string
): Promise<ApiResponse<GetOrderResponse>> {
  try {
    const res = await axios.patch(`/orders/admin/${orderId}/status`, { status });
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetOrderResponse>;
  }
}
