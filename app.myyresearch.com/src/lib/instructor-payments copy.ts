import { ApiResponse } from "@/types/common";
import {
  InstructorPaymentDetails,
  CreateInstructorPaymentDetailsRequest,
  UpdateInstructorPaymentDetailsRequest,
  PaymentRecord,
  CreatePaymentRecordRequest,
  PaymentRequest,
  CreatePaymentRequestRequest,
  UpdatePaymentRequestRequest,
  GetInstructorBalancesResponse,
  InstructorBalanceDetails,
  PaymentSummaryStats,
  InstructorBalanceFilter,
  InstructorPaymentDetailsResponse,
  PaymentRecordResponse,
  PaymentRequestResponse,
  InstructorBalancesResponse,
  InstructorBalanceDetailsResponse,
  PaymentSummaryStatsResponse,
  PaymentHistoryResponse,
  PaymentRequestsResponse,
} from "@/types/instructor-payments";
import axios from "@/utils/axios";

// Instructor Payment Details API Functions
export async function createInstructorPaymentDetails(
  data: CreateInstructorPaymentDetailsRequest
): Promise<ApiResponse<InstructorPaymentDetails>> {
  try {
    const res = await axios.post("/instructor-payments/payment-details", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<InstructorPaymentDetails>;
  }
}

export async function updateInstructorPaymentDetails(
  data: UpdateInstructorPaymentDetailsRequest
): Promise<ApiResponse<InstructorPaymentDetails>> {
  try {
    const res = await axios.put("/instructor-payments/payment-details", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<InstructorPaymentDetails>;
  }
}

export async function getInstructorPaymentDetails(): Promise<ApiResponse<InstructorPaymentDetails>> {
  try {
    const res = await axios.get("/instructor-payments/payment-details");
    return res.data;
  } catch (error) {
    return error as ApiResponse<InstructorPaymentDetails>;
  }
}

// Instructor Balance and History API Functions
export async function getInstructorBalance(): Promise<ApiResponse<InstructorBalanceDetails>> {
  try {
    const res = await axios.get("/instructor-payments/balance");
    return res.data;
  } catch (error) {
    return error as ApiResponse<InstructorBalanceDetails>;
  }
}

export async function getInstructorPaymentHistory(): Promise<ApiResponse<PaymentRecord[]>> {
  try {
    const res = await axios.get("/instructor-payments/payment-history");
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaymentRecord[]>;
  }
}

// Payment Request API Functions
export async function createPaymentRequest(
  data: CreatePaymentRequestRequest
): Promise<ApiResponse<PaymentRequest>> {
  try {
    const res = await axios.post("/instructor-payments/payment-request", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaymentRequest>;
  }
}

export async function getInstructorPaymentRequests(): Promise<ApiResponse<PaymentRequest[]>> {
  try {
    const res = await axios.get("/instructor-payments/payment-requests");
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaymentRequest[]>;
  }
}

// Admin API Functions
export async function getAdminInstructorBalances(
  params: InstructorBalanceFilter
): Promise<ApiResponse<GetInstructorBalancesResponse>> {
  try {
    const res = await axios.get("/instructor-payments/admin/balances", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<GetInstructorBalancesResponse>;
  }
}

export async function getAdminInstructorBalanceById(
  instructorId: string
): Promise<ApiResponse<InstructorBalanceDetails>> {
  try {
    const res = await axios.get(`/instructor-payments/admin/instructor/${instructorId}/balance`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<InstructorBalanceDetails>;
  }
}

export async function createAdminPaymentRecord(
  data: CreatePaymentRecordRequest
): Promise<ApiResponse<PaymentRecord>> {
  try {
    const res = await axios.post("/instructor-payments/admin/payment-record", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaymentRecord>;
  }
}

export async function getAdminPaymentHistory(): Promise<ApiResponse<PaymentRecord[]>> {
  try {
    const res = await axios.get("/instructor-payments/admin/payment-history");
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaymentRecord[]>;
  }
}

export async function getAdminPaymentRequests(): Promise<ApiResponse<PaymentRequest[]>> {
  try {
    const res = await axios.get("/instructor-payments/admin/payment-requests");
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaymentRequest[]>;
  }
}

export async function updateAdminPaymentRequest(
  requestId: string,
  data: UpdatePaymentRequestRequest
): Promise<ApiResponse<PaymentRequest>> {
  try {
    const res = await axios.put(`/instructor-payments/admin/payment-request/${requestId}`, data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaymentRequest>;
  }
}

export async function getAdminPaymentSummaryStats(): Promise<ApiResponse<PaymentSummaryStats>> {
  try {
    const res = await axios.get("/instructor-payments/admin/stats");
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaymentSummaryStats>;
  }
}
