import { ApiResponse } from "@/types/common";
import axios from "@/utils/axios";
import {
  ApplicationStatus,
  CreateInstructorApplicationDto,
  InstructorApplication,
  UpdateApplicationStatusDto,
} from "@/types/instructor-application";

export async function createInstructorApplication(
  data: CreateInstructorApplicationDto
): Promise<ApiResponse<InstructorApplication>> {
  try {
    const res = await axios.post("/instructor-applications", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<InstructorApplication>;
  }
}

export async function getInstructorApplications(params?: {
  status?: ApplicationStatus;
}): Promise<ApiResponse<InstructorApplication[]>> {
  try {
    const res = await axios.get("/instructor-applications", { params });
    return res.data;
  } catch (error) {
    console.error("Error fetching instructor applications", error);
    return error as ApiResponse<InstructorApplication[]>;
  }
}

export async function getInstructorApplication(
  id: string
): Promise<ApiResponse<InstructorApplication>> {
  try {
    const res = await axios.get(`/instructor-applications/${id}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<InstructorApplication>;
  }
}

export async function updateApplicationStatus(
  id: string,
  data: UpdateApplicationStatusDto
): Promise<ApiResponse<InstructorApplication>> {
  try {
    const res = await axios.patch(
      `/instructor-applications/${id}/status`,
      data
    );
    return res.data;
  } catch (error) {
    return error as ApiResponse<InstructorApplication>;
  }
}
