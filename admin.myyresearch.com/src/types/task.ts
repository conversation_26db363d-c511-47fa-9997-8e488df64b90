export enum TaskStatus {
  TODO = "todo",
  IN_PROGRESS = "in-progress",
  DONE = "done",
  CANCELED = "canceled",
}

export enum TaskLabel {
  BUG = "bug",
  FEATURE = "feature",
  ENHANCEMENT = "enhancement",
  DOCUMENTATION = "documentation",
}

export enum TaskPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
}

export interface Task {
  id: string
  code: string
  title: string | null
  status: TaskStatus
  label: TaskLabel
  priority: TaskPriority
  archived: boolean
  createdAt: Date
  updatedAt: Date | null
}
