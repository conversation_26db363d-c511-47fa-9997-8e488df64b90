export interface User {
  _id: string;
  username: string;
  avatar?: string;
  email: string;
  emailVerified: boolean;
  accountType: AccountType;
  role: Role;
  twoFactorMethod: TwoFactorMethod;
  twoFactorEnabled: boolean;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  accountType: string;
  role: string;
  twoFactorMethod: string;
  status: string;
}

export enum DocumentStatus {
  Active = "active",
  Archived = "archived",
}

export enum Status {
  Active = "active",
  Inactive = "inactive",
}

export enum TwoFactorMethod {
  Authenticator = "authenticator",
  Email = "email",
  Phone = "phone",
  None = "none",
}

export enum AccountType {
  Email = "email",
  Google = "google",
}

export enum Role {
  User = "user",
  Admin = "admin",
}

export interface LoginCredentials {
  identifier: string;
  password: string;
}

export interface RegisterCredentials {
  username: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
  user: User;
  requires2FA?: boolean;
  userId?: string;
}

export interface RegisterResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface TwoFactorResponse {
  secret: string;
  qrCode: string;
}

export interface TwoFactorVerifyResponse {
  isValid: boolean;
  message: string;
}

export interface TwoFactorVerifyLoginResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

export interface TwoFactorLoginVerifyResponse {
  user: User;
  tokens: {
    accessToken: string;
    refreshToken: string;
  };
}

// UpdateProfileData,

export interface UpdateProfileData {
  username?: string;
  email?: string;
  currentPassword?: string;
  newPassword?: string;
}

// UsernameCheckResponse,

export interface UsernameCheckResponse {
  username: string;
  exists: boolean;
  available: boolean;
}

export interface ForgotPasswordResponse {
  message: string;
}

export interface ResetPasswordResponse {
  message: string;
}

export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
}
