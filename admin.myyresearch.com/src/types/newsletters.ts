export interface NewsletterQueryParams {
  search?: string;
  limit?: number;
  page?: number;
}

export interface Newsletter {
  email: string;
  isSubscribed: boolean;
  subscribedAt: Date;
  unsubscribedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaginatedNewslettersResponse {
  subscribers: Newsletter[];
  total: number;
  page: number;
  totalPages: number;
}

export interface NewsletterCountResponse {
  count: number;
}

export interface NewsletterDeleteResponse {
  message: string;
  email: string;
}
