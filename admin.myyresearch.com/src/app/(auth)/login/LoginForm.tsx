"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { login } from "@/lib/authenticaton";
import { loginFormSchema } from "@/schema/auth";
import { ApiStatus } from "@/types/common";
import TwoFactorForm from "./two-factor-form";
import { useToast } from "@/hooks/use-toast";
import { Logo } from "@/components/ui/logo";
import { useAuth } from "@/contexts/auth-contexts";
import { PasswordInput } from "@/components/ui/password-input";

export default function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [userId, setUserId] = useState<string | null>(null);
  const { toast } = useToast();
  const { login: authLogin } = useAuth();
  const form = useForm<z.infer<typeof loginFormSchema>>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      email: "<EMAIL>",
      password: "123456",
    },
  });

  const router = useRouter();

  const onSubmit = async (values: z.infer<typeof loginFormSchema>) => {
    try {
      const res = await login({
        identifier: values.email,
        password: values.password,
      });

      if (res.status === ApiStatus.SUCCESS) {
        if (res.data.requires2FA) {
          setUserId(res.data.userId || null);
        } else {
          await authLogin(res.data.user, res.data.tokens);

          toast({
            description: "You have successfully Logged in.",
          });
          router.push("/");
        }
      } else {
        toast({
          variant: "destructive",
          description: res.message || "An error occurred.",
        });
      }
    } catch (error) {
      console.error("Login error:", error);
      toast({
        variant: "destructive",
        description: "Failed to login. Please try again later.",
      });
    }
  };

  const handle2FAError = (error: string) => {
    toast({
      variant: "destructive",
      description: error,
    });
  };

  if (userId) {
    return <TwoFactorForm userId={userId} onError={handle2FAError} />;
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="p-6 md:p-8">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center text-center">
          <Logo />
          <p className="text-balance text-muted-foreground">
            Login to your Acme Inc account
          </p>
        </div>
        <div className="grid gap-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            {...form.register("email")}
          />
          {form.formState.errors.email && (
            <p className="text-sm text-red-500">
              {form.formState.errors.email.message}
            </p>
          )}
        </div>
        <div className="grid gap-2">
          <div className="flex items-center">
            <Label htmlFor="password">Password</Label>
            <a
              href="/forgot-password"
              className="ml-auto text-sm underline-offset-2 hover:underline"
            >
              Forgot your password?
            </a>
          </div>
          <PasswordInput
            id="password"
            disabled={form.formState.isSubmitting}
            {...form.register("password")}
          />
          {form.formState.errors.password && (
            <p className="text-sm text-red-500">
              {form.formState.errors.password.message}
            </p>
          )}
        </div>
        <Button
          type="submit"
          className="w-full"
          disabled={form.formState.isSubmitting}
        >
          {form.formState.isSubmitting ? (
            <Loader2 className="size-6 animate-spin" />
          ) : (
            "Login"
          )}
        </Button>
      </div>
    </form>
  );
}
