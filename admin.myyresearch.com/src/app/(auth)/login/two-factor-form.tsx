"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ApiError } from "@/types/error";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import * as authentication from "@/lib/authenticaton";
import { ApiStatus } from "@/types/common";
import { useAuth } from "@/contexts/auth-contexts";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";

interface TwoFactorFormProps {
  userId: string;
  onError: (error: string) => void;
}

// Update form schema
const formSchema = z.object({
  verificationCode: z.string().length(6, "Code must be exactly 6 digits"),
});

export default function TwoFactorForm({ userId, onError }: TwoFactorFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const { login: authLogin } = useAuth();

  // Replace useState with useForm
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      verificationCode: "",
    },
  });

  // Update submit handler
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setLoading(true);

    try {
      const result = await authentication.verify2FA(
        userId,
        values.verificationCode
      );

      if (result.status === ApiStatus.SUCCESS) {
        await authLogin(result.data.user, result.data.tokens);
        router.push("/");
        router.refresh();
      } else {
        onError(result.message || "Verification failed");
      }
    } catch (err: unknown) {
      const apiError = err as ApiError;
      onError(apiError.response?.data?.message || "Verification failed");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="p-6 md:p-8 space-y-6"
      >
        <FormField
          control={form.control}
          name="verificationCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Verification Code</FormLabel>
              <FormControl>
                <InputOTP maxLength={6} {...field}>
                  <InputOTPGroup>
                    <InputOTPSlot index={0} />
                    <InputOTPSlot index={1} />
                    <InputOTPSlot index={2} />
                    <InputOTPSlot index={3} />
                    <InputOTPSlot index={4} />
                    <InputOTPSlot index={5} />
                  </InputOTPGroup>
                </InputOTP>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full" disabled={loading}>
          {loading ? "Verifying..." : "Verify Code"}
        </Button>
      </form>
    </Form>
  );
}
