"use client";
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import TwoFactorSetup from "./TwoFactorSetup";
import { useAuth } from "@/contexts/auth-contexts";
import { disable2FA } from "@/lib/authenticaton";
import { TwoFactorMethod } from "@/types/auth";

export default function TwoFactorCard() {
  const { user, updateUser } = useAuth();
  const [show2FASetup, setShow2FASetup] = useState(false);

  if (!user) return null;

  const handle2FAComplete = () => {
    updateUser({
      ...user,
      twoFactorEnabled: true,
    });
    setShow2FASetup(false);
  };

  const handleDisable2FA = async () => {
    try {
      await disable2FA();
      updateUser({
        ...user,
        twoFactorEnabled: false,
        twoFactorMethod: TwoFactorMethod.None,
      });
    } catch (error) {
      console.error("Failed to disable 2FA", error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Two-Factor Authentication</CardTitle>
      </CardHeader>
      <CardContent>
        {show2FASetup ? (
          <TwoFactorSetup
            onComplete={handle2FAComplete}
            onCancel={() => setShow2FASetup(false)}
          />
        ) : (
          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-400">
              {user.twoFactorMethod === TwoFactorMethod.None
                ? "Two-factor authentication is currently disabled for your account."
                : "Add an extra layer of security to your account by requiring a code from your phone whenever you sign in."}
            </p>
            {user.twoFactorEnabled ? (
              <Button onClick={handleDisable2FA} variant="destructive">
                Disable 2FA
              </Button>
            ) : (
              <Button onClick={() => setShow2FASetup(true)}>Enable 2FA</Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
