"use client";

import { type Table } from "@tanstack/react-table";
import { Download } from "lucide-react";

import { type User } from "@/types/user";
import { exportTableToCSV } from "@/lib/data-table/export";
import { Button } from "@/components/ui/button";

import { DeleteUsersDialog } from "./delete-users-dialog";
import { CreateUserDialog } from "./create-user-dialog";

interface UsersTableToolbarActionsProps {
  table: Table<User>;
  onRefresh?: () => void;
}

export function UsersTableToolbarActions({
  table,
  onRefresh,
}: UsersTableToolbarActionsProps) {
  return (
    <div className="flex items-center gap-2">
      {table.getFilteredSelectedRowModel().rows.length > 0 ? (
        <DeleteUsersDialog
          users={table
            .getFilteredSelectedRowModel()
            .rows.map((row) => row.original)}
          onSuccess={() => {
            table.toggleAllRowsSelected(false);
            onRefresh?.();
          }}
        />
      ) : null}
      <CreateUserDialog onSuccess={onRefresh} />
      <Button
        variant="outline"
        size="sm"
        onClick={() =>
          exportTableToCSV(table, {
            filename: "users",
            excludeColumns: ["select", "actions"],
          })
        }
        className="gap-2"
      >
        <Download className="size-4" aria-hidden="true" />
        Export
      </Button>
    </div>
  );
}
