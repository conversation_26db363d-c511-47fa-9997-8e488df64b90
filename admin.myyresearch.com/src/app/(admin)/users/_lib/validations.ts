import {
  createSearchParamsCache,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  parseAsStringEnum,
} from "nuqs/server";
import * as z from "zod";

import { UserRole, UserStatus, type User } from "@/types/user";
import {
  getFiltersStateParser,
  getSortingStateParser,
} from "@/lib/data-table/parsers";

export const searchParamsCache = createSearchParamsCache({
  flags: parseAsArrayOf(z.enum(["advancedTable", "floatingBar"])).withDefault(
    []
  ),
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<User>().withDefault([
    { id: "createdAt", desc: true },
  ]),
  username: parseAsString.withDefault(""),
  email: parseAsString.withDefault(""),
  status: parseAsArrayOf(
    z.enum(Object.values(UserStatus) as [string, ...string[]])
  ).withDefault([]),
  role: parseAsArrayOf(
    z.enum(Object.values(UserRole) as [string, ...string[]])
  ).withDefault([]),
  from: parseAsString.withDefault(""),
  to: parseAsString.withDefault(""),
  // advanced filter
  filters: getFiltersStateParser().withDefault([]),
  joinOperator: parseAsStringEnum(["and", "or"]).withDefault("and"),
});

export const updateUserSchema = z.object({
  username: z.string().optional(),
  email: z.string().email().optional(),
  role: z.enum(Object.values(UserRole) as [string, ...string[]]).optional(),
  status: z.enum(Object.values(UserStatus) as [string, ...string[]]).optional(),
});

export type GetUsersSchema = Awaited<
  ReturnType<typeof searchParamsCache.parse>
>;
export type UpdateUserSchema = z.infer<typeof updateUserSchema>;
