import type { Task } from "@/types/task"
import { TaskLabel, TaskPriority, TaskStatus } from "@/types/task"

export const tasks: Task[] = [
  {
    id: "1",
    code: "TASK-001",
    title: "Fix login page authentication bug",
    status: TaskStatus.IN_PROGRESS,
    label: TaskLabel.BUG,
    priority: TaskPriority.HIGH,
    archived: false,
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-16"),
  },
  {
    id: "2",
    code: "TASK-002",
    title: "Add dark mode support",
    status: TaskStatus.TODO,
    label: TaskLabel.FEATURE,
    priority: TaskPriority.MEDIUM,
    archived: false,
    createdAt: new Date("2024-01-14"),
    updatedAt: null,
  },
  {
    id: "3",
    code: "TASK-003",
    title: "Update API documentation",
    status: TaskStatus.DONE,
    label: TaskLabel.DOCUMENTATION,
    priority: TaskPriority.LOW,
    archived: false,
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-12"),
  },
  {
    id: "4",
    code: "TASK-004",
    title: "Improve search performance",
    status: TaskStatus.IN_PROGRESS,
    label: TaskLabel.ENHANCEMENT,
    priority: TaskPriority.HIGH,
    archived: false,
    createdAt: new Date("2024-01-13"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "5",
    code: "TASK-005",
    title: "Implement user settings page",
    status: TaskStatus.CANCELED,
    label: TaskLabel.FEATURE,
    priority: TaskPriority.MEDIUM,
    archived: true,
    createdAt: new Date("2024-01-08"),
    updatedAt: new Date("2024-01-11"),
  },
  {
    id: "6",
    code: "TASK-006",
    title: "Optimize image loading",
    status: TaskStatus.TODO,
    label: TaskLabel.ENHANCEMENT,
    priority: TaskPriority.HIGH,
    archived: false,
    createdAt: new Date("2024-01-16"),
    updatedAt: null,
  },
  {
    id: "7",
    code: "TASK-007",
    title: "Fix mobile navigation menu",
    status: TaskStatus.IN_PROGRESS,
    label: TaskLabel.BUG,
    priority: TaskPriority.HIGH,
    archived: false,
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-16"),
  },
  {
    id: "8",
    code: "TASK-008",
    title: "Add user analytics dashboard",
    status: TaskStatus.TODO,
    label: TaskLabel.FEATURE,
    priority: TaskPriority.MEDIUM,
    archived: false,
    createdAt: new Date("2024-01-17"),
    updatedAt: null,
  },
  {
    id: "9",
    code: "TASK-009",
    title: "Update privacy policy",
    status: TaskStatus.DONE,
    label: TaskLabel.DOCUMENTATION,
    priority: TaskPriority.LOW,
    archived: false,
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-14"),
  },
  {
    id: "10",
    code: "TASK-010",
    title: "Implement password reset flow",
    status: TaskStatus.IN_PROGRESS,
    label: TaskLabel.FEATURE,
    priority: TaskPriority.HIGH,
    archived: false,
    createdAt: new Date("2024-01-16"),
    updatedAt: new Date("2024-01-17"),
  },
  {
    id: "11",
    code: "TASK-011",
    title: "Fix checkout process errors",
    status: TaskStatus.DONE,
    label: TaskLabel.BUG,
    priority: TaskPriority.HIGH,
    archived: false,
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-13"),
  },
  {
    id: "12",
    code: "TASK-012",
    title: "Add multi-language support",
    status: TaskStatus.CANCELED,
    label: TaskLabel.FEATURE,
    priority: TaskPriority.MEDIUM,
    archived: true,
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-15"),
  },
  {
    id: "13",
    code: "TASK-013",
    title: "Optimize database queries",
    status: TaskStatus.TODO,
    label: TaskLabel.ENHANCEMENT,
    priority: TaskPriority.HIGH,
    archived: false,
    createdAt: new Date("2024-01-17"),
    updatedAt: null,
  },
  {
    id: "14",
    code: "TASK-014",
    title: "Update third-party dependencies",
    status: TaskStatus.IN_PROGRESS,
    label: TaskLabel.DOCUMENTATION,
    priority: TaskPriority.MEDIUM,
    archived: false,
    createdAt: new Date("2024-01-16"),
    updatedAt: new Date("2024-01-17"),
  },
  {
    id: "15",
    code: "TASK-015",
    title: "Create user onboarding guide",
    status: TaskStatus.TODO,
    label: TaskLabel.DOCUMENTATION,
    priority: TaskPriority.LOW,
    archived: false,
    createdAt: new Date("2024-01-17"),
    updatedAt: null,
  },
]
