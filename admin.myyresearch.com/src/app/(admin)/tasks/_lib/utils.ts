import { faker } from "@faker-js/faker";
import {
  ArrowDownIcon,
  ArrowRightIcon,
  ArrowUpIcon,
  CheckCircle2,
  CircleHelp,
  CircleIcon,
  CircleX,
  Timer,
} from "lucide-react";
import { customAlphabet } from "nanoid";

import { TaskLabel, TaskPriority, TaskStatus, type Task } from "@/types/task";
import { generateId } from "@/lib/data-table/id";

export function generateRandomTask(): Task {
  return {
    id: generateId("task"),
    code: `TASK-${customAlphabet("0123456789", 4)()}`,
    title: faker.hacker
      .phrase()
      .replace(/^./, (letter) => letter.toUpperCase()),
    status: faker.helpers.arrayElement(Object.values(TaskStatus)),
    label: faker.helpers.arrayElement(Object.values(TaskLabel)),
    priority: faker.helpers.arrayElement(Object.values(TaskPriority)),
    archived: faker.datatype.boolean({ probability: 0.2 }),
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

/**
 * Returns the appropriate status icon based on the provided status.
 * @param status - The status of the task.
 * @returns A React component representing the status icon.
 */
export function getStatusIcon(status: TaskStatus) {
  const statusIcons = {
    [TaskStatus.CANCELED]: CircleX,
    [TaskStatus.DONE]: CheckCircle2,
    [TaskStatus.IN_PROGRESS]: Timer,
    [TaskStatus.TODO]: CircleHelp,
  };

  return statusIcons[status] || CircleIcon;
}

/**
 * Returns the appropriate priority icon based on the provided priority.
 * @param priority - The priority of the task.
 * @returns A React component representing the priority icon.
 */
export function getPriorityIcon(priority: TaskPriority) {
  const priorityIcons = {
    [TaskPriority.HIGH]: ArrowUpIcon,
    [TaskPriority.LOW]: ArrowDownIcon,
    [TaskPriority.MEDIUM]: ArrowRightIcon,
  };

  return priorityIcons[priority] || CircleIcon;
}
