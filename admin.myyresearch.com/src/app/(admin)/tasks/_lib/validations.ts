import {
  createSearchParamsCache,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  parseAsStringEnum,
} from "nuqs/server";
import * as z from "zod";

import { TaskLabel, TaskPriority, TaskStatus, type Task } from "@/types/task";
import {
  getFiltersStateParser,
  getSortingStateParser,
} from "@/lib/data-table/parsers";

export const searchParamsCache = createSearchParamsCache({
  flags: parseAsArrayOf(z.enum(["advancedTable", "floatingBar"])).withDefault(
    []
  ),
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<Task>().withDefault([
    { id: "createdAt", desc: true },
  ]),
  title: parseAsString.withDefault(""),
  status: parseAsArrayOf(
    z.enum(Object.values(TaskStatus) as [string, ...string[]])
  ).withDefault([]),
  priority: parseAsArrayOf(
    z.enum(Object.values(TaskPriority) as [string, ...string[]])
  ).withDefault([]),
  from: parseAsString.withDefault(""),
  to: parseAsString.withDefault(""),
  // advanced filter
  filters: getFiltersStateParser().withDefault([]),
  joinOperator: parseAsStringEnum(["and", "or"]).withDefault("and"),
});

export const createTaskSchema = z.object({
  title: z.string(),
  label: z.enum(Object.values(TaskLabel) as [string, ...string[]]),
  status: z.enum(Object.values(TaskStatus) as [string, ...string[]]),
  priority: z.enum(Object.values(TaskPriority) as [string, ...string[]]),
});

export const updateTaskSchema = z.object({
  title: z.string().optional(),
  label: z.enum(Object.values(TaskLabel) as [string, ...string[]]).optional(),
  status: z.enum(Object.values(TaskStatus) as [string, ...string[]]).optional(),
  priority: z
    .enum(Object.values(TaskPriority) as [string, ...string[]])
    .optional(),
});

export type GetTasksSchema = Awaited<
  ReturnType<typeof searchParamsCache.parse>
>;
export type CreateTaskSchema = z.infer<typeof createTaskSchema>;
export type UpdateTaskSchema = z.infer<typeof updateTaskSchema>;
