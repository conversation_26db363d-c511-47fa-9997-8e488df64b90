import {
  createSearchParamsCache,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
  parseAsStringEnum,
} from "nuqs/server";
import * as z from "zod";

import { type Newsletter } from "@/types/newsletters";
import {
  getFiltersStateParser,
  getSortingStateParser,
} from "@/lib/data-table/parsers";

export const searchParamsCache = createSearchParamsCache({
  flags: parseAsArrayOf(z.enum(["advancedTable", "floatingBar"])).withDefault(
    []
  ),
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<Newsletter>().withDefault([
    { id: "createdAt", desc: true },
  ]),
  email: parseAsString.withDefault(""),
  isSubscribed: parseAsArrayOf(z.enum(["true", "false"])).withDefault([]),
  from: parseAsString.withDefault(""),
  to: parseAsString.withDefault(""),
  // advanced filter
  filters: getFiltersStateParser().withDefault([]),
  joinOperator: parseAsStringEnum(["and", "or"]).withDefault("and"),
});

export type GetNewslettersSchema = Awaited<
  ReturnType<typeof searchParamsCache.parse>
>;
