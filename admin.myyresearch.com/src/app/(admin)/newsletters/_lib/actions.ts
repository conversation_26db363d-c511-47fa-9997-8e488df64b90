"use server";

import { revalidateTag, unstable_noStore } from "next/cache";
import { getErrorMessage } from "@/lib/data-table/handle-error";
import { deleteNewsletterSubscription } from "@/lib/newsletters";

export async function deleteNewsletter(input: { email: string }) {
  unstable_noStore();
  try {
    const response = await deleteNewsletterSubscription(input.email);

    revalidateTag("newsletters");
    revalidateTag("newsletter-status-counts");

    return {
      data: response.data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function deleteNewsletters(input: { emails: string[] }) {
  unstable_noStore();
  try {
    // Since there's no bulk delete endpoint, we'll delete newsletters one by one
    const deletePromises = input.emails.map((email) =>
      deleteNewsletterSubscription(email)
    );

    const results = await Promise.all(deletePromises);

    revalidateTag("newsletters");
    revalidateTag("newsletter-status-counts");

    return {
      data: results.map((r) => r.data),
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}
