"use server";

import { ApiStatus } from "@/types/common";
import { type Newsletter } from "@/types/newsletters";
import { type GetNewslettersSchema } from "./validations";
import {
  getNewsletters as getNewslettersApi,
  getSubscribedCount,
  getUnsubscribedCount,
} from "@/lib/newsletters";

export async function getNewsletters(input: GetNewslettersSchema): Promise<{
  data: Newsletter[];
  pageCount: number;
}> {
  try {
    const params = {
      search: input.email,
      isSubscribed:
        input.isSubscribed.length > 0
          ? input.isSubscribed[0] === "true"
          : undefined,
      limit: input.perPage || 10,
      page: input.page || 1,
      from: input.from || undefined,
      to: input.to || undefined,
    };
    const response = await getNewslettersApi(params);
    if (response.status === ApiStatus.SUCCESS && response.data) {
      return {
        data: response.data.subscribers,
        pageCount: response.data.totalPages,
      };
    }

    return { data: [], pageCount: 0 };
  } catch {
    return { data: [], pageCount: 0 };
  }
}

export async function getNewsletterStatusCounts(
  input: GetNewslettersSchema
): Promise<Record<"subscribed" | "unsubscribed", number>> {
  try {
    const params = {
      search: input.email,
      from: input.from || undefined,
      to: input.to || undefined,
    };

    const [subscribedResponse, unsubscribedResponse] = await Promise.all([
      getSubscribedCount(params),
      getUnsubscribedCount(params),
    ]);

    if (
      subscribedResponse.status === ApiStatus.SUCCESS &&
      unsubscribedResponse.status === ApiStatus.SUCCESS
    ) {
      return {
        subscribed: subscribedResponse.data.count,
        unsubscribed: unsubscribedResponse.data.count,
      };
    }

    return { subscribed: 0, unsubscribed: 0 };
  } catch {
    return { subscribed: 0, unsubscribed: 0 };
  }
}
