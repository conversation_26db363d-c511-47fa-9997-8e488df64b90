"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
} from "@/types";
import { useRouter } from "next/navigation";

import { type Newsletter } from "@/types/newsletters";
import { useDataTable } from "@/hooks/data-table/use-data-table";
import { DataTable } from "@/components/data-table/data-table";
import { DataTableToolbar } from "@/components/data-table/data-table-toolbar";
import { DataTableAdvancedToolbar } from "@/components/data-table/data-table-advanced-toolbar";

import { getNewsletters, getNewsletterStatusCounts } from "../_lib/queries";
import { getColumns } from "./newsletters-table-columns";
import { NewslettersTableToolbarActions } from "./newsletters-table-toolbar-actions";
import { NewslettersTableFloatingBar } from "./newsletters-table-floating-bar";
import { useFeatureFlags } from "./feature-flags-provider";
import { type DataTableRowAction } from "@/types";
import { DeleteNewslettersDialog } from "./delete-newsletters-dialog";

interface NewslettersTableProps {
  promises: Promise<
    [
      Awaited<ReturnType<typeof getNewsletters>>,
      Awaited<ReturnType<typeof getNewsletterStatusCounts>>
    ]
  >;
}

export function NewslettersTable({ promises }: NewslettersTableProps) {
  const router = useRouter();
  const { featureFlags } = useFeatureFlags();
  const [{ data, pageCount }, statusCounts] = React.use(promises);
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<Newsletter> | null>(null);

  const columns = React.useMemo(
    () => getColumns({ setRowAction }),
    [setRowAction]
  );

  const filterFields: DataTableFilterField<Newsletter>[] = [
    {
      id: "email",
      label: "Email",
      placeholder: "Filter emails...",
    },
    {
      id: "isSubscribed",
      label: "Status",
      options: [
        {
          label: "Subscribed",
          value: "true",
          count: statusCounts.subscribed,
        },
        {
          label: "Unsubscribed",
          value: "false",
          count: statusCounts.unsubscribed,
        },
      ],
    },
  ];

  const advancedFilterFields: DataTableAdvancedFilterField<Newsletter>[] = [
    {
      id: "email",
      label: "Email",
      type: "text",
    },
    {
      id: "isSubscribed",
      label: "Status",
      type: "multi-select",
      options: [
        {
          label: "Subscribed",
          value: "true",
          count: statusCounts.subscribed,
        },
        {
          label: "Unsubscribed",
          value: "false",
          count: statusCounts.unsubscribed,
        },
      ],
    },
    {
      id: "subscribedAt",
      label: "Subscribed at",
      type: "date",
    },
    {
      id: "createdAt",
      label: "Created at",
      type: "date",
    },
  ];

  const enableAdvancedTable = featureFlags.includes("advancedTable");
  const enableFloatingBar = featureFlags.includes("floatingBar");

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    filterFields,
    enableAdvancedFilter: enableAdvancedTable,
    initialState: {
      sorting: [{ id: "createdAt", desc: true }],
      columnPinning: { right: ["actions"] },
    },
    getRowId: (row) => row.email,
  });

  return (
    <>
      <DataTable
        table={table}
        floatingBar={
          enableFloatingBar ? (
            <NewslettersTableFloatingBar table={table} />
          ) : null
        }
      >
        {enableAdvancedTable ? (
          <DataTableAdvancedToolbar
            table={table}
            filterFields={advancedFilterFields}
            shallow={false}
          >
            <NewslettersTableToolbarActions
              table={table}
              onRefresh={() => router.refresh()}
            />
          </DataTableAdvancedToolbar>
        ) : (
          <DataTableToolbar table={table} filterFields={filterFields}>
            <NewslettersTableToolbarActions
              table={table}
              onRefresh={() => router.refresh()}
            />
          </DataTableToolbar>
        )}
      </DataTable>
      <DeleteNewslettersDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        newsletters={rowAction?.row ? [rowAction.row.original] : []}
        showTrigger={false}
        onSuccess={() => {
          rowAction?.row?.toggleSelected(false);
          router.refresh();
        }}
      />
    </>
  );
}
