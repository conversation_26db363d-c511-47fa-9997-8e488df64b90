"use server";

import { ApiResponse } from "@/types/common";
import {
  User,
  UpdateProfileRequest,
  AdminUpdateUserRequest,
  UsersQueryParams,
  PaginatedUsersResponse,
  DeleteUserResponse,
  DeleteManyUsersRequest,
  DeleteManyUsersResponse,
  UserStatusCounts,
  UserRoleCounts,
} from "@/types/user";
import axios from "@/utils/axios";

export async function getUserProfile(): Promise<ApiResponse<User>> {
  try {
    const res = await axios.get("/users/profile");
    return res.data;
  } catch (error) {
    return error as ApiResponse<User>;
  }
}

export async function updateProfile(
  data: UpdateProfileRequest
): Promise<ApiResponse<User>> {
  try {
    const res = await axios.post("/users/update-profile", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<User>;
  }
}

export async function adminUpdateUser(
  data: AdminUpdateUserRequest
): Promise<ApiResponse<User>> {
  try {
    const res = await axios.post("/users/admin/update-user", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<User>;
  }
}

export async function deleteUser(
  userId: string
): Promise<ApiResponse<DeleteUserResponse>> {
  try {
    const res = await axios.delete(`/users/${userId}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<DeleteUserResponse>;
  }
}

export async function deleteManyUsers(
  data: DeleteManyUsersRequest
): Promise<ApiResponse<DeleteManyUsersResponse>> {
  try {
    const res = await axios.post("/users/delete-many", data);
    return res.data;
  } catch (error) {
    return error as ApiResponse<DeleteManyUsersResponse>;
  }
}

export async function getUsers(
  params: UsersQueryParams
): Promise<ApiResponse<PaginatedUsersResponse>> {
  try {
    const res = await axios.get("/users", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaginatedUsersResponse>;
  }
}

export async function getUserStatusCounts(
  params: UsersQueryParams
): Promise<ApiResponse<UserStatusCounts>> {
  try {
    const res = await axios.get("/users/status-counts", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<UserStatusCounts>;
  }
}

export async function getUserRoleCounts(
  params: UsersQueryParams
): Promise<ApiResponse<UserRoleCounts>> {
  try {
    const res = await axios.get("/users/role-counts", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<UserRoleCounts>;
  }
}
