import { ApiResponse } from "@/types/common";
import {
  NewsletterQueryParams,
  PaginatedNewslettersResponse,
  NewsletterCountResponse,
  NewsletterDeleteResponse,
} from "@/types/newsletters";
import axios from "@/utils/axios";

export async function getNewsletters(
  params: NewsletterQueryParams
): Promise<ApiResponse<PaginatedNewslettersResponse>> {
  try {
    const res = await axios.get("/newsletters", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<PaginatedNewslettersResponse>;
  }
}

export async function deleteNewsletterSubscription(
  email: string
): Promise<ApiResponse<NewsletterDeleteResponse>> {
  try {
    const res = await axios.delete(`/newsletters/delete/${email}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<NewsletterDeleteResponse>;
  }
}

export async function getSubscribedCount(
  params: NewsletterQueryParams
): Promise<ApiResponse<NewsletterCountResponse>> {
  try {
    const res = await axios.get("/newsletters/count/subscribed", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<NewsletterCountResponse>;
  }
}

export async function getUnsubscribedCount(
  params: NewsletterQueryParams
): Promise<ApiResponse<NewsletterCountResponse>> {
  try {
    const res = await axios.get("/newsletters/count/unsubscribed", { params });
    return res.data;
  } catch (error) {
    return error as ApiResponse<NewsletterCountResponse>;
  }
}
